#include "world_wrapper.h"
#include <algorithm>
#include <cmath>
#include <numeric>

namespace WorldWrapper
{

    // Predefined voice morph profiles
    const VoiceMorphProfile PredefinedProfiles::SECURE_DEEP_MALE = {
        0.75f, // pitchScale - Lower pitch
        -8.0f, // spectralWarp - Formant shift down
        20.0f, // reverbAmount - Moderate reverb
        -3.0f, // eqTilt - Emphasize low frequencies
        0.05f, // temporalJitter - Anti-forensic timing
        0.15f, // spectralNoise - Irreversible masking
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::SECURE_HIGH_FEMALE = {
        1.25f, // pitchScale - Higher pitch
        6.0f,  // spectralWarp - Formant shift up
        15.0f, // reverbAmount - Light reverb
        2.0f,  // eqTilt - Emphasize high frequencies
        0.03f, // temporalJitter
        0.12f, // spectralNoise
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::ROBOTIC_SYNTHETIC = {
        0.9f,   // pitchScale - Slightly lower
        -15.0f, // spectralWarp - Heavy formant shift
        35.0f,  // reverbAmount - Heavy reverb
        -6.0f,  // eqTilt - Strong low emphasis
        0.1f,   // temporalJitter - High jitter
        0.25f,  // spectralNoise - Heavy masking
        true    // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::WHISPER_SOFT = {
        1.1f,  // pitchScale - Slightly higher
        3.0f,  // spectralWarp - Light formant shift
        8.0f,  // reverbAmount - Minimal reverb
        1.0f,  // eqTilt - Slight high emphasis
        0.02f, // temporalJitter - Minimal jitter
        0.08f, // spectralNoise - Light masking
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::DRAMATIC_BASS = {
        0.65f,  // pitchScale - Very low pitch
        -12.0f, // spectralWarp - Strong formant down
        25.0f,  // reverbAmount - Strong reverb
        -4.0f,  // eqTilt - Heavy low emphasis
        0.06f,  // temporalJitter
        0.18f,  // spectralNoise
        true    // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::ETHEREAL_HIGH = {
        1.4f,  // pitchScale - Very high pitch
        10.0f, // spectralWarp - Strong formant up
        30.0f, // reverbAmount - Heavy reverb
        3.0f,  // eqTilt - Strong high emphasis
        0.04f, // temporalJitter
        0.14f, // spectralNoise
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::MECHANICAL_DRONE = {
        0.8f,   // pitchScale
        -10.0f, // spectralWarp
        40.0f,  // reverbAmount - Very heavy reverb
        -5.0f,  // eqTilt
        0.12f,  // temporalJitter - High jitter
        0.3f,   // spectralNoise - Very heavy masking
        true    // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::WARM_TENOR = {
        0.95f, // pitchScale - Slightly lower
        -2.0f, // spectralWarp - Slight formant down
        12.0f, // reverbAmount - Light reverb
        -1.0f, // eqTilt - Slight low emphasis
        0.03f, // temporalJitter
        0.1f,  // spectralNoise
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::CRYSTAL_SOPRANO = {
        1.3f,   // pitchScale - High pitch
        8.0f,   // spectralWarp - Strong formant up
        18.0f,  // reverbAmount
        4.0f,   // eqTilt - Strong high emphasis
        0.035f, // temporalJitter
        0.13f,  // spectralNoise
        true    // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::DARK_BARITONE = {
        0.7f,   // pitchScale - Low pitch
        -6.0f,  // spectralWarp
        22.0f,  // reverbAmount
        -2.5f,  // eqTilt
        0.045f, // temporalJitter
        0.16f,  // spectralNoise
        true    // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::BRIGHT_ALTO = {
        1.15f,  // pitchScale
        4.0f,   // spectralWarp
        14.0f,  // reverbAmount
        2.5f,   // eqTilt
        0.025f, // temporalJitter
        0.11f,  // spectralNoise
        true    // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::MYSTERIOUS_ECHO = {
        0.85f, // pitchScale
        -5.0f, // spectralWarp
        45.0f, // reverbAmount - Very heavy reverb
        -1.5f, // eqTilt
        0.08f, // temporalJitter
        0.2f,  // spectralNoise
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::ENERGETIC_YOUNG = {
        1.2f,  // pitchScale - Higher pitch
        5.0f,  // spectralWarp
        10.0f, // reverbAmount - Light reverb
        1.5f,  // eqTilt
        0.02f, // temporalJitter - Low jitter
        0.09f, // spectralNoise
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::WISE_ELDER = {
        0.8f,  // pitchScale - Lower pitch
        -4.0f, // spectralWarp
        16.0f, // reverbAmount
        -1.0f, // eqTilt
        0.04f, // temporalJitter
        0.12f, // spectralNoise
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::DIGITAL_GLITCH = {
        1.0f,  // pitchScale - No pitch change
        0.0f,  // spectralWarp - No formant shift
        50.0f, // reverbAmount - Maximum reverb
        0.0f,  // eqTilt - No EQ
        0.15f, // temporalJitter - Maximum jitter
        0.35f, // spectralNoise - Maximum masking
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::SMOOTH_RADIO = {
        0.9f,   // pitchScale
        -3.0f,  // spectralWarp
        8.0f,   // reverbAmount - Minimal reverb
        -0.5f,  // eqTilt
        0.015f, // temporalJitter - Very low jitter
        0.07f,  // spectralNoise - Light masking
        true    // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::INTENSE_GROWL = {
        0.6f,   // pitchScale - Very low pitch
        -18.0f, // spectralWarp - Extreme formant down
        28.0f,  // reverbAmount
        -5.5f,  // eqTilt - Extreme low emphasis
        0.07f,  // temporalJitter
        0.22f,  // spectralNoise
        true    // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::GENTLE_BREEZE = {
        1.05f, // pitchScale - Slightly higher
        2.0f,  // spectralWarp - Light formant up
        6.0f,  // reverbAmount - Very light reverb
        0.5f,  // eqTilt - Very light high emphasis
        0.01f, // temporalJitter - Minimal jitter
        0.05f, // spectralNoise - Minimal masking
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::POWERFUL_BOOM = {
        0.7f,  // pitchScale - Low pitch
        -8.0f, // spectralWarp
        32.0f, // reverbAmount - Heavy reverb
        -4.0f, // eqTilt - Heavy low emphasis
        0.06f, // temporalJitter
        0.19f, // spectralNoise
        true   // antiForensic
    };

    const VoiceMorphProfile PredefinedProfiles::SUBTLE_SHIFT = {
        0.98f,  // pitchScale - Very slight change
        -1.0f,  // spectralWarp - Minimal formant shift
        5.0f,   // reverbAmount - Very light reverb
        -0.2f,  // eqTilt - Minimal EQ
        0.008f, // temporalJitter - Very low jitter
        0.04f,  // spectralNoise - Minimal masking
        true    // antiForensic
    };

    // Normal voice profile (only for regular users)
    const VoiceMorphProfile PredefinedProfiles::NORMAL_VOICE = {
        1.0f, // pitchScale - No change
        0.0f, // spectralWarp - No change
        0.0f, // reverbAmount - No reverb
        0.0f, // eqTilt - No EQ
        0.0f, // temporalJitter - No jitter
        0.0f, // spectralNoise - No masking
        false // antiForensic - Disabled
    };

    // Static member initialization
    std::map<std::string, VoiceMorphProfile> VoiceProcessorManager::profileCache_;
    std::once_flag VoiceProcessorManager::profileCacheInitialized_;

    VoiceProcessorManager::VoiceProcessorManager()
    {
        std::call_once(profileCacheInitialized_, &VoiceProcessorManager::InitializeProfileCache);
    }

    VoiceProcessorManager::~VoiceProcessorManager()
    {
        std::lock_guard<std::mutex> lock(processorsMutex_);
        processors_.clear();
    }

    void VoiceProcessorManager::InitializeProfileCache()
    {
        profileCache_["SECURE_DEEP_MALE"] = PredefinedProfiles::SECURE_DEEP_MALE;
        profileCache_["SECURE_HIGH_FEMALE"] = PredefinedProfiles::SECURE_HIGH_FEMALE;
        profileCache_["ROBOTIC_SYNTHETIC"] = PredefinedProfiles::ROBOTIC_SYNTHETIC;
        profileCache_["WHISPER_SOFT"] = PredefinedProfiles::WHISPER_SOFT;
        profileCache_["DRAMATIC_BASS"] = PredefinedProfiles::DRAMATIC_BASS;
        profileCache_["ETHEREAL_HIGH"] = PredefinedProfiles::ETHEREAL_HIGH;
        profileCache_["MECHANICAL_DRONE"] = PredefinedProfiles::MECHANICAL_DRONE;
        profileCache_["WARM_TENOR"] = PredefinedProfiles::WARM_TENOR;
        profileCache_["CRYSTAL_SOPRANO"] = PredefinedProfiles::CRYSTAL_SOPRANO;
        profileCache_["DARK_BARITONE"] = PredefinedProfiles::DARK_BARITONE;
        profileCache_["BRIGHT_ALTO"] = PredefinedProfiles::BRIGHT_ALTO;
        profileCache_["MYSTERIOUS_ECHO"] = PredefinedProfiles::MYSTERIOUS_ECHO;
        profileCache_["ENERGETIC_YOUNG"] = PredefinedProfiles::ENERGETIC_YOUNG;
        profileCache_["WISE_ELDER"] = PredefinedProfiles::WISE_ELDER;
        profileCache_["DIGITAL_GLITCH"] = PredefinedProfiles::DIGITAL_GLITCH;
        profileCache_["SMOOTH_RADIO"] = PredefinedProfiles::SMOOTH_RADIO;
        profileCache_["INTENSE_GROWL"] = PredefinedProfiles::INTENSE_GROWL;
        profileCache_["GENTLE_BREEZE"] = PredefinedProfiles::GENTLE_BREEZE;
        profileCache_["POWERFUL_BOOM"] = PredefinedProfiles::POWERFUL_BOOM;
        profileCache_["SUBTLE_SHIFT"] = PredefinedProfiles::SUBTLE_SHIFT;
        profileCache_["NORMAL_VOICE"] = PredefinedProfiles::NORMAL_VOICE;
    }

    bool VoiceProcessorManager::CreateSession(const std::string &sessionId, int sampleRate,
                                              int frameSize, QualityLevel quality)
    {
        std::lock_guard<std::mutex> lock(processorsMutex_);

        if (processors_.find(sessionId) != processors_.end())
        {
            return false; // Session already exists
        }

        try
        {
            bool realTimeMode = (quality != QualityLevel::HIGH_QUALITY);
            auto processor = std::make_unique<WorldProcessor>(sampleRate, frameSize, realTimeMode);
            processors_[sessionId] = std::move(processor);
            sessionLatencies_[sessionId] = 0.0;
            return true;
        }
        catch (const std::exception &)
        {
            return false;
        }
    }

    std::vector<float> VoiceProcessorManager::ProcessAudioFrame(const std::string &sessionId,
                                                                const float *audioData, size_t audioLength,
                                                                const std::string &profileName)
    {
        VoiceMorphProfile profile = GetPredefinedProfile(profileName);
        return ProcessAudioFrameCustom(sessionId, audioData, audioLength, profile);
    }

    std::vector<float> VoiceProcessorManager::ProcessAudioFrameCustom(const std::string &sessionId,
                                                                      const float *audioData, size_t audioLength,
                                                                      const VoiceMorphProfile &customProfile)
    {
        std::lock_guard<std::mutex> lock(processorsMutex_);

        auto it = processors_.find(sessionId);
        if (it == processors_.end())
        {
            // Return original audio if session not found
            return std::vector<float>(audioData, audioData + audioLength);
        }

        try
        {
            std::vector<float> result = it->second->ProcessFrame(audioData, audioLength, customProfile);
            sessionLatencies_[sessionId] = it->second->GetProcessingLatency();
            return result;
        }
        catch (const std::exception &)
        {
            // Return original audio on error
            return std::vector<float>(audioData, audioData + audioLength);
        }
    }

    VoiceMorphProfile VoiceProcessorManager::GetPredefinedProfile(const std::string &profileName)
    {
        auto it = profileCache_.find(profileName);
        if (it != profileCache_.end())
        {
            return it->second;
        }

        // Return default profile if not found
        return PredefinedProfiles::SECURE_DEEP_MALE;
    }

    std::vector<std::string> VoiceProcessorManager::GetAvailableProfiles()
    {
        std::vector<std::string> profiles;
        for (const auto &pair : profileCache_)
        {
            profiles.push_back(pair.first);
        }
        return profiles;
    }

    void VoiceProcessorManager::DestroySession(const std::string &sessionId)
    {
        std::lock_guard<std::mutex> lock(processorsMutex_);
        processors_.erase(sessionId);
        sessionLatencies_.erase(sessionId);
    }

    double VoiceProcessorManager::GetSessionLatency(const std::string &sessionId)
    {
        std::lock_guard<std::mutex> lock(processorsMutex_);
        auto it = sessionLatencies_.find(sessionId);
        return (it != sessionLatencies_.end()) ? it->second : 0.0;
    }

    size_t VoiceProcessorManager::GetActiveSessionCount()
    {
        std::lock_guard<std::mutex> lock(processorsMutex_);
        return processors_.size();
    }

    // Utility functions
    namespace Utils
    {

        bool ValidateAudioParameters(int sampleRate, int frameSize)
        {
            // Common sample rates: 8000, 16000, 22050, 44100, 48000
            if (sampleRate < 8000 || sampleRate > 96000)
            {
                return false;
            }

            // Frame size should be reasonable for real-time processing
            if (frameSize < 64 || frameSize > 4096)
            {
                return false;
            }

            // Frame size should be power of 2 for optimal FFT performance
            return (frameSize & (frameSize - 1)) == 0;
        }

        int CalculateOptimalFrameSize(int sampleRate, double targetLatencyMs)
        {
            int targetSamples = static_cast<int>(sampleRate * targetLatencyMs / 1000.0);

            // Find nearest power of 2
            int frameSize = 64;
            while (frameSize < targetSamples && frameSize < 4096)
            {
                frameSize *= 2;
            }

            return frameSize;
        }

        void NormalizeAudio(std::vector<float> &audioData, float targetLevel)
        {
            if (audioData.empty())
                return;

            // Calculate RMS
            float rms = 0.0f;
            for (float sample : audioData)
            {
                rms += sample * sample;
            }
            rms = std::sqrt(rms / audioData.size());

            if (rms > 0.0f)
            {
                float gain = targetLevel / rms;
                for (float &sample : audioData)
                {
                    sample *= gain;
                    // Prevent clipping
                    sample = std::max(-1.0f, std::min(1.0f, sample));
                }
            }
        }

        void ApplyFadeInOut(std::vector<float> &audioData, int fadeLength)
        {
            if (audioData.size() < static_cast<size_t>(fadeLength * 2))
            {
                return; // Audio too short for fade
            }

            // Fade in
            for (int i = 0; i < fadeLength; i++)
            {
                float factor = static_cast<float>(i) / fadeLength;
                audioData[i] *= factor;
            }

            // Fade out
            int startFadeOut = static_cast<int>(audioData.size()) - fadeLength;
            for (int i = 0; i < fadeLength; i++)
            {
                float factor = static_cast<float>(fadeLength - i) / fadeLength;
                audioData[startFadeOut + i] *= factor;
            }
        }

        bool IsProfileAllowedForUser(const std::string &profileName, bool isSuperuser)
        {
            // NORMAL_VOICE is only allowed for regular users, not superusers
            if (profileName == "NORMAL_VOICE")
            {
                return !isSuperuser;
            }

            // All other profiles are allowed for all users
            return true;
        }

    } // namespace Utils

} // namespace WorldWrapper
