#ifndef WORLD_H_
#define WORLD_H_

// Include all WORLD vocoder headers
#include "../../lib/world/src/world/cheaptrick.h"
#include "../../lib/world/src/world/common.h"
#include "../../lib/world/src/world/d4c.h"
#include "../../lib/world/src/world/dio.h"
#include "../../lib/world/src/world/fft.h"
#include "../../lib/world/src/world/harvest.h"
#include "../../lib/world/src/world/matlabfunctions.h"
#include "../../lib/world/src/world/stonemask.h"
#include "../../lib/world/src/world/synthesis.h"
#include "../../lib/world/src/world/synthesisrealtime.h"
#include "../../lib/world/src/world/constantnumbers.h"
#include "../../lib/world/src/world/macrodefinitions.h"

#endif  // WORLD_H_
