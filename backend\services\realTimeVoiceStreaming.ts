/**
 * Real-time Voice Streaming Service with WORLD Vocoder Integration
 * Handles WebRTC signaling and voice morphing for secure voice calls
 * Designed for <100ms end-to-end latency with non-reversible voice transformation
 */

import { EventEmitter } from 'events';
import * as crypto from 'crypto';
import { WebSocket } from 'ws';
import { worldVocoderService, WorldVoiceProfile, WORLD_VOICE_PROFILES, ProcessingStats } from './worldVocoderService';
// ParticipantStats interface for call statistics
interface ParticipantStats {
  userId: string;
  connected: boolean;
  profile: string;
  vocoderStats: ProcessingStats | null;
}
import { VoiceModulationService } from './voiceModulation';
import { voiceCallRecordingService, AudioFrame as RecordingAudioFrame } from './voiceCallRecording';

export interface VoiceCallSession {
  callId: string;
  initiatorId: string;
  recipientId: string;
  status: 'initiating' | 'ringing' | 'connected' | 'ended';
  startTime: Date;
  endTime?: Date;
  recordingPath?: string;
  morphProfiles: {
    initiator: string;
    recipient: string;
  };
  webrtcSessions: {
    initiator?: WebRTCSession;
    recipient?: WebRTCSession;
  };
}

export interface WebRTCSession {
  userId: string;
  sessionId: string;
  websocket: WebSocket;
  voiceProcessorSessionId?: string;
  audioBuffer: Buffer[];
  lastActivity: Date;
  isConnected: boolean;
}

export interface AudioFrame {
  sessionId: string;
  userId: string;
  audioData: Float32Array;
  timestamp: number;
  sequenceNumber: number;
}

export interface VoiceStreamingConfig {
  frameSize: number;          // Audio frame size (960 samples for 20ms at 48kHz)
  sampleRate: number;         // Audio sample rate (48000)
  bufferSize: number;         // Buffer size for audio processing
  maxLatency: number;         // Maximum acceptable latency in ms
  recordingEnabled: boolean;  // Enable call recording
}

/**
 * Real-time Voice Streaming Service
 * Manages WebRTC voice calls with real-time voice morphing
 */
export class RealTimeVoiceStreamingService extends EventEmitter {
  private activeCalls: Map<string, VoiceCallSession> = new Map();
  private webrtcSessions: Map<string, WebRTCSession> = new Map();
  private voiceModulation: VoiceModulationService;
  private config: VoiceStreamingConfig;
  private recordingStreams: Map<string, NodeJS.WritableStream> = new Map();

  constructor() {
    super();

    this.voiceModulation = new VoiceModulationService();
    this.config = {
      frameSize: 960,        // 20ms at 48kHz
      sampleRate: 48000,
      bufferSize: 4096,
      maxLatency: 100,       // 100ms max latency
      recordingEnabled: true
    };

    // Cleanup inactive sessions periodically
    setInterval(() => this.cleanupInactiveSessions(), 30000);
  }

  /**
   * Initiate a voice call between two users
   */
  async initiateCall(initiatorId: string, recipientId: string,
    initiatorProfile: string, recipientProfile: string): Promise<string> {
    const callId = crypto.randomUUID();

    const session: VoiceCallSession = {
      callId,
      initiatorId,
      recipientId,
      status: 'initiating',
      startTime: new Date(),
      morphProfiles: {
        initiator: initiatorProfile,
        recipient: recipientProfile
      },
      webrtcSessions: {}
    };

    this.activeCalls.set(callId, session);

    // Start recording if enabled
    if (this.config.recordingEnabled) {
      await voiceCallRecordingService.startRecording(callId, initiatorId, recipientId);
    }

    this.emit('callInitiated', {
      callId,
      initiatorId,
      recipientId,
      timestamp: new Date()
    });

    return callId;
  }

  /**
   * Handle WebRTC session connection
   */
  async connectWebRTCSession(callId: string, userId: string, websocket: WebSocket): Promise<boolean> {
    const call = this.activeCalls.get(callId);
    if (!call) {
      throw new Error(`Call ${callId} not found`);
    }

    // Create WebRTC session
    const sessionId = crypto.randomUUID();
    const webrtcSession: WebRTCSession = {
      userId,
      sessionId,
      websocket,
      audioBuffer: [],
      lastActivity: new Date(),
      isConnected: true
    };

    // Initialize voice processor session for this user
    try {
      const voiceProcessorSessionId = await worldVocoderService.createSession(userId, {
        sampleRate: this.config.sampleRate,
        frameSize: this.config.frameSize,
        realTimeMode: true,
        qualityLevel: 'balanced'
      });

      webrtcSession.voiceProcessorSessionId = voiceProcessorSessionId;
    } catch (error) {
      console.warn('Failed to create WORLD vocoder session, falling back to SoX:', error);
    }

    // Store session
    this.webrtcSessions.set(sessionId, webrtcSession);

    // Associate with call
    if (userId === call.initiatorId) {
      call.webrtcSessions.initiator = webrtcSession;
    } else if (userId === call.recipientId) {
      call.webrtcSessions.recipient = webrtcSession;
    }

    // Set up WebSocket message handling
    this.setupWebSocketHandlers(webrtcSession);

    // Check if both users are connected
    if (call.webrtcSessions.initiator && call.webrtcSessions.recipient) {
      call.status = 'connected';
      this.emit('callConnected', { callId, timestamp: new Date() });
    }

    return true;
  }

  /**
   * Process incoming audio frame with voice morphing
   */
  async processAudioFrame(sessionId: string, audioData: Float32Array): Promise<Float32Array | null> {
    const session = this.webrtcSessions.get(sessionId);
    if (!session) {
      return null;
    }

    // Find the call this session belongs to
    const call = this.findCallBySession(session);
    if (!call) {
      return null;
    }

    // Get the appropriate voice profile
    const isInitiator = session.userId === call.initiatorId;
    const profileName = isInitiator ? call.morphProfiles.initiator : call.morphProfiles.recipient;

    try {
      let morphedAudio: Float32Array | null = null;

      // Try WORLD vocoder first if available
      if (session.voiceProcessorSessionId && worldVocoderService.isAvailable()) {
        const profile = WORLD_VOICE_PROFILES[profileName] || WORLD_VOICE_PROFILES.SECURE_DEEP_MALE;
        morphedAudio = await worldVocoderService.processAudioFrame(
          session.voiceProcessorSessionId,
          audioData,
          profile
        );
      }

      // Fallback to existing voice modulation system
      if (!morphedAudio) {
        const audioBuffer = Buffer.from(audioData.buffer);
        const profile = this.voiceModulation.getProfile(profileName);
        const morphedBuffer = await this.voiceModulation.modulateVoice(audioBuffer, profile, session.userId);

        // Convert back to Float32Array
        morphedAudio = new Float32Array(morphedBuffer.buffer);
      }

      // Record the morphed audio
      if (this.config.recordingEnabled && morphedAudio) {
        const recordingFrame: RecordingAudioFrame = {
          userId: session.userId,
          audioData: morphedAudio,
          timestamp: Date.now(),
          morphProfile: profileName
        };
        await voiceCallRecordingService.recordAudioFrame(call.callId, recordingFrame);
      }

      // Update session activity
      session.lastActivity = new Date();

      return morphedAudio;
    } catch (error) {
      console.error('Audio processing error:', error);
      this.emit('processingError', { sessionId, error });
      return null;
    }
  }

  /**
   * Forward processed audio to the other participant
   */
  async forwardAudioFrame(senderSessionId: string, morphedAudio: Float32Array): Promise<void> {
    const senderSession = this.webrtcSessions.get(senderSessionId);
    if (!senderSession) {
      return;
    }

    const call = this.findCallBySession(senderSession);
    if (!call || call.status !== 'connected') {
      return;
    }

    // Find the recipient session
    const recipientSession = senderSession.userId === call.initiatorId
      ? call.webrtcSessions.recipient
      : call.webrtcSessions.initiator;

    if (!recipientSession || !recipientSession.isConnected) {
      return;
    }

    // Send morphed audio to recipient
    try {
      const audioMessage = {
        type: 'audio_frame',
        sessionId: senderSessionId,
        audioData: Array.from(morphedAudio),
        timestamp: Date.now()
      };

      recipientSession.websocket.send(JSON.stringify(audioMessage));
    } catch (error) {
      console.error('Error forwarding audio frame:', error);
      this.emit('forwardingError', { senderSessionId, error });
    }
  }

  /**
   * End a voice call
   */
  async endCall(callId: string, endedBy: string): Promise<void> {
    const call = this.activeCalls.get(callId);
    if (!call) {
      return;
    }

    call.status = 'ended';
    call.endTime = new Date();

    // Cleanup WebRTC sessions
    if (call.webrtcSessions.initiator) {
      await this.cleanupWebRTCSession(call.webrtcSessions.initiator);
    }
    if (call.webrtcSessions.recipient) {
      await this.cleanupWebRTCSession(call.webrtcSessions.recipient);
    }

    // Stop recording
    if (this.config.recordingEnabled) {
      await voiceCallRecordingService.stopRecording(callId);
    }

    this.activeCalls.delete(callId);

    this.emit('callEnded', {
      callId,
      endedBy,
      duration: call.endTime.getTime() - call.startTime.getTime(),
      timestamp: new Date()
    });
  }

  /**
   * Get active call statistics
   */
  getCallStats(callId: string): any {
    const call = this.activeCalls.get(callId);
    if (!call) {
      return null;
    }

    const stats = {
      callId,
      status: call.status,
      duration: Date.now() - call.startTime.getTime(),
      participants: {
        initiator: {
          userId: call.initiatorId,
          connected: !!call.webrtcSessions.initiator?.isConnected,
          profile: call.morphProfiles.initiator,
          vocoderStats: null
        } as ParticipantStats,
        recipient: {
          userId: call.recipientId,
          connected: !!call.webrtcSessions.recipient?.isConnected,
          profile: call.morphProfiles.recipient,
          vocoderStats: null
        } as ParticipantStats
      }
    };

    // Add WORLD vocoder stats if available
    if (call.webrtcSessions.initiator?.voiceProcessorSessionId) {
      stats.participants.initiator.vocoderStats =
        worldVocoderService.getSessionStats(call.webrtcSessions.initiator.voiceProcessorSessionId);
    }
    if (call.webrtcSessions.recipient?.voiceProcessorSessionId) {
      stats.participants.recipient.vocoderStats =
        worldVocoderService.getSessionStats(call.webrtcSessions.recipient.voiceProcessorSessionId);
    }

    return stats;
  }

  /**
   * Get all active calls
   */
  getActiveCalls(): string[] {
    return Array.from(this.activeCalls.keys());
  }

  private setupWebSocketHandlers(session: WebRTCSession): void {
    session.websocket.on('message', async (data) => {
      try {
        const message = JSON.parse(data.toString());

        switch (message.type) {
          case 'audio_frame':
            const audioData = new Float32Array(message.audioData);
            const morphedAudio = await this.processAudioFrame(session.sessionId, audioData);
            if (morphedAudio) {
              await this.forwardAudioFrame(session.sessionId, morphedAudio);
            }
            break;

          case 'ping':
            session.websocket.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
            break;
        }
      } catch (error) {
        console.error('WebSocket message handling error:', error);
      }
    });

    session.websocket.on('close', () => {
      session.isConnected = false;
      this.handleSessionDisconnect(session);
    });

    session.websocket.on('error', (error) => {
      console.error('WebSocket error:', error);
      session.isConnected = false;
      this.handleSessionDisconnect(session);
    });
  }

  private findCallBySession(session: WebRTCSession): VoiceCallSession | null {
    for (const call of this.activeCalls.values()) {
      if (call.webrtcSessions.initiator?.sessionId === session.sessionId ||
        call.webrtcSessions.recipient?.sessionId === session.sessionId) {
        return call;
      }
    }
    return null;
  }

  private async cleanupWebRTCSession(session: WebRTCSession): Promise<void> {
    if (session.voiceProcessorSessionId) {
      worldVocoderService.destroySession(session.voiceProcessorSessionId);
    }

    if (session.websocket.readyState === WebSocket.OPEN) {
      session.websocket.close();
    }

    this.webrtcSessions.delete(session.sessionId);
  }

  private handleSessionDisconnect(session: WebRTCSession): void {
    const call = this.findCallBySession(session);
    if (call) {
      this.endCall(call.callId, session.userId);
    }
  }

  private cleanupInactiveSessions(): void {
    const now = new Date();
    const timeout = 5 * 60 * 1000; // 5 minutes

    for (const [sessionId, session] of this.webrtcSessions.entries()) {
      if (now.getTime() - session.lastActivity.getTime() > timeout) {
        console.log(`Cleaning up inactive session: ${sessionId}`);
        this.cleanupWebRTCSession(session);
      }
    }
  }


}

// Export singleton instance
export const realTimeVoiceStreamingService = new RealTimeVoiceStreamingService();
