@echo off
REM CCALC App Metro Resolution Fix Script
REM Fixes the "Unable to resolve module ./app/index" error

echo 🔧 CCALC Metro Resolution Fix
echo ==============================

REM Change to app directory
cd /d "%~dp0"
echo 📁 Working directory: %CD%

REM Step 1: Clear all caches
echo.
echo 🧹 Step 1: Clearing all caches...
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache"
if exist ".expo" rmdir /s /q ".expo"
if exist "dist" rmdir /s /q "dist"

REM Step 2: Verify file structure
echo.
echo 📋 Step 2: Verifying file structure...
if not exist "index.js" (
    echo ❌ ERROR: index.js not found in app directory
    pause
    exit /b 1
)

if not exist "package.json" (
    echo ❌ ERROR: package.json not found in app directory
    pause
    exit /b 1
)

if not exist "src\App.tsx" (
    echo ❌ ERROR: src\App.tsx not found
    pause
    exit /b 1
)

echo ✅ All required files exist

REM Step 3: Clear Metro cache specifically
echo.
echo 🚇 Step 3: Clearing Metro cache...
npx expo start --clear --no-open >nul 2>&1

REM Step 4: Check Metro config
echo.
echo ⚙️  Step 4: Verifying Metro configuration...
if exist "metro.config.js" (
    echo ✅ Metro config exists
) else (
    echo ❌ Metro config missing
)

REM Step 5: Install missing dependencies
echo.
echo 📚 Step 5: Checking dependencies...
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
)

REM Step 6: Final verification
echo.
echo ✅ Step 6: Final verification...
echo    📂 Current directory: %CD%
if exist "index.js" (
    echo    🗂️  Index.js exists: YES
) else (
    echo    🗂️  Index.js exists: NO
)

echo.
echo 🚀 Fix completed! To start the app:
echo    1. Make sure you're in the app\ directory
echo    2. Run: npm run start:clear
echo    3. Or run: npx expo start --clear
echo.
echo 🔍 If the issue persists:
echo    - Delete node_modules: rmdir /s /q node_modules ^&^& npm install
echo    - Clear all Expo cache: rmdir /s /q "%USERPROFILE%\.expo"
echo    - Restart your terminal and try again

pause
