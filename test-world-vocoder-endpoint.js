/**
 * Simple test to trigger WORLD vocoder processing via direct endpoint
 * This will help us see the actual processing results with our fixes
 */

const fs = require('fs');
const fetch = require('node-fetch');
const FormData = require('form-data');

async function testWorldVocoderEndpoint() {
  try {
    console.log('🎵 Testing WORLD Vocoder via Voice Processing Endpoint');
    console.log('==================================================');
    
    // Test if we have the input file
    const inputFile = './test-input.wav';
    if (!fs.existsSync(inputFile)) {
      console.error('❌ Test input file not found:', inputFile);
      return;
    }
    
    const inputStats = fs.statSync(inputFile);
    console.log(`📁 Input file: ${inputFile}`);
    console.log(`📏 Input size: ${inputStats.size} bytes`);
    
    // Create form data
    const formData = new FormData();
    formData.append('audio', fs.createReadStream(inputFile), {
      filename: 'test-input.wav',
      contentType: 'audio/wav'
    });
    formData.append('profile', 'ROBOTIC_SYNTHETIC');
    
    console.log('🚀 Sending audio for processing...');
    
    // Make the request
    const response = await fetch('http://localhost:3000/api/voice/process', {
      method: 'POST',
      body: formData,
      headers: {
        ...formData.getHeaders()
      }
    });
    
    console.log(`📡 Response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Request failed:', errorText);
      return;
    }
    
    // Get the response
    const contentType = response.headers.get('content-type');
    console.log(`📋 Response content-type: ${contentType}`);
    
    if (contentType && contentType.includes('audio')) {
      // It's an audio response
      const audioBuffer = await response.buffer();
      const outputSize = audioBuffer.length;
      const sizeRatio = outputSize / inputStats.size;
      
      console.log(`✅ Received processed audio`);
      console.log(`📏 Output size: ${outputSize} bytes`);
      console.log(`📊 Size ratio: ${sizeRatio.toFixed(3)}x`);
      
      // Check if the ratio is correct (close to 1.0)
      if (sizeRatio >= 0.9 && sizeRatio <= 1.1) {
        console.log(`✅ Size ratio looks good! (${sizeRatio.toFixed(3)}x - close to 1.0x)`);
        console.log('✅ The 4x output size issue appears to be FIXED!');
      } else {
        console.log(`⚠️ Size ratio is still off: ${sizeRatio.toFixed(3)}x`);
        if (sizeRatio > 3.5) {
          console.log('❌ The 4x output size issue is still present!');
        }
      }
      
      // Save the output
      const outputPath = './test-world-vocoder-output.wav';
      fs.writeFileSync(outputPath, audioBuffer);
      console.log(`💾 Output saved: ${outputPath}`);
      
    } else {
      // It's a JSON response
      const jsonResponse = await response.json();
      console.log('📄 JSON Response:', JSON.stringify(jsonResponse, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testWorldVocoderEndpoint();
