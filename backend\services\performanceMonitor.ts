/**
 * Performance Monitor Service
 * Monitors and optimizes the entire WORLD vocoder pipeline for <100ms latency
 * Provides comprehensive testing and performance metrics
 */

import { EventEmitter } from 'events';
import * as os from 'os';

export interface PerformanceMetrics {
  latency: {
    endToEnd: number;           // Total pipeline latency
    voiceCapture: number;       // Microphone capture latency
    worldProcessing: number;    // WORLD vocoder processing time
    securityProcessing: number; // Security transformation time
    networkTransmission: number; // WebRTC transmission time
    audioPlayback: number;      // Speaker output latency
  };
  throughput: {
    framesPerSecond: number;    // Audio frames processed per second
    bytesPerSecond: number;     // Audio data throughput
    packetsPerSecond: number;   // Network packets per second
  };
  quality: {
    audioQuality: number;       // 0-100 audio quality score
    voiceClarity: number;       // 0-100 voice clarity score
    morphingAccuracy: number;   // 0-100 morphing accuracy
    securityLevel: number;      // 0-100 security effectiveness
  };
  resources: {
    cpuUsage: number;          // CPU usage percentage
    memoryUsage: number;       // Memory usage in MB
    gpuUsage?: number;         // GPU usage if available
    networkBandwidth: number;  // Network bandwidth usage
  };
  errors: {
    dropouts: number;          // Audio dropouts count
    latencySpikes: number;     // Latency spikes > 100ms
    processingErrors: number;  // Processing errors count
    networkErrors: number;     // Network transmission errors
  };
}

export interface PerformanceTest {
  testId: string;
  testName: string;
  description: string;
  duration: number;           // Test duration in ms
  targetLatency: number;      // Target latency threshold
  results: PerformanceMetrics;
  passed: boolean;
  timestamp: Date;
}

export interface OptimizationConfig {
  targetLatency: number;      // Target end-to-end latency (default: 100ms)
  bufferSize: number;         // Audio buffer size optimization
  threadCount: number;        // Processing thread count
  priorityMode: 'latency' | 'quality' | 'balanced';
  adaptiveOptimization: boolean; // Enable adaptive optimization
}

/**
 * Performance Monitor Service
 * Monitors and optimizes voice processing pipeline performance
 */
export class PerformanceMonitorService extends EventEmitter {
  private metrics: PerformanceMetrics;
  private isMonitoring: boolean = false;
  private testResults: PerformanceTest[] = [];
  private optimizationConfig: OptimizationConfig;
  private monitoringInterval?: NodeJS.Timeout;

  constructor() {
    super();
    
    this.metrics = this.initializeMetrics();
    this.optimizationConfig = {
      targetLatency: 100,
      bufferSize: 256,
      threadCount: Math.min(4, os.cpus().length),
      priorityMode: 'balanced',
      adaptiveOptimization: true
    };

    console.log('📊 Performance Monitor Service initialized');
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(intervalMs: number = 1000): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.updateMetrics();
    }, intervalMs);

    this.emit('monitoringStarted');
    console.log('📊 Performance monitoring started');
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    this.emit('monitoringStopped');
    console.log('📊 Performance monitoring stopped');
  }

  /**
   * Record latency measurement
   */
  recordLatency(component: keyof PerformanceMetrics['latency'], latency: number): void {
    this.metrics.latency[component] = latency;
    
    // Calculate end-to-end latency
    this.metrics.latency.endToEnd = 
      this.metrics.latency.voiceCapture +
      this.metrics.latency.worldProcessing +
      this.metrics.latency.securityProcessing +
      this.metrics.latency.networkTransmission +
      this.metrics.latency.audioPlayback;

    // Check for latency spikes
    if (this.metrics.latency.endToEnd > this.optimizationConfig.targetLatency) {
      this.metrics.errors.latencySpikes++;
      this.emit('latencySpike', {
        component,
        latency,
        endToEndLatency: this.metrics.latency.endToEnd
      });
    }

    // Trigger adaptive optimization if enabled
    if (this.optimizationConfig.adaptiveOptimization) {
      this.adaptiveOptimization();
    }
  }

  /**
   * Record throughput measurement
   */
  recordThroughput(type: keyof PerformanceMetrics['throughput'], value: number): void {
    this.metrics.throughput[type] = value;
  }

  /**
   * Record quality measurement
   */
  recordQuality(type: keyof PerformanceMetrics['quality'], score: number): void {
    this.metrics.quality[type] = Math.max(0, Math.min(100, score));
  }

  /**
   * Record error occurrence
   */
  recordError(type: keyof PerformanceMetrics['errors']): void {
    this.metrics.errors[type]++;
    this.emit('errorRecorded', { type, count: this.metrics.errors[type] });
  }

  /**
   * Run comprehensive performance test
   */
  async runPerformanceTest(testName: string, durationMs: number = 30000): Promise<PerformanceTest> {
    const testId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    console.log(`📊 Starting performance test: ${testName}`);
    
    // Reset metrics for clean test
    this.metrics = this.initializeMetrics();
    
    // Start monitoring
    this.startMonitoring(100); // High frequency monitoring during test
    
    const startTime = Date.now();
    
    // Simulate voice processing load
    await this.simulateVoiceProcessingLoad(durationMs);
    
    const endTime = Date.now();
    
    // Stop monitoring
    this.stopMonitoring();
    
    // Evaluate test results
    const passed = this.evaluateTestResults();
    
    const test: PerformanceTest = {
      testId,
      testName,
      description: `Performance test running for ${durationMs}ms`,
      duration: endTime - startTime,
      targetLatency: this.optimizationConfig.targetLatency,
      results: { ...this.metrics },
      passed,
      timestamp: new Date()
    };
    
    this.testResults.push(test);
    this.emit('testCompleted', test);
    
    console.log(`📊 Performance test completed: ${testName}, Passed: ${passed}`);
    return test;
  }

  /**
   * Get current performance metrics
   */
  getCurrentMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Get test results
   */
  getTestResults(): PerformanceTest[] {
    return [...this.testResults];
  }

  /**
   * Get optimization recommendations
   */
  getOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.metrics.latency.endToEnd > this.optimizationConfig.targetLatency) {
      recommendations.push('Reduce audio buffer size to decrease latency');
    }
    
    if (this.metrics.resources.cpuUsage > 80) {
      recommendations.push('Optimize CPU usage or increase thread count');
    }
    
    if (this.metrics.errors.dropouts > 0) {
      recommendations.push('Increase buffer size to reduce audio dropouts');
    }
    
    if (this.metrics.quality.audioQuality < 70) {
      recommendations.push('Adjust voice processing parameters for better quality');
    }
    
    if (this.metrics.errors.latencySpikes > 5) {
      recommendations.push('Enable adaptive optimization to handle latency spikes');
    }
    
    return recommendations;
  }

  /**
   * Apply performance optimizations
   */
  async applyOptimizations(config: Partial<OptimizationConfig>): Promise<void> {
    this.optimizationConfig = { ...this.optimizationConfig, ...config };
    
    console.log('📊 Applying performance optimizations:', config);
    
    // Apply buffer size optimization
    if (config.bufferSize) {
      await this.optimizeBufferSize(config.bufferSize);
    }
    
    // Apply thread count optimization
    if (config.threadCount) {
      await this.optimizeThreadCount(config.threadCount);
    }
    
    // Apply priority mode optimization
    if (config.priorityMode) {
      await this.optimizePriorityMode(config.priorityMode);
    }
    
    this.emit('optimizationsApplied', config);
  }

  // Private implementation methods

  private initializeMetrics(): PerformanceMetrics {
    return {
      latency: {
        endToEnd: 0,
        voiceCapture: 0,
        worldProcessing: 0,
        securityProcessing: 0,
        networkTransmission: 0,
        audioPlayback: 0
      },
      throughput: {
        framesPerSecond: 0,
        bytesPerSecond: 0,
        packetsPerSecond: 0
      },
      quality: {
        audioQuality: 0,
        voiceClarity: 0,
        morphingAccuracy: 0,
        securityLevel: 0
      },
      resources: {
        cpuUsage: 0,
        memoryUsage: 0,
        networkBandwidth: 0
      },
      errors: {
        dropouts: 0,
        latencySpikes: 0,
        processingErrors: 0,
        networkErrors: 0
      }
    };
  }

  private updateMetrics(): void {
    // Update resource metrics
    this.updateResourceMetrics();
    
    // Emit metrics update
    this.emit('metricsUpdated', this.metrics);
  }

  private updateResourceMetrics(): void {
    // CPU usage
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;
    
    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += (cpu.times as any)[type];
      }
      totalIdle += cpu.times.idle;
    });
    
    this.metrics.resources.cpuUsage = Math.round(100 - (totalIdle / totalTick) * 100);
    
    // Memory usage
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    this.metrics.resources.memoryUsage = Math.round((totalMem - freeMem) / 1024 / 1024);
  }

  private async simulateVoiceProcessingLoad(durationMs: number): Promise<void> {
    const startTime = Date.now();
    const frameInterval = 20; // 20ms frames
    
    while (Date.now() - startTime < durationMs) {
      // Simulate voice processing latency
      const processingStart = Date.now();
      
      // Simulate WORLD vocoder processing
      await this.simulateProcessingDelay(15, 25); // 15-25ms
      this.recordLatency('worldProcessing', Date.now() - processingStart);
      
      // Simulate security processing
      const securityStart = Date.now();
      await this.simulateProcessingDelay(5, 15); // 5-15ms
      this.recordLatency('securityProcessing', Date.now() - securityStart);
      
      // Simulate network transmission
      const networkStart = Date.now();
      await this.simulateProcessingDelay(10, 30); // 10-30ms
      this.recordLatency('networkTransmission', Date.now() - networkStart);
      
      // Update throughput metrics
      this.recordThroughput('framesPerSecond', 1000 / frameInterval);
      this.recordThroughput('bytesPerSecond', 1024 * 48); // 48kHz * 1024 samples
      
      // Update quality metrics (simulated)
      this.recordQuality('audioQuality', 80 + Math.random() * 15);
      this.recordQuality('voiceClarity', 75 + Math.random() * 20);
      this.recordQuality('morphingAccuracy', 85 + Math.random() * 10);
      this.recordQuality('securityLevel', 90 + Math.random() * 10);
      
      // Wait for next frame
      await new Promise(resolve => setTimeout(resolve, frameInterval));
    }
  }

  private async simulateProcessingDelay(minMs: number, maxMs: number): Promise<void> {
    const delay = minMs + Math.random() * (maxMs - minMs);
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  private evaluateTestResults(): boolean {
    const metrics = this.metrics;
    
    // Check latency requirements
    if (metrics.latency.endToEnd > this.optimizationConfig.targetLatency) {
      return false;
    }
    
    // Check quality requirements
    if (metrics.quality.audioQuality < 70) {
      return false;
    }
    
    // Check error thresholds
    if (metrics.errors.dropouts > 5 || metrics.errors.latencySpikes > 10) {
      return false;
    }
    
    return true;
  }

  private adaptiveOptimization(): void {
    const latency = this.metrics.latency.endToEnd;
    const target = this.optimizationConfig.targetLatency;
    
    if (latency > target * 1.2) {
      // Significant latency spike - reduce buffer size
      this.optimizationConfig.bufferSize = Math.max(128, this.optimizationConfig.bufferSize - 32);
      this.emit('adaptiveOptimization', {
        action: 'reduceBufferSize',
        newBufferSize: this.optimizationConfig.bufferSize
      });
    } else if (latency < target * 0.8 && this.metrics.errors.dropouts === 0) {
      // Good latency with no dropouts - can increase buffer for quality
      this.optimizationConfig.bufferSize = Math.min(512, this.optimizationConfig.bufferSize + 32);
      this.emit('adaptiveOptimization', {
        action: 'increaseBufferSize',
        newBufferSize: this.optimizationConfig.bufferSize
      });
    }
  }

  private async optimizeBufferSize(bufferSize: number): Promise<void> {
    // Implementation would configure audio buffer size
    console.log(`📊 Optimizing buffer size to: ${bufferSize}`);
  }

  private async optimizeThreadCount(threadCount: number): Promise<void> {
    // Implementation would configure processing thread count
    console.log(`📊 Optimizing thread count to: ${threadCount}`);
  }

  private async optimizePriorityMode(priorityMode: string): Promise<void> {
    // Implementation would configure processing priority
    console.log(`📊 Optimizing priority mode to: ${priorityMode}`);
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitorService();
