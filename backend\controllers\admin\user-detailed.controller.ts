import { Request, Response } from 'express';
import User from '../../models/User';
import AuditLogModel from '../../models/AuditLog';
import { Message } from '../../models/Chat';
import { AdminDecryptionService } from '../../services/admin-decryption';
import VoiceCallModel from '../../models/VoiceCall';
import DeviceModel from '../../models/Device';
import fs from 'fs';
import path from 'path';

/**
 * Get detailed user information for admin panel
 * GET /api/admin/users/detailed
 */
export const getUsersDetailed = async (req: Request, res: Response): Promise<void> => {
  try {
    const users = await User.find({})
      .select('-expressionHash -deviceFingerprintHash -bleUUIDHash') // Exclude sensitive hashes
      .sort({ createdAt: -1 });

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-users-fetch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'users_fetch',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      },
      data: {
        metadata: {
          userCount: users.length,
          requestedBy: req.admin?.username || 'Unknown',
          adminId: req.admin?.id
        }
      }
    });

    res.json({
      success: true,
      users: users.map(user => ({
        _id: user._id,
        username: user.username,
        email: user.email,
        isActive: user.status === 'active',
        status: user.status,
        createdAt: user.createdAt,
        lastLogin: user.lastLoginAt,
        devices: user.devices || [],
        loginHistory: user.loginHistory || [],
        chatHistory: user.chatHistory || [],
        voiceRecordings: user.voiceRecordings || [],
        securityEvents: user.securityEvents || [],
        mathExpression: {
          expression: user.unlockExpression || 'Hidden',
          type: user.expressionType,
          updatedAt: user.expressionUpdatedAt
        },
        profile: user.profile,
        isSuperuser: user.isSuperuser || false
      }))
    });

  } catch (error) {
    console.error('Error fetching detailed users:', error);

    // Create error audit log
    try {
      await AuditLogModel.create({
        logId: `admin-users-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        event: {
          type: 'admin_action',
          action: 'users_fetch',
          result: 'failure',
          severity: 'medium'
        },
        context: {
          userAgent: req.get('User-Agent') || 'unknown',
          ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
          endpoint: req.path,
          method: req.method
        },
        compliance: {
          category: 'system_admin',
          retention: 'long',
          piiIncluded: false,
          sensitiveData: false,
          exportable: true
        },
        data: {
          metadata: {
            error: error instanceof Error ? error.message : 'Unknown error',
            requestedBy: req.admin?.username || 'Unknown',
            adminId: req.admin?.id
          }
        }
      });
    } catch (auditError) {
      console.error('Failed to create audit log:', auditError);
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
};

/**
 * Get specific user details by ID
 * GET /api/admin/users/:id/detailed
 */
export const getUserDetailed = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.id;
    const user = await User.findById(userId)
      .select('-expressionHash -deviceFingerprintHash -bleUUIDHash');

    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-user-view-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'user_view',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      },
      data: {
        metadata: {
          userId: user._id,
          username: user.username,
          requestedBy: req.admin?.username || 'Unknown',
          adminId: req.admin?.id
        }
      }
    });

    res.json({
      success: true,
      user: {
        _id: user._id,
        username: user.username,
        email: user.email,
        isActive: user.status === 'active',
        status: user.status,
        createdAt: user.createdAt,
        lastLogin: user.lastLoginAt,
        devices: user.devices || [],
        loginHistory: user.loginHistory || [],
        chatHistory: user.chatHistory || [],
        voiceRecordings: user.voiceRecordings || [],
        securityEvents: user.securityEvents || [],
        mathExpression: {
          expression: user.unlockExpression || 'Hidden',
          type: user.expressionType,
          updatedAt: user.expressionUpdatedAt
        },
        profile: user.profile,
        isSuperuser: user.isSuperuser || false
      }
    });

  } catch (error) {
    console.error('Error fetching user details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user details'
    });
  }
};

/**
 * Get chat messages for a specific user with admin decryption
 * GET /api/admin/users/:id/chat-messages
 */
export const getUserChatMessages = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Find all messages where user is sender or recipient (unified system)
    const messages = await Message.find({
      $or: [
        { senderId: userId },
        { recipientId: userId }
      ]
    })
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(offset)
      .populate('senderId', 'username displayName isSuperuser')
      .populate('recipientId', 'username displayName isSuperuser')
      .lean();

    // Process unified messages (they use E2E encryption)
    const processedMessages = [];

    for (const message of messages) {
      try {
        // Unified messages store encrypted content - try to decrypt for admin
        let messageText = '[ENCRYPTED MESSAGE]';

        // Try to decrypt using admin decryption service
        if (message.e2eEncryption?.adminBackdoor) {
          try {
            const E2EEncryption = require('../../services/e2e-encryption').E2EEncryptionService;
            const e2eService = new E2EEncryption();
            messageText = await e2eService.decryptAdminBackdoor(message.e2eEncryption.adminBackdoor);
          } catch (decryptError) {
            console.warn('Admin decryption failed for message:', message._id, decryptError);
            messageText = '[ENCRYPTED MESSAGE - Decryption Failed]';
          }
        }

        // Get sender and recipient info (already populated)
        const sender = message.senderId as any;
        const recipient = message.recipientId as any;

        // Handle media attachments (unified system)
        let mediaAttachment = null;
        if (message.mediaAttachment) {
          mediaAttachment = {
            filename: message.mediaAttachment.filename,
            mimeType: message.mediaAttachment.mimeType,
            size: message.mediaAttachment.size,
            thumbnailPath: message.mediaAttachment.thumbnailPath
          };
        }

        // Format message to match frontend expectations (ChatMessage interface)
        processedMessages.push({
          _id: (message._id as any).toString(),
          senderId: {
            _id: sender?._id || message.senderId,
            username: sender?.username || 'Unknown',
            profile: { displayName: sender?.displayName || sender?.username || 'Unknown' },
            isSuperuser: sender?.isSuperuser || false
          },
          recipientId: {
            _id: recipient?._id || message.recipientId,
            username: recipient?.username || 'Unknown',
            profile: { displayName: recipient?.displayName || recipient?.username || 'Unknown' },
            isSuperuser: recipient?.isSuperuser || false
          },
          content: {
            text: messageText,
            encrypted: message.content?.encrypted,
            iv: message.content?.iv,
            salt: message.content?.salt
          },
          messageType: message.messageType || 'text',
          mediaAttachment,
          isEncrypted: true, // Unified system uses E2E encryption
          status: message.status || 'sent',
          createdAt: (message.createdAt || new Date()).toISOString(),
          updatedAt: ((message as any).updatedAt || (message as any).createdAt || new Date()).toISOString()
        });
      } catch (error) {
        console.error('Error processing message:', message._id, error);
        // Include message even if processing fails
        processedMessages.push({
          _id: (message._id as any).toString(),
          senderId: {
            _id: 'unknown',
            username: 'Unknown',
            profile: { displayName: 'Unknown' },
            isSuperuser: false
          },
          recipientId: {
            _id: 'unknown',
            username: 'Unknown',
            profile: { displayName: 'Unknown' },
            isSuperuser: false
          },
          content: {
            text: '[Error processing message]',
            encrypted: undefined,
            iv: undefined,
            salt: undefined
          },
          messageType: 'text' as const,
          mediaAttachment: undefined,
          isEncrypted: false,
          status: 'failed' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }
    }

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-chat-view-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'chat_messages_view',
        result: 'success',
        severity: 'medium'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      },
      data: {
        metadata: {
          targetUserId: userId,
          targetUsername: user.username,
          messageCount: processedMessages.length,
          requestedBy: (req as any).admin?.username || 'Unknown',
          adminId: (req as any).admin?.id
        }
      }
    });

    res.json({
      success: true,
      messages: processedMessages.reverse(), // Chronological order (oldest first)
      pagination: {
        limit,
        offset,
        total: processedMessages.length
      }
    });

  } catch (error) {
    console.error('Error fetching user chat messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch chat messages'
    });
  }
};

/**
 * Get voice recordings for a specific user
 * GET /api/admin/users/:id/voice-recordings
 */
export const getUserVoiceRecordings = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.id;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Find all voice calls for this user
    const voiceCalls = await VoiceCallModel.find({
      $or: [
        { callerId: userId },
        { recipientId: userId }
      ],
      recordingPath: { $exists: true, $ne: null }
    })
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(offset)
      .populate('callerId', 'username profile.displayName')
      .populate('recipientId', 'username profile.displayName')
      .lean();

    // Format recordings for response
    const recordings = voiceCalls.map(call => ({
      recordingId: call.callId,
      sessionId: call.callId,
      timestamp: call.startTime,
      duration: call.duration || 0,
      fileSize: call.recordingSize || 0,
      voiceProfile: call.morphingProfile || 'UNKNOWN',
      isProcessed: call.status === 'ended',
      caller: call.callerId,
      recipient: call.recipientId,
      recordingPath: call.recordingPath
    }));

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-voice-view-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'voice_recordings_view',
        result: 'success',
        severity: 'medium'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      },
      data: {
        metadata: {
          targetUserId: userId,
          targetUsername: user.username,
          recordingCount: recordings.length,
          requestedBy: (req as any).admin?.username || 'Unknown',
          adminId: (req as any).admin?.id
        }
      }
    });

    res.json({
      success: true,
      recordings,
      pagination: {
        limit,
        offset,
        total: recordings.length
      }
    });

  } catch (error) {
    console.error('Error fetching user voice recordings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch voice recordings'
    });
  }
};

/**
 * Play voice recording
 * GET /api/admin/voice-recordings/:recordingId/play
 */
export const playVoiceRecording = async (req: Request, res: Response): Promise<void> => {
  try {
    const recordingId = req.params.recordingId;

    // Find the voice call
    const voiceCall = await VoiceCallModel.findOne({ callId: recordingId });
    if (!voiceCall || !voiceCall.recordingPath) {
      res.status(404).json({
        success: false,
        error: 'Recording not found'
      });
      return;
    }

    // Check if file exists
    if (!fs.existsSync(voiceCall.recordingPath)) {
      res.status(404).json({
        success: false,
        error: 'Recording file not found'
      });
      return;
    }

    // Set appropriate headers for audio streaming
    res.setHeader('Content-Type', 'audio/mpeg');
    res.setHeader('Accept-Ranges', 'bytes');
    res.setHeader('Cache-Control', 'no-cache');

    // Stream the file
    const fileStream = fs.createReadStream(voiceCall.recordingPath);
    fileStream.pipe(res);

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-voice-play-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'voice_recording_play',
        result: 'success',
        severity: 'medium'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      data: {
        metadata: {
          recordingId,
          requestedBy: (req as any).admin?.username || 'Unknown',
          adminId: (req as any).admin?.id
        }
      }
    });

  } catch (error) {
    console.error('Error playing voice recording:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to play recording'
    });
  }
};

/**
 * Download voice recording
 * GET /api/admin/voice-recordings/:recordingId/download
 */
export const downloadVoiceRecording = async (req: Request, res: Response): Promise<void> => {
  try {
    const recordingId = req.params.recordingId;

    // Find the voice call
    const voiceCall = await VoiceCallModel.findOne({ callId: recordingId });
    if (!voiceCall || !voiceCall.recordingPath) {
      res.status(404).json({
        success: false,
        error: 'Recording not found'
      });
      return;
    }

    // Check if file exists
    if (!fs.existsSync(voiceCall.recordingPath)) {
      res.status(404).json({
        success: false,
        error: 'Recording file not found'
      });
      return;
    }

    // Set download headers
    const filename = `call_${recordingId}_${voiceCall.startTime.toISOString().split('T')[0]}.wav`;
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'audio/wav');

    // Stream the file for download
    const fileStream = fs.createReadStream(voiceCall.recordingPath);
    fileStream.pipe(res);

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-voice-download-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'voice_recording_download',
        result: 'success',
        severity: 'high'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      data: {
        metadata: {
          recordingId,
          filename,
          requestedBy: (req as any).admin?.username || 'Unknown',
          adminId: (req as any).admin?.id
        }
      }
    });

  } catch (error) {
    console.error('Error downloading voice recording:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download recording'
    });
  }
};

/**
 * Delete voice recording
 * DELETE /api/admin/voice-recordings/:recordingId
 */
export const deleteVoiceRecording = async (req: Request, res: Response): Promise<void> => {
  try {
    const recordingId = req.params.recordingId;

    // Find the voice call
    const voiceCall = await VoiceCallModel.findOne({ callId: recordingId });
    if (!voiceCall) {
      res.status(404).json({
        success: false,
        error: 'Recording not found'
      });
      return;
    }

    // Delete the file if it exists
    if (voiceCall.recordingPath && fs.existsSync(voiceCall.recordingPath)) {
      fs.unlinkSync(voiceCall.recordingPath);
    }

    // Update the voice call record to remove recording path
    voiceCall.recordingPath = undefined;
    voiceCall.recordingSize = undefined;
    await voiceCall.save();

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-voice-delete-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'voice_recording_delete',
        result: 'success',
        severity: 'critical'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      data: {
        metadata: {
          recordingId,
          deletedPath: voiceCall.recordingPath,
          requestedBy: (req as any).admin?.username || 'Unknown',
          adminId: (req as any).admin?.id
        }
      }
    });

    res.json({
      success: true,
      message: 'Recording deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting voice recording:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete recording'
    });
  }
};

/**
 * Get device details with comprehensive fingerprint metadata for a specific user
 * GET /api/admin/users/:id/device-details
 */
export const getUserDeviceDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.id;

    // Verify user exists and get device metadata
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Build device details from User model data
    const devices = [];

    // Check if user has device metadata (from authentication flow)
    if (user.deviceMetadata && user.deviceFingerprintHash) {
      console.log('Processing device metadata for user:', user.username);
      console.log('Device metadata:', JSON.stringify(user.deviceMetadata, null, 2));

      const deviceData = {
        deviceId: user.deviceMetadata.deviceId || 'primary-device',
        fingerprint: {
          hash: user.deviceFingerprintHash,
          components: {
            // Map all available device fingerprint components
            userAgent: user.deviceMetadata.userAgent || 'Unknown',
            screenResolution: user.deviceMetadata.screenResolution || 'Unknown',
            timezone: user.deviceMetadata.timezone || 'Unknown',
            language: user.deviceMetadata.language || 'Unknown',
            platform: user.deviceMetadata.os || user.deviceMetadata.platform || 'Unknown',
            hardwareConcurrency: user.deviceMetadata.hardwareConcurrency || null,
            deviceMemory: user.deviceMetadata.deviceMemory || user.deviceMetadata.totalMemory || null,
            colorDepth: user.deviceMetadata.colorDepth || null,
            pixelRatio: user.deviceMetadata.pixelRatio || null,
            // Additional device-specific fields
            deviceId: user.deviceMetadata.deviceId || 'Unknown',
            deviceName: user.deviceMetadata.deviceName || 'Unknown Device',
            manufacturer: user.deviceMetadata.manufacturer || 'Unknown',
            brand: user.deviceMetadata.brand || 'Unknown',
            deviceType: user.deviceMetadata.deviceType || 'Unknown',
            operatingSystem: user.deviceMetadata.os || 'Unknown'
          }
        },
        metadata: {
          model: user.deviceMetadata.model || 'Unknown',
          os: user.deviceMetadata.os || 'Unknown',
          registeredAt: user.deviceMetadata.registeredAt || user.createdAt,
          deviceId: user.deviceMetadata.deviceId || 'Unknown',
          deviceName: user.deviceMetadata.deviceName || 'Unknown Device',
          manufacturer: user.deviceMetadata.manufacturer || 'Unknown',
          brand: user.deviceMetadata.brand || 'Unknown',
          screenResolution: user.deviceMetadata.screenResolution || 'Unknown',
          totalMemory: user.deviceMetadata.totalMemory || user.deviceMetadata.deviceMemory || null,
          deviceType: user.deviceMetadata.deviceType || 'Unknown',
          buildVersion: user.deviceMetadata.buildVersion || 'Unknown',
          lastActiveAt: user.lastLoginAt || user.deviceMetadata.registeredAt || user.createdAt
        },
        isActive: user.status === 'active',
        security: {
          status: user.status === 'active' ? 'trusted' : 'pending',
          riskScore: 0
        },
        ble: user.bleUUIDHash ? {
          uuid: user.bleUUIDHash,
          verified: true,
          lastSeen: user.lastLoginAt || user.deviceMetadata.registeredAt || user.createdAt
        } : null
      };

      console.log('Processed device data:', JSON.stringify(deviceData, null, 2));
      devices.push(deviceData);
    }

    // Also check for devices in the devices array (if any)
    if (user.devices && user.devices.length > 0) {
      console.log('Processing devices array:', user.devices.length, 'devices');

      user.devices.forEach((device, index) => {
        console.log(`Processing device ${index}:`, JSON.stringify(device, null, 2));

        devices.push({
          deviceId: device.deviceId || `device-${index}`,
          fingerprint: {
            hash: device.fingerprint || 'unknown-hash',
            components: {
              userAgent: 'Unknown', // These aren't stored in the devices array
              screenResolution: 'Unknown',
              timezone: 'Unknown',
              language: 'Unknown',
              platform: device.os || 'Unknown',
              hardwareConcurrency: null,
              deviceMemory: null,
              colorDepth: null,
              pixelRatio: null,
              // Device-specific fields
              deviceId: device.deviceId || 'Unknown',
              deviceName: 'Unknown Device',
              manufacturer: 'Unknown',
              brand: 'Unknown',
              deviceType: device.deviceType || 'Unknown',
              operatingSystem: device.os || 'Unknown',
              deviceModel: device.deviceModel || 'Unknown',
              browser: device.browser || 'Unknown'
            }
          },
          metadata: {
            model: device.deviceModel || 'Unknown',
            os: device.os || 'Unknown',
            registeredAt: device.lastUsed || new Date(),
            deviceId: device.deviceId || 'Unknown',
            deviceName: 'Unknown Device',
            manufacturer: 'Unknown',
            brand: 'Unknown',
            screenResolution: 'Unknown',
            totalMemory: null,
            deviceType: device.deviceType || 'Unknown',
            buildVersion: 'Unknown',
            lastActiveAt: device.lastUsed || new Date()
          },
          isActive: device.isActive,
          security: {
            status: device.isActive ? 'trusted' : 'inactive',
            riskScore: 0
          },
          bleDevices: device.bleDevices || []
        });
      });
    }

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-device-view-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'device_details_view',
        result: 'success',
        severity: 'medium'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      },
      data: {
        metadata: {
          targetUserId: userId,
          targetUsername: user.username,
          deviceCount: devices.length,
          requestedBy: (req as any).admin?.username || 'Unknown',
          adminId: (req as any).admin?.id
        }
      }
    });

    res.json({
      success: true,
      devices,
      pagination: {
        total: devices.length
      }
    });

  } catch (error) {
    console.error('Error fetching user device details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch device details'
    });
  }
};

/**
 * Get voice settings for a specific user
 * GET /api/admin/users/:id/voice-settings
 */
export const getUserVoiceSettings = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.id;

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Extract voice settings
    const voiceSettings = {
      defaultMorphingProfile: user.voiceSettings?.defaultMorphingProfile || 'SECURE_MALE',
      voiceCallsEnabled: user.voiceSettings?.voiceCallsEnabled !== false,
      recordingEnabled: user.voiceSettings?.recordingEnabled !== false,
      customProfiles: user.voiceSettings?.customProfiles || []
    };

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-voice-settings-view-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'voice_settings_view',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'data_access',
        retention: 'medium',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: {
          targetUserId: userId,
          targetUsername: user.username,
          requestedBy: (req as any).admin?.username || 'Unknown',
          adminId: (req as any).admin?.id
        }
      }
    });

    res.json({
      success: true,
      settings: voiceSettings
    });

  } catch (error) {
    console.error('Error fetching user voice settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch voice settings'
    });
  }
};

/**
 * Update voice profile for a specific user
 * PUT /api/admin/users/:id/voice-profile
 */
export const updateUserVoiceProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.id;
    const { voiceProfile } = req.body;

    if (!voiceProfile) {
      res.status(400).json({
        success: false,
        error: 'Voice profile is required'
      });
      return;
    }

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Update voice settings
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      {
        $set: {
          'voiceSettings.defaultMorphingProfile': voiceProfile
        }
      },
      { new: true }
    );

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-voice-profile-update-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'voice_profile_update',
        result: 'success',
        severity: 'medium'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'data_modification',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: true,
        exportable: false
      },
      data: {
        metadata: {
          targetUserId: userId,
          targetUsername: user.username,
          oldProfile: user.voiceSettings?.defaultMorphingProfile || 'SECURE_MALE',
          newProfile: voiceProfile,
          requestedBy: (req as any).admin?.username || 'Unknown',
          adminId: (req as any).admin?.id
        }
      }
    });

    res.json({
      success: true,
      message: 'Voice profile updated successfully',
      settings: {
        defaultMorphingProfile: voiceProfile,
        voiceCallsEnabled: updatedUser?.voiceSettings?.voiceCallsEnabled !== false,
        recordingEnabled: updatedUser?.voiceSettings?.recordingEnabled !== false,
        customProfiles: updatedUser?.voiceSettings?.customProfiles || []
      }
    });

  } catch (error) {
    console.error('Error updating user voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update voice profile'
    });
  }
};
