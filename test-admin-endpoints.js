#!/usr/bin/env node

/**
 * Test script to verify admin panel API endpoints
 */

const axios = require('axios');
const fs = require('fs');

const BACKEND_URL = 'http://localhost:3000';
const FRONTEND_URL = 'http://localhost:3005';

// Test configuration
const testConfig = {
  adminCredentials: {
    username: 'admin',
    password: 'admin123'
  }
};

let adminToken = null;

async function testEndpoint(method, url, data = null, headers = {}) {
  try {
    console.log(`Testing ${method.toUpperCase()} ${url}`);
    
    const config = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    console.log(`✅ ${method.toUpperCase()} ${url} - Status: ${response.status}`);
    return response.data;
  } catch (error) {
    console.log(`❌ ${method.toUpperCase()} ${url} - Error: ${error.response?.status || error.message}`);
    if (error.response?.data) {
      console.log(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return null;
  }
}

async function loginAdmin() {
  console.log('\n🔐 Testing Admin Login...');
  
  // Test admin login
  const loginData = await testEndpoint('POST', `${BACKEND_URL}/api/auth/admin/login`, {
    username: testConfig.adminCredentials.username,
    password: testConfig.adminCredentials.password
  });
  
  if (loginData && loginData.token) {
    adminToken = loginData.token;
    console.log('✅ Admin login successful');
    return true;
  } else {
    console.log('❌ Admin login failed');
    return false;
  }
}

async function testUserManagementEndpoints() {
  console.log('\n👥 Testing User Management Endpoints...');
  
  const authHeaders = { 'Authorization': `Bearer ${adminToken}` };
  
  // Test get users detailed
  await testEndpoint('GET', `${BACKEND_URL}/api/admin/users/detailed`, null, authHeaders);
  
  // Test get users (regular)
  await testEndpoint('GET', `${BACKEND_URL}/api/admin/users`, null, authHeaders);
  
  // Get first user for detailed tests
  const usersData = await testEndpoint('GET', `${BACKEND_URL}/api/admin/users`, null, authHeaders);
  
  if (usersData && usersData.users && usersData.users.length > 0) {
    const userId = usersData.users[0]._id;
    console.log(`\n📋 Testing detailed endpoints for user: ${userId}`);
    
    // Test user detailed endpoints
    await testEndpoint('GET', `${BACKEND_URL}/api/admin/users/${userId}/detailed`, null, authHeaders);
    await testEndpoint('GET', `${BACKEND_URL}/api/admin/users/${userId}/chat-messages`, null, authHeaders);
    await testEndpoint('GET', `${BACKEND_URL}/api/admin/users/${userId}/voice-recordings`, null, authHeaders);
    await testEndpoint('GET', `${BACKEND_URL}/api/admin/users/${userId}/device-details`, null, authHeaders);
    await testEndpoint('GET', `${BACKEND_URL}/api/admin/users/${userId}/voice-settings`, null, authHeaders);
  }
}

async function testVoiceEndpoints() {
  console.log('\n🎤 Testing Voice Endpoints...');
  
  const authHeaders = { 'Authorization': `Bearer ${adminToken}` };
  
  // Test voice service status
  await testEndpoint('GET', `${BACKEND_URL}/api/voice/service-status`, null, authHeaders);
  
  // Test voice profiles
  await testEndpoint('GET', `${BACKEND_URL}/api/voice/profiles`, null, authHeaders);
  
  // Test voice modulation settings
  await testEndpoint('GET', `${BACKEND_URL}/api/voice/modulation-settings`, null, authHeaders);
  
  // Test user voice settings
  await testEndpoint('GET', `${BACKEND_URL}/api/voice/user-settings`, null, authHeaders);
  
  // Test voice calls
  await testEndpoint('GET', `${BACKEND_URL}/api/voice/calls`, null, authHeaders);
}

async function testFrontendProxyEndpoints() {
  console.log('\n🌐 Testing Frontend Proxy Endpoints...');
  
  const authHeaders = { 'Authorization': `Bearer ${adminToken}` };
  
  // Test frontend proxy routes
  await testEndpoint('GET', `${FRONTEND_URL}/api/admin/users/detailed`, null, authHeaders);
  await testEndpoint('GET', `${FRONTEND_URL}/api/voice/service-status`, null, authHeaders);
}

async function runTests() {
  console.log('🧪 Starting CCALC Admin Panel API Tests\n');
  
  // Test admin login first
  const loginSuccess = await loginAdmin();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without admin authentication');
    return;
  }
  
  // Test all endpoints
  await testUserManagementEndpoints();
  await testVoiceEndpoints();
  await testFrontendProxyEndpoints();
  
  console.log('\n✅ All tests completed!');
  console.log('\n📊 Summary:');
  console.log('- Backend running on http://localhost:3000');
  console.log('- Frontend running on http://localhost:3005');
  console.log('- SoX voice modulation service available');
  console.log('- Admin authentication working');
}

// Run the tests
runTests().catch(console.error);
