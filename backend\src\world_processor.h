#ifndef WORLD_PROCESSOR_H
#define WORLD_PROCESSOR_H

#include <vector>
#include <memory>
#include <random>
#include "world/world.h"

/**
 * WORLD Vocoder Processor for Real-time Voice Transformation
 * Implements non-reversible voice morphing with <100ms latency
 */

struct VoiceMorphProfile {
    float pitchScale;       // 0.7-1.3 (pitch modification)
    float spectralWarp;     // -10% to +10% (formant shifting)
    float reverbAmount;     // 0-50% (spatial distortion)
    float eqTilt;          // -6dB to +6dB (frequency emphasis)
    float temporalJitter;   // Anti-forensic timing variation
    float spectralNoise;    // Irreversible spectral masking
    bool antiForensic;      // Enable anti-forensic processing
};

struct WorldFeatures {
    std::vector<double> f0;                              // Fundamental frequency
    std::vector<std::vector<double>> spectralEnvelope;   // Spectral envelope
    std::vector<std::vector<double>> aperiodicity;       // Aperiodicity
    int frameLength;                                     // Frame length
    int fftSize;                                         // FFT size
    size_t inputAudioLength;                             // Original input audio length for proper synthesis
};

class WorldProcessor {
public:
    /**
     * Constructor
     * @param sampleRate Audio sample rate (typically 48000)
     * @param frameSize Frame size in samples (typically 960 for 20ms at 48kHz)
     * @param realTimeMode Enable real-time optimizations
     */
    WorldProcessor(int sampleRate, int frameSize, bool realTimeMode = true);
    
    /**
     * Destructor
     */
    ~WorldProcessor();
    
    /**
     * Process audio frame with voice morphing
     * @param audioData Input audio samples
     * @param audioLength Number of samples
     * @param profile Voice morphing profile
     * @return Morphed audio samples
     */
    std::vector<float> ProcessFrame(const float* audioData, size_t audioLength, 
                                   const VoiceMorphProfile& profile);
    
    /**
     * Extract WORLD features from audio
     * @param audioData Input audio samples
     * @param audioLength Number of samples
     * @return Extracted WORLD features
     */
    WorldFeatures ExtractFeatures(const float* audioData, size_t audioLength);
    
    /**
     * Synthesize audio from WORLD features
     * @param features WORLD features
     * @return Synthesized audio samples
     */
    std::vector<float> SynthesizeAudio(const WorldFeatures& features);
    
    /**
     * Apply voice morphing to WORLD features
     * @param features Input features
     * @param profile Morphing profile
     * @return Morphed features
     */
    WorldFeatures ApplyMorphing(const WorldFeatures& features, const VoiceMorphProfile& profile);
    
    /**
     * Apply anti-forensic processing
     * @param audioData Audio samples to process
     * @param profile Morphing profile
     */
    void ApplyAntiForensicProcessing(std::vector<float>& audioData, const VoiceMorphProfile& profile);
    
    /**
     * Get processing latency in milliseconds
     * @return Current processing latency
     */
    double GetProcessingLatency() const { return processingLatency_; }
    
    /**
     * Get sample rate
     * @return Sample rate
     */
    int GetSampleRate() const { return sampleRate_; }
    
    /**
     * Get frame size
     * @return Frame size in samples
     */
    int GetFrameSize() const { return frameSize_; }

private:
    // Configuration
    int sampleRate_;
    int frameSize_;
    bool realTimeMode_;
    
    // WORLD parameters
    int fftSize_;
    double framePeriod_;
    
    // Processing buffers
    std::vector<double> audioBuffer_;
    std::vector<double> f0Buffer_;
    std::vector<double> timeAxis_;
    std::vector<double*> spectralEnvelopeBuffer_;
    std::vector<double*> aperiodicityBuffer_;
    std::vector<double> synthesizedBuffer_;
    
    // Performance monitoring
    double processingLatency_;
    
    // Anti-forensic processing
    std::mt19937 randomGenerator_;
    std::uniform_real_distribution<float> noiseDistribution_;
    
    // Private methods
    void InitializeBuffers();
    void CleanupBuffers();
    void ResizeBuffers(size_t newAudioLength);
    void ApplySpectralWarping(std::vector<std::vector<double>>& spectralEnvelope, 
                             float warpFactor);
    void ApplyTemporalJitter(std::vector<double>& f0, float jitterAmount);
    void ApplySpectralNoise(std::vector<std::vector<double>>& spectralEnvelope, 
                           float noiseAmount);
    void ApplyEQTilt(std::vector<std::vector<double>>& spectralEnvelope, float tiltAmount);
    void ApplyReverb(std::vector<float>& audioData, float reverbAmount);
    void NormalizeSpectralFrame(std::vector<double>& spectralFrame);
    
    // Utility methods
    double GetCurrentTimeMs() const;
    void ConvertFloatToDouble(const float* input, double* output, size_t length);
    void ConvertDoubleToFloat(const double* input, float* output, size_t length);
};

#endif // WORLD_PROCESSOR_H
