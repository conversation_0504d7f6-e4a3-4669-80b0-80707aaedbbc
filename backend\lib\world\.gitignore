# generated
build/

# Test
*.wav

# Visual Studio
visualstudio2015/.vs/
visualstudio2015/Debug/
visualstudio2015/Release/
visualstudio2015/win.v11.suo
visualstudio2015/win.VC.VC.opendb
visualstudio2015/win.VC.db
visualstudio2015/win/Debug/
visualstudio2015/win/Release/
visualstudio2015/win/win.vcxproj.user
visualstudio2015/win/x64/Debug/
visualstudio2015/win/x64/Release/
visualstudio2015/x64/Debug/
visualstudio2015/x64/Release/

visualstudio2019/.vs/
visualstudio2019/Debug/
visualstudio2019/Release/
visualstudio2019/win.v11.suo
visualstudio2019/win/Debug/
visualstudio2019/win/Release/
visualstudio2019/win/win.vcxproj.user
visualstudio2019/win/x64/Debug/
visualstudio2019/win/x64/Release/
visualstudio2019/x64/Debug/
visualstudio2019/x64/Release/

# IntelliJ project files
.idea
*.iml
out
gen

### JetBrains template
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio, WebStorm and Rider
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# User-specific stuff
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/**/usage.statistics.xml
.idea/**/dictionaries
.idea/**/shelf

# Generated files
.idea/**/contentModel.xml

# Sensitive or high-churn files
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml
.idea/**/dbnavigator.xml

# Gradle
.idea/**/gradle.xml
.idea/**/libraries

# Gradle and Maven with auto-import
# When using Gradle or Maven with auto-import, you should exclude module files,
# since they will be recreated, and may cause churn.  Uncomment if using
# auto-import.
# .idea/artifacts
# .idea/compiler.xml
# .idea/jarRepositories.xml
# .idea/modules.xml
# .idea/*.iml
# .idea/modules
# *.iml
# *.ipr

# CMake
cmake-build-*/

# Mongo Explorer plugin
.idea/**/mongoSettings.xml

# File-based project format
*.iws

# IntelliJ
out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Cursive Clojure plugin
.idea/replstate.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# Editor-based Rest Client
.idea/httpRequests

# Android studio 3.1+ serialized cache file
.idea/caches/build_file_checksums.ser

