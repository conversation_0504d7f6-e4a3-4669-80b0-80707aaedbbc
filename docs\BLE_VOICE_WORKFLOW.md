# BLE Device Registration and Voice Call Workflow

## Overview

The CCALC application implements a comprehensive BLE (Bluetooth Low Energy) device registration and verification system for voice calls. This document outlines the complete end-to-end workflow from device registration to voice call authorization.

## Architecture Components

### 1. Database Models

#### BleDevice Model (`backend/models/BleDevice.ts`)
- **Primary Model**: Dedicated BLE device management with comprehensive tracking
- **Features**: Registration data, security tracking, performance metrics, voice call authorization
- **Indexes**: Optimized for user queries and device lookups

#### User Model Integration (`backend/models/User.ts`)
- **Legacy Support**: Maintains backward compatibility with existing BLE device storage
- **Unified Storage**: New `bleDevices` array for enhanced device management
- **Sync Mechanism**: Automatic synchronization between models

### 2. Backend Services

#### BLE Verification Service (`backend/services/bleVerificationService.ts`)
- **Voice Call Authorization**: Verifies BLE device requirements based on user role
- **Challenge Generation**: Creates cryptographic challenges for device authentication
- **Security Tracking**: Records authentication attempts and failures

#### BLE Device Controller (`backend/controllers/admin/bleDevice.controller.ts`)
- **Device Registration**: Handles device registration from Python scripts
- **Admin Management**: Provides CRUD operations for BLE devices
- **Challenge Management**: Generates and validates authentication challenges

### 3. Frontend Components

#### BLE Device Management (`frontend/components/admin/BleDeviceManagement.tsx`)
- **Admin Interface**: Comprehensive device viewing and management
- **Real-time Updates**: Live device status and security information
- **Device Operations**: Edit, delete, and generate challenges

#### User Management Integration
- **Dedicated Tab**: BLE devices tab in user management interface
- **Device Statistics**: Overview of user's BLE devices in dashboard

### 4. Registration Scripts

#### GUI Registration (`scripts/ble_register_gui.py`)
- **User-Friendly Interface**: Tkinter-based GUI for device registration
- **Device Discovery**: Automatic BLE device scanning and selection
- **Data Capture**: Comprehensive device metadata collection

#### CLI Registration (`scripts/ble_register.py`)
- **Command-Line Interface**: Scriptable device registration
- **Batch Operations**: Support for automated device registration

## Workflow Details

### Phase 1: BLE Device Registration

1. **Device Discovery**
   - Python scripts scan for available BLE devices
   - Filter devices based on type (earbuds, headphones, etc.)
   - Capture advertisement data and RSSI information

2. **Device Connection**
   - Establish GATT connection to selected device
   - Discover available services and characteristics
   - Read device capabilities and metadata

3. **Signature Writing**
   - Generate unique signature for device authentication
   - Write signature to custom characteristic (`0000beef-0000-1000-8000-00805f9b34fb`)
   - Verify signature was written successfully

4. **Backend Registration**
   - Send device data to backend API (`POST /api/admin/users/{userId}/ble-devices`)
   - Store in both BleDevice model and User model for compatibility
   - Mark device as verified if signature was written

### Phase 2: Admin Panel Management

1. **Device Viewing**
   - Admin can view all registered BLE devices per user
   - Display device status, security information, and usage statistics
   - Real-time updates of device connection status

2. **Device Management**
   - Edit device properties (name, type, voice call authorization)
   - Deactivate or permanently delete devices
   - Add administrative notes and tags

3. **Security Operations**
   - Generate authentication challenges for devices
   - View security violations and risk scores
   - Monitor device usage patterns

### Phase 3: Voice Call Integration

1. **User Role Verification**
   ```typescript
   // Regular users require BLE verification
   if (!user.isSuperuser) {
     requiresBLE = true;
   }
   
   // Superuser can bypass BLE but still gets voice modulation
   if (user.isSuperuser) {
     allowVoiceCall = true;
     applyVoiceModulation = true;
   }
   ```

2. **BLE Device Challenge**
   - Generate cryptographic challenge for device authentication
   - Challenge expires after 5 minutes for security
   - Device must respond with correct challenge response

3. **Voice Call Authorization**
   - Verify BLE device is active and authorized for voice calls
   - Check device security status and risk score
   - Record voice call attempt for audit purposes

4. **Voice Modulation Application**
   - Apply user-specific voice modulation profile
   - Ensure voice modulation is mandatory for all calls
   - Record call with applied modulation

## API Endpoints

### BLE Device Management
- `POST /api/admin/users/{userId}/ble-devices` - Register new BLE device
- `GET /api/admin/users/{userId}/ble-devices` - Get user's BLE devices
- `GET /api/admin/ble-devices/{deviceId}` - Get device details
- `PUT /api/admin/ble-devices/{deviceId}` - Update device settings
- `DELETE /api/admin/ble-devices/{deviceId}` - Delete device
- `POST /api/admin/ble-devices/{deviceId}/challenge` - Generate auth challenge

### Voice Call Integration
- `GET /api/voice/ble-devices` - Get user's voice-enabled BLE devices
- `POST /api/voice/ble-challenge` - Generate challenge for voice call
- `GET /api/voice/ble-requirement` - Check if user requires BLE verification
- `POST /api/voice/start-call` - Initiate voice call with BLE verification

## Security Features

### Device Authentication
- **Unique Signatures**: Each device gets a unique cryptographic signature
- **Challenge-Response**: Time-limited challenges prevent replay attacks
- **Risk Scoring**: Continuous assessment of device security risk

### Access Control
- **Role-Based**: Different requirements for superuser vs regular users
- **Device Authorization**: Per-device voice call permissions
- **Audit Logging**: Complete audit trail of all BLE operations

### Voice Call Security
- **Mandatory Modulation**: All voice calls use voice modulation
- **BLE Verification**: Regular users must verify BLE device
- **Recording**: All calls are recorded for security purposes

## Testing

### End-to-End Test Suite (`scripts/test_ble_voice_workflow.py`)
The comprehensive test suite verifies:

1. **Admin Authentication**: Verify admin can authenticate
2. **User Setup**: Create/verify test user exists
3. **BLE Device Scan**: Test device discovery functionality
4. **Device Registration**: Test registration via API
5. **Admin Panel Integration**: Verify devices appear in admin panel
6. **Challenge Generation**: Test authentication challenge creation
7. **User Authentication**: Test user login for voice calls
8. **BLE Requirement Check**: Verify role-based BLE requirements
9. **Voice Call Initiation**: Test complete voice call workflow

### Running Tests
```bash
# Install dependencies
pip install bleak requests

# Run test suite
python scripts/test_ble_voice_workflow.py

# View help
python scripts/test_ble_voice_workflow.py --help
```

## Configuration

### Environment Variables
- `BACKEND_BASE_URL`: Backend server URL (default: http://localhost:3000)
- `BLE_SECRET_KEY`: Encryption key for BLE data
- `VOICE_MODULATION_ENABLED`: Enable/disable voice modulation

### BLE Service UUIDs
- **Custom Service**: `0000c0de-0000-1000-8000-00805f9b34fb`
- **Signature Characteristic**: `0000beef-0000-1000-8000-00805f9b34fb`

## Troubleshooting

### Common Issues

1. **BLE Device Not Found**
   - Ensure device is in pairing mode
   - Check Bluetooth is enabled on registration machine
   - Verify device is not already connected to another system

2. **Registration Fails**
   - Check backend server is running
   - Verify admin authentication credentials
   - Ensure user exists in system

3. **Voice Call Authorization Fails**
   - Verify BLE device is registered and active
   - Check device has voice call authorization enabled
   - Ensure challenge hasn't expired

4. **Admin Panel Not Showing Devices**
   - Refresh the page
   - Check browser console for errors
   - Verify admin has proper permissions

### Debug Mode
Enable debug logging by setting environment variable:
```bash
export DEBUG=ble:*
```

## Future Enhancements

1. **Real-time Device Monitoring**: Live connection status updates
2. **Device Health Metrics**: Battery level, signal strength tracking
3. **Automated Device Discovery**: Background scanning for new devices
4. **Enhanced Security**: Biometric authentication integration
5. **Device Grouping**: Organize devices by location or purpose

## Support

For technical support or questions about the BLE workflow:
1. Check the troubleshooting section above
2. Review the test suite output for specific error details
3. Examine backend logs for detailed error information
4. Consult the API documentation for endpoint specifications
