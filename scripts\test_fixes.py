#!/usr/bin/env python3
"""
Test script to verify the fixes for:
1. TypeScript compilation error in addBleDevice function
2. Device fingerprint data loss in admin panel
"""

import requests
import json
import time

# Configuration
BACKEND_BASE_URL = "http://localhost:3000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"
TEST_USER_USERNAME = "testuser"

def test_typescript_compilation():
    """Test that the backend starts without TypeScript compilation errors"""
    print("🔧 Testing TypeScript Compilation Fix...")
    
    try:
        # Try to access the backend health endpoint
        response = requests.get(f"{BACKEND_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running - TypeScript compilation successful")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend - server may not be running or compilation failed")
        return False
    except Exception as e:
        print(f"❌ Error testing backend: {str(e)}")
        return False

def test_ble_device_registration():
    """Test the BLE device registration endpoint"""
    print("\n🔧 Testing BLE Device Registration Fix...")
    
    try:
        # Authenticate as admin
        auth_response = requests.post(f"{BACKEND_BASE_URL}/api/admin/auth/login", json={
            "username": ADMIN_USERNAME,
            "password": ADMIN_PASSWORD
        })
        
        if auth_response.status_code != 200:
            print(f"❌ Admin authentication failed: {auth_response.status_code}")
            return False
        
        admin_token = auth_response.json().get("token")
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Get test user
        users_response = requests.get(f"{BACKEND_BASE_URL}/api/admin/users?search={TEST_USER_USERNAME}", headers=headers)
        if users_response.status_code != 200:
            print(f"❌ Failed to get users: {users_response.status_code}")
            return False
        
        users = users_response.json().get("users", [])
        test_user = next((u for u in users if u["username"] == TEST_USER_USERNAME), None)
        
        if not test_user:
            print(f"❌ Test user '{TEST_USER_USERNAME}' not found")
            return False
        
        user_id = test_user["_id"]
        
        # Test BLE device registration
        ble_data = {
            "deviceId": "test-device-123",
            "deviceName": "Test BLE Device",
            "adData": {"test": "data"},
            "characteristics": {"test": "char"},
            "isVerified": True,
            "signature": "test-signature",
            "signatureCharUuid": "test-uuid"
        }
        
        ble_response = requests.post(
            f"{BACKEND_BASE_URL}/api/admin/users/{user_id}/add-ble-device",
            headers=headers,
            json=ble_data
        )
        
        if ble_response.status_code in [200, 201, 409]:  # 409 = already exists
            print("✅ BLE device registration endpoint working")
            return True
        else:
            print(f"❌ BLE device registration failed: {ble_response.status_code} - {ble_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing BLE registration: {str(e)}")
        return False

def test_device_fingerprint_data():
    """Test device fingerprint data retrieval"""
    print("\n🔧 Testing Device Fingerprint Data Fix...")
    
    try:
        # Authenticate as admin
        auth_response = requests.post(f"{BACKEND_BASE_URL}/api/admin/auth/login", json={
            "username": ADMIN_USERNAME,
            "password": ADMIN_PASSWORD
        })
        
        if auth_response.status_code != 200:
            print(f"❌ Admin authentication failed: {auth_response.status_code}")
            return False
        
        admin_token = auth_response.json().get("token")
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Get test user
        users_response = requests.get(f"{BACKEND_BASE_URL}/api/admin/users?search={TEST_USER_USERNAME}", headers=headers)
        if users_response.status_code != 200:
            print(f"❌ Failed to get users: {users_response.status_code}")
            return False
        
        users = users_response.json().get("users", [])
        test_user = next((u for u in users if u["username"] == TEST_USER_USERNAME), None)
        
        if not test_user:
            print(f"❌ Test user '{TEST_USER_USERNAME}' not found")
            return False
        
        user_id = test_user["_id"]
        
        # Get device details
        device_response = requests.get(f"{BACKEND_BASE_URL}/api/admin/users/{user_id}/device-details", headers=headers)
        
        if device_response.status_code != 200:
            print(f"❌ Failed to get device details: {device_response.status_code}")
            return False
        
        device_data = device_response.json()
        devices = device_data.get("devices", [])
        
        if not devices:
            print("⚠️  No devices found for user - this is expected if user hasn't logged in")
            return True
        
        # Check if device fingerprint data is properly populated
        device = devices[0]
        fingerprint = device.get("fingerprint", {})
        components = fingerprint.get("components", {})
        
        # Count non-"Unknown" values
        known_values = 0
        total_fields = 0
        
        for key, value in components.items():
            total_fields += 1
            if value and value != "Unknown" and value != "N/A":
                known_values += 1
        
        if total_fields > 0:
            success_rate = (known_values / total_fields) * 100
            print(f"📊 Device fingerprint data: {known_values}/{total_fields} fields populated ({success_rate:.1f}%)")
            
            if success_rate > 30:  # At least 30% of fields should have real data
                print("✅ Device fingerprint data is properly populated")
                return True
            else:
                print("❌ Most device fingerprint fields are showing 'Unknown' - data loss issue persists")
                print("Sample data:", json.dumps(components, indent=2))
                return False
        else:
            print("⚠️  No fingerprint components found")
            return True
            
    except Exception as e:
        print(f"❌ Error testing device fingerprint data: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🧪 CCALC Backend Fixes Test Suite")
    print("=" * 50)
    
    results = []
    
    # Test 1: TypeScript compilation
    results.append(("TypeScript Compilation", test_typescript_compilation()))
    
    # Test 2: BLE device registration
    results.append(("BLE Device Registration", test_ble_device_registration()))
    
    # Test 3: Device fingerprint data
    results.append(("Device Fingerprint Data", test_device_fingerprint_data()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 All fixes verified successfully!")
    else:
        print("⚠️  Some issues may still need attention")

if __name__ == "__main__":
    main()
