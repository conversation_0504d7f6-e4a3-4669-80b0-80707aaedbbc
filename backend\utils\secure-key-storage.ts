/**
 * Secure Key Storage System
 * Fixes the critical security issue of storing encryption keys in plain text
 * Implements proper key encryption while maintaining admin panel access
 */

import crypto from 'crypto';

// Master key for encrypting storage keys (should be from environment)
const MASTER_KEY = process.env.MASTER_ENCRYPTION_KEY || 'fallback-master-key-change-in-production';
const ALGORITHM = 'aes-256-gcm';

interface EncryptedKeyData {
  encryptedKey: string;
  iv: string;
  tag: string;
  keyId: string;
}

interface AdminKeyData {
  encryptedKey: string;
  iv: string;
  tag: string;
  keyId: string;
  adminAccessKey: string; // Encrypted with admin master key
}

export class SecureKeyStorage {
  private static masterKeyBuffer: Buffer;

  /**
   * Initialize the secure key storage system
   */
  static initialize(): void {
    if (!MASTER_KEY || MASTER_KEY === 'fallback-master-key-change-in-production') {
      console.warn('⚠️ WARNING: Using fallback master key. Set MASTER_ENCRYPTION_KEY in production!');
    }
    
    this.masterKeyBuffer = crypto.scryptSync(MASTER_KEY, 'salt', 32);
    console.log('✅ Secure key storage initialized');
  }

  /**
   * Encrypt a storage key for secure database storage
   */
  static encryptStorageKey(plainKey: string | Buffer): EncryptedKeyData {
    const keyBuffer = typeof plainKey === 'string' ? Buffer.from(plainKey, 'base64') : plainKey;
    const iv = crypto.randomBytes(16);
    const keyId = crypto.randomUUID();
    
    const cipher = crypto.createCipheriv(ALGORITHM, this.masterKeyBuffer, iv);
    let encryptedKey = cipher.update(keyBuffer, undefined, 'hex');
    encryptedKey += cipher.final('hex');
    const tag = cipher.getAuthTag();

    return {
      encryptedKey,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
      keyId
    };
  }

  /**
   * Decrypt a storage key from database
   */
  static decryptStorageKey(encryptedData: EncryptedKeyData): Buffer {
    const iv = Buffer.from(encryptedData.iv, 'hex');
    const tag = Buffer.from(encryptedData.tag, 'hex');
    
    const decipher = crypto.createDecipheriv(ALGORITHM, this.masterKeyBuffer, iv);
    decipher.setAuthTag(tag);
    
    let decrypted = decipher.update(encryptedData.encryptedKey, 'hex');
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    return decrypted;
  }

  /**
   * Encrypt a key with admin access capability
   * This allows admin panel to decrypt messages for compliance
   */
  static encryptKeyWithAdminAccess(plainKey: string | Buffer, adminMasterKey?: string): AdminKeyData {
    const keyBuffer = typeof plainKey === 'string' ? Buffer.from(plainKey, 'base64') : plainKey;
    const iv = crypto.randomBytes(16);
    const keyId = crypto.randomUUID();
    
    // Encrypt with master key for normal access
    const cipher = crypto.createCipheriv(ALGORITHM, this.masterKeyBuffer, iv);
    let encryptedKey = cipher.update(keyBuffer, undefined, 'hex');
    encryptedKey += cipher.final('hex');
    const tag = cipher.getAuthTag();

    // Also encrypt with admin master key for admin panel access
    const adminKey = adminMasterKey || process.env.ADMIN_MASTER_KEY || MASTER_KEY;
    const adminKeyBuffer = crypto.scryptSync(adminKey, 'admin-salt', 32);
    const adminIv = crypto.randomBytes(16);
    
    const adminCipher = crypto.createCipheriv(ALGORITHM, adminKeyBuffer, adminIv);
    let adminEncryptedKey = adminCipher.update(keyBuffer, undefined, 'hex');
    adminEncryptedKey += adminCipher.final('hex');
    const adminTag = adminCipher.getAuthTag();

    const adminAccessKey = `${adminIv.toString('hex')}:${adminEncryptedKey}:${adminTag.toString('hex')}`;

    return {
      encryptedKey,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
      keyId,
      adminAccessKey
    };
  }

  /**
   * Decrypt key using admin access (for admin panel)
   */
  static decryptKeyWithAdminAccess(adminKeyData: AdminKeyData, adminMasterKey?: string): Buffer {
    const adminKey = adminMasterKey || process.env.ADMIN_MASTER_KEY || MASTER_KEY;
    const adminKeyBuffer = crypto.scryptSync(adminKey, 'admin-salt', 32);
    
    const [ivHex, encryptedHex, tagHex] = adminKeyData.adminAccessKey.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const tag = Buffer.from(tagHex, 'hex');
    
    const decipher = crypto.createDecipheriv(ALGORITHM, adminKeyBuffer, iv);
    decipher.setAuthTag(tag);
    
    let decrypted = decipher.update(encryptedHex, 'hex');
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    return decrypted;
  }

  /**
   * Generate a secure random key
   */
  static generateSecureKey(length: number = 32): Buffer {
    return crypto.randomBytes(length);
  }

  /**
   * Migrate existing plain text keys to secure storage
   */
  static migratePlainTextKey(plainTextKey: string): EncryptedKeyData {
    console.log('🔄 Migrating plain text key to secure storage');
    return this.encryptStorageKey(plainTextKey);
  }

  /**
   * Validate encrypted key data
   */
  static validateEncryptedKeyData(data: any): data is EncryptedKeyData {
    return (
      typeof data === 'object' &&
      typeof data.encryptedKey === 'string' &&
      typeof data.iv === 'string' &&
      typeof data.tag === 'string' &&
      typeof data.keyId === 'string'
    );
  }

  /**
   * Create a secure key for file encryption
   */
  static createFileEncryptionKey(): {
    key: Buffer;
    encryptedStorage: EncryptedKeyData;
    adminAccess: AdminKeyData;
  } {
    const key = this.generateSecureKey(32);
    const encryptedStorage = this.encryptStorageKey(key);
    const adminAccess = this.encryptKeyWithAdminAccess(key);

    return {
      key,
      encryptedStorage,
      adminAccess
    };
  }

  /**
   * Create a secure key for message encryption
   */
  static createMessageEncryptionKey(): {
    key: Buffer;
    encryptedStorage: EncryptedKeyData;
    adminAccess: AdminKeyData;
  } {
    const key = this.generateSecureKey(32);
    const encryptedStorage = this.encryptStorageKey(key);
    const adminAccess = this.encryptKeyWithAdminAccess(key);

    return {
      key,
      encryptedStorage,
      adminAccess
    };
  }

  /**
   * Rotate encryption keys (for security best practices)
   */
  static rotateKey(oldEncryptedData: EncryptedKeyData): {
    newKey: Buffer;
    newEncryptedStorage: EncryptedKeyData;
    oldKey: Buffer;
  } {
    const oldKey = this.decryptStorageKey(oldEncryptedData);
    const newKey = this.generateSecureKey(32);
    const newEncryptedStorage = this.encryptStorageKey(newKey);

    console.log('🔄 Key rotated successfully');

    return {
      newKey,
      newEncryptedStorage,
      oldKey
    };
  }

  /**
   * Get key derivation info for debugging (without exposing keys)
   */
  static getKeyInfo(encryptedData: EncryptedKeyData): {
    keyId: string;
    algorithm: string;
    ivLength: number;
    tagLength: number;
    encryptedLength: number;
  } {
    return {
      keyId: encryptedData.keyId,
      algorithm: ALGORITHM,
      ivLength: Buffer.from(encryptedData.iv, 'hex').length,
      tagLength: Buffer.from(encryptedData.tag, 'hex').length,
      encryptedLength: Buffer.from(encryptedData.encryptedKey, 'hex').length
    };
  }
}

// Initialize on module load
SecureKeyStorage.initialize();

export default SecureKeyStorage;
