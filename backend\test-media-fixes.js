/**
 * Test script to verify media persistence and WebSocket fixes
 */

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config();

// Import models and utilities
const Media = require('./models/Media').default;
const SecureKeyStorage = require('./utils/secure-key-storage').default;
const { encryptFile, decryptFile } = require('./utils/encryption');

async function testMediaDecryption() {
  console.log('\n🔐 Testing Media Decryption Fixes...');
  
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ccalc');
    console.log('✅ Connected to database');

    // Find a media record to test
    const mediaRecord = await Media.findOne({ isActive: true }).limit(1);
    
    if (!mediaRecord) {
      console.log('⚠️ No media records found in database');
      return;
    }

    console.log('📁 Testing media record:', {
      mediaId: mediaRecord.mediaId,
      originalName: mediaRecord.file.originalName,
      mimeType: mediaRecord.file.mimeType,
      encryptionKeyType: typeof mediaRecord.storage.encryptionKey,
      hasAdminAccessKey: !!mediaRecord.storage.adminAccessKey
    });

    // Test key decryption
    let decryptionKey;
    
    if (typeof mediaRecord.storage.encryptionKey === 'string') {
      console.log('🔑 Testing legacy key format...');
      decryptionKey = Buffer.from(mediaRecord.storage.encryptionKey, 'base64');
    } else if (mediaRecord.storage.encryptionKey && typeof mediaRecord.storage.encryptionKey === 'object') {
      console.log('🔑 Testing secure encrypted key format...');
      try {
        decryptionKey = SecureKeyStorage.decryptStorageKey(mediaRecord.storage.encryptionKey);
        console.log('✅ Successfully decrypted storage key');
      } catch (keyError) {
        console.error('❌ Failed to decrypt storage key:', keyError.message);
        
        // Try admin access key
        if (mediaRecord.storage.adminAccessKey) {
          console.log('🔑 Trying admin access key...');
          decryptionKey = SecureKeyStorage.decryptKeyWithAdminAccess(
            mediaRecord.storage.adminAccessKey,
            process.env.ADMIN_MASTER_KEY || 'fallback-admin-key'
          );
          console.log('✅ Successfully decrypted admin access key');
        } else {
          throw new Error('No admin access key available');
        }
      }
    }

    // Test file decryption
    if (fs.existsSync(mediaRecord.storage.encryptedPath)) {
      console.log('📂 Testing file decryption...');
      
      const encryptedBuffer = fs.readFileSync(mediaRecord.storage.encryptedPath);
      console.log('📂 Read encrypted file:', encryptedBuffer.length, 'bytes');

      const decryptedBuffer = decryptFile({
        encrypted: encryptedBuffer,
        iv: mediaRecord.storage.fileIv,
        tag: mediaRecord.storage.fileTag
      }, decryptionKey.toString('hex'));

      console.log('✅ Successfully decrypted file:', decryptedBuffer.length, 'bytes');
      
      // Verify it's a valid file by checking first few bytes
      const fileHeader = decryptedBuffer.slice(0, 10).toString('hex');
      console.log('📂 File header (hex):', fileHeader);
      
    } else {
      console.log('❌ Encrypted file not found:', mediaRecord.storage.encryptedPath);
    }

  } catch (error) {
    console.error('❌ Media decryption test failed:', error);
  }
}

async function testMediaListEndpoint() {
  console.log('\n📡 Testing Media List Endpoint...');
  
  try {
    const fetch = require('node-fetch');
    const baseUrl = process.env.BACKEND_URL || 'http://localhost:3001';
    
    // This would need a valid token in a real test
    console.log('📡 Media list endpoint URL:', `${baseUrl}/api/chat/unified/media/list`);
    console.log('⚠️ Note: This endpoint requires authentication token for actual testing');
    
  } catch (error) {
    console.error('❌ Media list endpoint test failed:', error);
  }
}

async function testSecureKeyStorage() {
  console.log('\n🔐 Testing Secure Key Storage...');
  
  try {
    // Test key encryption/decryption
    const testKey = Buffer.from('test-encryption-key-32-bytes-long!');
    
    // Test storage key encryption
    const encryptedStorage = SecureKeyStorage.encryptStorageKey(testKey);
    console.log('✅ Storage key encrypted');
    
    const decryptedStorage = SecureKeyStorage.decryptStorageKey(encryptedStorage);
    console.log('✅ Storage key decrypted');
    
    if (decryptedStorage.equals(testKey)) {
      console.log('✅ Storage key encryption/decryption working correctly');
    } else {
      console.log('❌ Storage key encryption/decryption failed');
    }
    
    // Test admin access key
    const adminKeyData = SecureKeyStorage.encryptKeyWithAdminAccess(testKey);
    console.log('✅ Admin access key encrypted');
    
    const decryptedAdmin = SecureKeyStorage.decryptKeyWithAdminAccess(adminKeyData);
    console.log('✅ Admin access key decrypted');
    
    if (decryptedAdmin.equals(testKey)) {
      console.log('✅ Admin access key encryption/decryption working correctly');
    } else {
      console.log('❌ Admin access key encryption/decryption failed');
    }
    
  } catch (error) {
    console.error('❌ Secure key storage test failed:', error);
  }
}

async function runTests() {
  console.log('🧪 Running Media Persistence and WebSocket Fixes Tests...');
  
  await testSecureKeyStorage();
  await testMediaDecryption();
  await testMediaListEndpoint();
  
  console.log('\n✅ Test suite completed');
  process.exit(0);
}

// Run tests
runTests().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
