/**
 * Admin API Routes
 * Handles admin panel endpoints
 */

import { Router } from 'express';
import { dashboardStatsHandler } from './dashboard';
import {
    getUsersDetailed,
    getUserChatMessages,
    getUserVoiceRecordings,
    getUserDeviceDetails,
    getUserVoiceSettings,
    updateUserVoiceProfile
} from '../../controllers/admin/user-detailed.controller';

const router = Router();

// Dashboard routes
router.get('/dashboard/stats', dashboardStatsHandler);

// User management routes
router.get('/users/detailed', getUsersDetailed);
router.get('/users/:id/chat-messages', getUserChatMessages);
router.get('/users/:id/voice-recordings', getUserVoiceRecordings);
router.get('/users/:id/device-details', getUserDeviceDetails);
router.get('/users/:id/voice-settings', getUserVoiceSettings);
router.put('/users/:id/voice-profile', updateUserVoiceProfile);

export default router;
