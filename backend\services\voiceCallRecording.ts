/**
 * Voice Call Recording Service
 * Handles recording of morphed voice calls for admin panel access
 * Stores all conversations with metadata and provides playback functionality
 */

import * as fs from 'fs';
import * as path from 'path';
import { Writable } from 'stream';
import * as crypto from 'crypto';
import VoiceCallModel from '../models/VoiceCall';
import { worldVocoderService } from './worldVocoderService';

export interface VoiceRecordingConfig {
  recordingPath: string;
  maxFileSize: number;      // Maximum file size in bytes
  compressionLevel: number; // Audio compression level (0-9)
  encryptRecordings: boolean;
  retentionDays: number;    // How long to keep recordings
}

export interface VoiceRecordingSession {
  callId: string;
  recordingPath: string;
  startTime: Date;
  endTime?: Date;
  fileSize: number;
  duration: number;
  participants: {
    initiator: string;
    recipient: string;
  };
  encryptionKey?: Buffer;
  metadata: {
    sampleRate: number;
    channels: number;
    bitDepth: number;
    codec: string;
  };
}

export interface AudioFrame {
  userId: string;
  audioData: Float32Array;
  timestamp: number;
  morphProfile: string;
}

/**
 * Voice Call Recording Service
 * Manages recording of all voice calls with morphed audio
 */
export class VoiceCallRecordingService {
  private activeRecordings: Map<string, VoiceRecordingSession> = new Map();
  private recordingStreams: Map<string, Writable> = new Map();
  private config: VoiceRecordingConfig;

  constructor(config?: Partial<VoiceRecordingConfig>) {
    this.config = {
      recordingPath: process.env.VOICE_RECORDINGS_PATH || './recordings',
      maxFileSize: 100 * 1024 * 1024, // 100MB default
      compressionLevel: 6,
      encryptRecordings: true,
      retentionDays: 90,
      ...config
    };

    // Ensure recording directory exists
    this.ensureRecordingDirectory();

    // Start cleanup job
    this.startCleanupJob();
  }

  /**
   * Start recording a voice call
   */
  async startRecording(callId: string, initiatorId: string, recipientId: string): Promise<boolean> {
    try {
      if (this.activeRecordings.has(callId)) {
        console.warn(`Recording already active for call: ${callId}`);
        return false;
      }

      const recordingPath = this.generateRecordingPath(callId);
      const encryptionKey = this.config.encryptRecordings ? crypto.randomBytes(32) : undefined;

      const session: VoiceRecordingSession = {
        callId,
        recordingPath,
        startTime: new Date(),
        fileSize: 0,
        duration: 0,
        participants: {
          initiator: initiatorId,
          recipient: recipientId
        },
        encryptionKey,
        metadata: {
          sampleRate: 48000,
          channels: 2, // Stereo: left=initiator, right=recipient
          bitDepth: 16,
          codec: 'PCM'
        }
      };

      // Create recording stream
      const recordingStream = this.createRecordingStream(recordingPath, encryptionKey);
      this.recordingStreams.set(callId, recordingStream);
      this.activeRecordings.set(callId, session);

      // Write WAV header
      await this.writeWavHeader(recordingStream, session.metadata);

      console.log(`🎤 Started recording for call: ${callId}`);
      return true;
    } catch (error) {
      console.error('Failed to start recording:', error);
      return false;
    }
  }

  /**
   * Record audio frame from a participant
   */
  async recordAudioFrame(callId: string, frame: AudioFrame): Promise<void> {
    const session = this.activeRecordings.get(callId);
    const stream = this.recordingStreams.get(callId);

    if (!session || !stream) {
      return;
    }

    try {
      // Convert Float32Array to PCM data
      const pcmData = this.convertToPCM(frame.audioData, session.metadata.bitDepth);
      
      // Determine channel (left=initiator, right=recipient)
      const isInitiator = frame.userId === session.participants.initiator;
      const stereoData = this.createStereoFrame(pcmData, isInitiator);

      // Write to stream
      stream.write(stereoData);

      // Update session metadata
      session.fileSize += stereoData.length;
      session.duration = Date.now() - session.startTime.getTime();

      // Check file size limit
      if (session.fileSize > this.config.maxFileSize) {
        console.warn(`Recording file size limit exceeded for call: ${callId}`);
        await this.stopRecording(callId);
      }
    } catch (error) {
      console.error('Failed to record audio frame:', error);
    }
  }

  /**
   * Stop recording a voice call
   */
  async stopRecording(callId: string): Promise<VoiceRecordingSession | null> {
    const session = this.activeRecordings.get(callId);
    const stream = this.recordingStreams.get(callId);

    if (!session || !stream) {
      return null;
    }

    try {
      session.endTime = new Date();
      session.duration = session.endTime.getTime() - session.startTime.getTime();

      // Close recording stream
      await new Promise<void>((resolve, reject) => {
        stream.end((error: Error | null | undefined) => {
          if (error) reject(error);
          else resolve();
        });
      });

      // Update WAV header with final file size
      await this.updateWavHeader(session.recordingPath, session.fileSize);

      // Update database record
      await this.updateVoiceCallRecord(session);

      // Cleanup
      this.activeRecordings.delete(callId);
      this.recordingStreams.delete(callId);

      console.log(`🎤 Stopped recording for call: ${callId}, duration: ${session.duration}ms`);
      return session;
    } catch (error) {
      console.error('Failed to stop recording:', error);
      return null;
    }
  }

  /**
   * Get recording for playback
   */
  async getRecording(callId: string): Promise<{
    filePath: string;
    metadata: VoiceRecordingSession;
    decryptionKey?: Buffer;
  } | null> {
    try {
      const voiceCall = await VoiceCallModel.findOne({ callId });
      if (!voiceCall || !voiceCall.recordingPath) {
        return null;
      }

      const recordingPath = path.resolve(voiceCall.recordingPath);
      
      // Check if file exists
      if (!fs.existsSync(recordingPath)) {
        console.error(`Recording file not found: ${recordingPath}`);
        return null;
      }

      // Get session metadata
      const session = this.activeRecordings.get(callId);
      if (session) {
        return {
          filePath: recordingPath,
          metadata: session,
          decryptionKey: session.encryptionKey
        };
      }

      // Reconstruct metadata from database
      const metadata: VoiceRecordingSession = {
        callId,
        recordingPath,
        startTime: voiceCall.startTime,
        endTime: voiceCall.endTime,
        fileSize: voiceCall.recordingSize || 0,
        duration: voiceCall.recordingDuration || 0,
        participants: {
          initiator: voiceCall.initiatorId || voiceCall.callerId,
          recipient: voiceCall.recipientId
        },
        metadata: {
          sampleRate: 48000,
          channels: 2,
          bitDepth: 16,
          codec: 'PCM'
        }
      };

      return {
        filePath: recordingPath,
        metadata,
        decryptionKey: voiceCall.metadata?.encryptionKey
      };
    } catch (error) {
      console.error('Failed to get recording:', error);
      return null;
    }
  }

  /**
   * Delete a recording
   */
  async deleteRecording(callId: string): Promise<boolean> {
    try {
      const recording = await this.getRecording(callId);
      if (!recording) {
        return false;
      }

      // Delete file
      if (fs.existsSync(recording.filePath)) {
        fs.unlinkSync(recording.filePath);
      }

      // Update database
      await VoiceCallModel.updateOne(
        { callId },
        { 
          $unset: { recordingPath: 1, recordingSize: 1 },
          $set: { 'metadata.recordingDeleted': true }
        }
      );

      console.log(`🗑️ Deleted recording for call: ${callId}`);
      return true;
    } catch (error) {
      console.error('Failed to delete recording:', error);
      return false;
    }
  }

  /**
   * Get all recordings for admin panel
   */
  async getAllRecordings(page: number = 1, limit: number = 50): Promise<{
    recordings: VoiceRecordingSession[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const skip = (page - 1) * limit;
      
      const [calls, total] = await Promise.all([
        VoiceCallModel.find({ recordingPath: { $exists: true } })
          .sort({ startTime: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        VoiceCallModel.countDocuments({ recordingPath: { $exists: true } })
      ]);

      const recordings: VoiceRecordingSession[] = calls.map(call => ({
        callId: call.callId,
        recordingPath: call.recordingPath!,
        startTime: call.startTime,
        endTime: call.endTime,
        fileSize: call.recordingSize || 0,
        duration: call.recordingDuration || 0,
        participants: {
          initiator: call.initiatorId || call.callerId,
          recipient: call.recipientId
        },
        metadata: {
          sampleRate: 48000,
          channels: 2,
          bitDepth: 16,
          codec: 'PCM'
        }
      }));

      return {
        recordings,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('Failed to get recordings:', error);
      return {
        recordings: [],
        total: 0,
        page: 1,
        totalPages: 0
      };
    }
  }

  private generateRecordingPath(callId: string): string {
    const date = new Date();
    const dateStr = date.toISOString().split('T')[0];
    const filename = `${callId}_${Date.now()}.wav`;
    return path.join(this.config.recordingPath, dateStr, filename);
  }

  private createRecordingStream(filePath: string, encryptionKey?: Buffer): Writable {
    // Ensure directory exists
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    let stream: Writable = fs.createWriteStream(filePath);

    // Add encryption if enabled
    if (encryptionKey && this.config.encryptRecordings) {
      const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
      stream = cipher.pipe(stream);
    }

    return stream;
  }

  private async writeWavHeader(stream: Writable, metadata: VoiceRecordingSession['metadata']): Promise<void> {
    const header = this.createWavHeader(metadata);
    return new Promise((resolve, reject) => {
      stream.write(header, (error) => {
        if (error) reject(error);
        else resolve();
      });
    });
  }

  private createWavHeader(metadata: VoiceRecordingSession['metadata']): Buffer {
    const header = Buffer.alloc(44);
    
    // RIFF header
    header.write('RIFF', 0);
    header.writeUInt32LE(36, 4); // File size - 8 (will be updated later)
    header.write('WAVE', 8);
    
    // Format chunk
    header.write('fmt ', 12);
    header.writeUInt32LE(16, 16); // Format chunk size
    header.writeUInt16LE(1, 20);  // PCM format
    header.writeUInt16LE(metadata.channels, 22);
    header.writeUInt32LE(metadata.sampleRate, 24);
    header.writeUInt32LE(metadata.sampleRate * metadata.channels * (metadata.bitDepth / 8), 28); // Byte rate
    header.writeUInt16LE(metadata.channels * (metadata.bitDepth / 8), 32); // Block align
    header.writeUInt16LE(metadata.bitDepth, 34);
    
    // Data chunk
    header.write('data', 36);
    header.writeUInt32LE(0, 40); // Data size (will be updated later)
    
    return header;
  }

  private async updateWavHeader(filePath: string, dataSize: number): Promise<void> {
    try {
      const fd = fs.openSync(filePath, 'r+');
      
      // Update file size
      const fileSize = Buffer.alloc(4);
      fileSize.writeUInt32LE(dataSize + 36, 0);
      fs.writeSync(fd, fileSize, 0, 4, 4);
      
      // Update data chunk size
      const dataSizeBuffer = Buffer.alloc(4);
      dataSizeBuffer.writeUInt32LE(dataSize, 0);
      fs.writeSync(fd, dataSizeBuffer, 0, 4, 40);
      
      fs.closeSync(fd);
    } catch (error) {
      console.error('Failed to update WAV header:', error);
    }
  }

  private convertToPCM(audioData: Float32Array, bitDepth: number): Buffer {
    const buffer = Buffer.alloc(audioData.length * (bitDepth / 8));
    
    if (bitDepth === 16) {
      for (let i = 0; i < audioData.length; i++) {
        const sample = Math.max(-1, Math.min(1, audioData[i]));
        const pcmValue = Math.round(sample * 32767);
        buffer.writeInt16LE(pcmValue, i * 2);
      }
    }
    
    return buffer;
  }

  private createStereoFrame(pcmData: Buffer, isInitiator: boolean): Buffer {
    const stereoBuffer = Buffer.alloc(pcmData.length * 2);
    
    for (let i = 0; i < pcmData.length; i += 2) {
      const sample = pcmData.readInt16LE(i);
      
      if (isInitiator) {
        // Left channel (initiator)
        stereoBuffer.writeInt16LE(sample, i * 2);
        stereoBuffer.writeInt16LE(0, i * 2 + 2); // Right channel silence
      } else {
        // Right channel (recipient)
        stereoBuffer.writeInt16LE(0, i * 2); // Left channel silence
        stereoBuffer.writeInt16LE(sample, i * 2 + 2);
      }
    }
    
    return stereoBuffer;
  }

  private async updateVoiceCallRecord(session: VoiceRecordingSession): Promise<void> {
    try {
      await VoiceCallModel.updateOne(
        { callId: session.callId },
        {
          $set: {
            recordingPath: session.recordingPath,
            recordingSize: session.fileSize,
            recordingDuration: session.duration,
            'metadata.recordingEnabled': true,
            'metadata.recordingStarted': session.startTime
          }
        }
      );
    } catch (error) {
      console.error('Failed to update voice call record:', error);
    }
  }

  private ensureRecordingDirectory(): void {
    if (!fs.existsSync(this.config.recordingPath)) {
      fs.mkdirSync(this.config.recordingPath, { recursive: true });
    }
  }

  private startCleanupJob(): void {
    // Run cleanup every 24 hours
    setInterval(() => {
      this.cleanupOldRecordings();
    }, 24 * 60 * 60 * 1000);
  }

  private async cleanupOldRecordings(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);

      const oldCalls = await VoiceCallModel.find({
        startTime: { $lt: cutoffDate },
        recordingPath: { $exists: true }
      });

      for (const call of oldCalls) {
        if (call.recordingPath && fs.existsSync(call.recordingPath)) {
          fs.unlinkSync(call.recordingPath);
          console.log(`🗑️ Cleaned up old recording: ${call.callId}`);
        }

        await VoiceCallModel.updateOne(
          { _id: call._id },
          { $unset: { recordingPath: 1, recordingSize: 1 } }
        );
      }
    } catch (error) {
      console.error('Failed to cleanup old recordings:', error);
    }
  }
}

// Export singleton instance
export const voiceCallRecordingService = new VoiceCallRecordingService();
