/**
 * Admin Chat Management API
 * Allows admin to view and decrypt chat messages
 */

import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import { adminPanelOnly } from '../../middleware/strictAuth';
import { Chat, Message } from '../../models/Chat';
import UserModel from '../../models/User';
import AuditLogModel from '../../models/AuditLog';
import { ChatEncryption } from '../../utils/encryption';
import path from 'path';
import fs from 'fs/promises';

const router = Router();

/**
 * Get all chat conversations for admin panel
 * GET /api/admin/chat/conversations
 */
router.get('/conversations', authenticateToken, adminPanelOnly, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = (req as any).user?._id || (req as any).user?.id;

    console.log('👑 Admin chat conversations request:', { adminId });

    // Get all active chats from unified system
    const chats = await Chat.find({ isActive: true })
      .populate('participants', 'username displayName status')
      .sort({ lastActivity: -1 });

    const conversations = await Promise.all(
      chats.map(async (chat) => {
        // Get latest message from unified system
        const latestMessage = await Message.findOne({
          _id: { $in: chat.messages }
        }).sort({ createdAt: -1 });

        // Get participant info
        const participants = chat.participants as any[];
        const nonSuperuserParticipant = participants.find(p => !p.isSuperuser);

        return {
          chatId: (chat._id as any).toString(),
          participant: nonSuperuserParticipant ? {
            id: nonSuperuserParticipant._id,
            username: nonSuperuserParticipant.username,
            displayName: nonSuperuserParticipant.displayName || nonSuperuserParticipant.username,
            status: nonSuperuserParticipant.status
          } : null,
          lastActivity: chat.lastActivity,
          messageCount: chat.messages.length,
          lastMessage: latestMessage ? {
            content: latestMessage.content,
            sentAt: latestMessage.createdAt,
            isEncrypted: true // Unified system uses E2E encryption
          } : null
        };
      })
    );

    // Log admin access
    await AuditLogModel.create({
      logId: `admin_chat_access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId,
      event: {
        type: 'admin_action',
        action: 'CHAT_CONVERSATIONS_ACCESSED',
        resource: '/api/admin/chat/conversations',
        result: 'success',
        severity: 'low'
      },
      context: {
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        endpoint: req.originalUrl,
        method: req.method
      },
      security: {
        riskScore: 20,
        authMethod: 'password',
        mfaUsed: false,
        suspiciousActivity: false
      },
      data: {
        metadata: {
          conversationCount: conversations.length
        }
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      }
    });

    res.json({
      success: true,
      conversations
    });

  } catch (error: any) {
    console.error('Admin chat conversations error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversations'
    });
  }
});

/**
 * Get messages for a specific chat
 * GET /api/admin/chat/messages/:chatId
 */
router.get('/messages/:chatId', authenticateToken, adminPanelOnly, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = (req as any).user?._id || (req as any).user?.id;
    const { chatId } = req.params;
    const { limit = 100, offset = 0 } = req.query;

    console.log('👑 Admin chat messages request:', { adminId, chatId, limit, offset });

    // Verify chat exists in unified system
    const chat = await Chat.findById(chatId);
    if (!chat) {
      res.status(404).json({
        success: false,
        error: 'Chat not found'
      });
      return;
    }

    // Get messages from unified system
    const messages = await Message.find({
      _id: { $in: chat.messages }
    })
      .sort({ createdAt: 1 })
      .limit(Number(limit))
      .skip(Number(offset))
      .populate('senderId', 'username displayName isSuperuser')
      .populate('recipientId', 'username displayName isSuperuser');

    // Format messages for admin (with decryption)
    const formattedMessages = await Promise.all(
      messages.map(async (msg) => {
        const sender = msg.senderId as any;
        const recipient = msg.recipientId as any;

        // Decrypt message content using unified system's E2E encryption
        let decryptedText = '[ENCRYPTED MESSAGE]';

        // Try to decrypt using E2E admin backdoor
        if (msg.e2eEncryption?.adminBackdoor) {
          try {
            const E2EEncryption = require('../../services/e2e-encryption').E2EEncryptionService;
            const e2eService = new E2EEncryption();
            decryptedText = await e2eService.decryptAdminBackdoor(msg.e2eEncryption.adminBackdoor);
            console.log('✅ Admin decrypted E2E message successfully');
          } catch (error) {
            console.warn('Admin E2E decryption failed:', error);
            decryptedText = '[ENCRYPTED MESSAGE - Decryption Failed]';
          }
        } else if (msg.content?.adminAccessKey) {
          // Fallback to legacy admin access key
          try {
            const AdminDecryption = require('../../services/admin-decryption').AdminDecryptionService;
            const adminDecryption = new AdminDecryption();
            const decryptedMessage = await adminDecryption.decryptMessage((msg._id as any).toString());
            decryptedText = decryptedMessage?.content || '[ENCRYPTED MESSAGE - Decryption Failed]';
            console.log('✅ Admin decrypted using admin access key');
          } catch (legacyError) {
            console.warn('Admin access key decryption failed:', legacyError);
            decryptedText = '[ENCRYPTED MESSAGE - Decryption Failed]';
          }
        }

        return {
          id: (msg._id as any).toString(),
          chatId: chatId,
          sender: {
            id: sender._id,
            username: sender.username,
            displayName: sender.displayName || sender.username,
            isSuperuser: sender.isSuperuser
          },
          recipient: {
            id: recipient._id,
            username: recipient.username,
            displayName: recipient.displayName || recipient.username,
            isSuperuser: recipient.isSuperuser
          },
          content: {
            text: decryptedText,
            originalEncrypted: msg.content.encrypted, // Keep original encrypted text for reference
            messageType: msg.messageType
          },
          sentAt: msg.createdAt,
          platform: 'mobile', // Default platform
          status: msg.status,
          isEncrypted: true, // Unified system uses E2E encryption
          hasAttachment: !!msg.mediaAttachment
        };
      })
    );

    // Log admin access
    await AuditLogModel.create({
      logId: `admin_chat_messages_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId,
      event: {
        type: 'admin_action',
        action: 'CHAT_MESSAGES_ACCESSED',
        resource: `/api/admin/chat/messages/${chatId}`,
        result: 'success',
        severity: 'medium'
      },
      context: {
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        endpoint: req.originalUrl,
        method: req.method
      },
      security: {
        riskScore: 30,
        authMethod: 'password',
        mfaUsed: false,
        suspiciousActivity: false
      },
      data: {
        metadata: {
          chatId,
          messageCount: formattedMessages.length,
          participantIds: chat.participants
        }
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      }
    });

    res.json({
      success: true,
      messages: formattedMessages.reverse(), // Chronological order
      chat: {
        chatId: (chat._id as any).toString(),
        participants: chat.participants,
        messageCount: chat.messages.length,
        lastActivity: chat.lastActivity
      }
    });

  } catch (error: any) {
    console.error('Admin chat messages error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch messages'
    });
  }
});

/**
 * Download attachment file (admin only)
 * GET /api/admin/chat/attachment/:fileName
 */
router.get('/attachment/:fileName', authenticateToken, adminPanelOnly, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = (req as any).user?._id || (req as any).user?.id;
    const { fileName } = req.params;

    console.log('👑 Admin attachment download:', { adminId, fileName });

    // Security check - ensure filename is safe
    if (!fileName || fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
      res.status(400).json({
        success: false,
        error: 'Invalid filename'
      });
      return;
    }

    const filePath = path.join(process.cwd(), 'uploads', 'chat-attachments', fileName);

    // Check if file exists
    try {
      await fs.access(filePath);
    } catch {
      res.status(404).json({
        success: false,
        error: 'File not found'
      });
      return;
    }

    // Log admin file access
    await AuditLogModel.create({
      logId: `admin_file_access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId,
      event: {
        type: 'admin_action',
        action: 'CHAT_ATTACHMENT_ACCESSED',
        resource: `/api/admin/chat/attachment/${fileName}`,
        result: 'success',
        severity: 'medium'
      },
      context: {
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        endpoint: req.originalUrl,
        method: req.method
      },
      security: {
        riskScore: 40,
        authMethod: 'password',
        mfaUsed: false,
        suspiciousActivity: false
      },
      data: {
        metadata: {
          fileName,
          filePath: filePath
        }
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: true,
        exportable: false
      }
    });

    // Send file
    res.sendFile(filePath);

  } catch (error: any) {
    console.error('Admin attachment download error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download attachment'
    });
  }
});

export default router;
