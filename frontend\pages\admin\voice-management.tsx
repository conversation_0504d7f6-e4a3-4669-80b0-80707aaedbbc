import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { withAdminAuth } from '../../contexts/AdminAuthContext';
import AdminLayout from '../../components/admin/AdminLayout';
import apiClient from '../../utils/apiClient';
import VoiceNeutralization from '../../components/VoiceNeutralization';
import {
  Security as SecurityIcon,
  CheckCircle as CheckCircleIcon,
  Warning as ExclamationTriangleIcon,
  Info as InfoIcon
} from '@mui/icons-material';

interface VoiceManagementProps {}

const VoiceManagement: React.FC<VoiceManagementProps> = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [serviceStatus, setServiceStatus] = useState<any>(null);

  useEffect(() => {
    fetchServiceStatus();
    setIsLoading(false);
  }, []);

  const fetchServiceStatus = async () => {
    try {
      const response = await apiClient.get('/api/voice/service-status');
      setServiceStatus(response.data);
    } catch (error: any) {
      console.error('Failed to fetch service status:', error);
      setError('Failed to fetch service status');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading Voice Management...</p>
        </div>
      </div>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Voice Management</h1>
              <p className="mt-2 text-gray-600">
                Unified interface for voice processing, neutralization, and modulation
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {serviceStatus?.status === 'healthy' ? (
                <div className="flex items-center text-green-600">
                  <CheckCircleIcon className="w-5 h-5 mr-1" />
                  <span className="text-sm font-medium">Service Online</span>
                </div>
              ) : (
                <div className="flex items-center text-red-600">
                  <ExclamationTriangleIcon className="w-5 h-5 mr-1" />
                  <span className="text-sm font-medium">Service Issues</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="w-5 h-5 text-red-600 mr-2" />
              <span className="text-red-800">{error}</span>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Voice Neutralization - Main Feature */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-lg shadow-lg overflow-hidden"
            >
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
                <div className="flex items-center">
                  <SecurityIcon className="w-6 h-6 text-white mr-3" />
                  <div>
                    <h2 className="text-xl font-semibold text-white">Voice Neutralization</h2>
                    <p className="text-blue-100 text-sm">
                      Real-time, irreversible voice anonymization with tone neutralization
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="p-6">
                <VoiceNeutralization />
              </div>
            </motion.div>
          </div>

          {/* Sidebar - Information and Status */}
          <div className="space-y-6">
            {/* System Status */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="bg-white rounded-lg shadow p-6"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <InfoIcon className="w-5 h-5 mr-2 text-blue-600" />
                System Status
              </h3>
              
              {serviceStatus ? (
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Voice Service</span>
                    <span className={`text-sm font-medium ${
                      serviceStatus.status === 'healthy' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {serviceStatus.status === 'healthy' ? 'Online' : 'Offline'}
                    </span>
                  </div>
                  
                  {serviceStatus.neutralizationAvailable && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Neutralization</span>
                      <span className="text-sm font-medium text-green-600">Available</span>
                    </div>
                  )}
                  
                  {serviceStatus.soxAvailable && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">SoX Processing</span>
                      <span className="text-sm font-medium text-green-600">Available</span>
                    </div>
                  )}
                  
                  {!serviceStatus.soxAvailable && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">SoX Processing</span>
                      <span className="text-sm font-medium text-yellow-600">Unavailable</span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm text-gray-500">Loading status...</div>
              )}
            </motion.div>

            {/* Key Features */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-white rounded-lg shadow p-6"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">Key Features</h3>
              
              <div className="space-y-3">
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Irreversible Processing</p>
                    <p className="text-xs text-gray-600">Cannot be reversed or recovered</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Real-time Processing</p>
                    <p className="text-xs text-gray-600">Sub-100ms latency for voice calls</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Complete Tone Removal</p>
                    <p className="text-xs text-gray-600">Removes all input voice characteristics</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Anti-Forensic</p>
                    <p className="text-xs text-gray-600">Prevents voice analysis and identification</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Usage Instructions */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-blue-50 rounded-lg border border-blue-200 p-6"
            >
              <h3 className="text-lg font-medium text-blue-900 mb-4">How to Use</h3>
              
              <div className="space-y-2 text-sm text-blue-800">
                <p>1. Upload an audio file using the interface</p>
                <p>2. Select a neutralization profile (recommended: REAL_TIME_MEDIUM)</p>
                <p>3. Click "Process Audio" to neutralize the voice</p>
                <p>4. Download the processed audio file</p>
              </div>
              
              <div className="mt-4 p-3 bg-blue-100 rounded border border-blue-300">
                <p className="text-xs text-blue-700">
                  <strong>Note:</strong> All processing is irreversible. The original voice characteristics 
                  cannot be recovered from the processed audio.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(VoiceManagement);
