import { Router, Request, Response } from 'express';
import { authenticateToken } from '../middleware/auth';
import AdminBuildModel from '../models/AdminBuild';
import AuditLogModel from '../models/AuditLog';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../uploads/builds');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}-${timestamp}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow common build file types
    const allowedTypes = ['.apk', '.ipa', '.exe', '.dmg', '.zip', '.tar.gz', '.deb', '.rpm'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only build files are allowed.'));
    }
  }
});

/**
 * Get all builds with optional filtering
 * GET /api/builds
 */
router.get('/', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 10, search, platform, status } = req.query;
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Build query
    const query: any = {};
    
    if (search) {
      query.$or = [
        { version: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { changelog: { $regex: search, $options: 'i' } }
      ];
    }

    if (platform) {
      query.platform = platform;
    }

    if (status) {
      query.status = status;
    }

    const builds = await AdminBuildModel.find(query)
      .populate('createdBy', 'username email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    const total = await AdminBuildModel.countDocuments(query);

    // Log audit
    await AuditLogModel.create({
      logId: `build-list-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      event: {
        type: 'admin',
        action: 'view_builds',
        result: 'success'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system_admin',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { 
          query, 
          resultCount: builds.length,
          adminId: req.admin?.id
        }
      }
    });

    res.json({
      builds,
      pagination: {
        total,
        page: pageNum,
        pages: Math.ceil(total / limitNum),
        limit: limitNum
      }
    });
  } catch (error) {
    console.error('Error fetching builds:', error);
    res.status(500).json({ error: 'Failed to fetch builds' });
  }
});

/**
 * Get single build by ID
 * GET /api/builds/:id
 */
router.get('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    const build = await AdminBuildModel.findById(req.params.id)
      .populate('createdBy', 'username email');

    if (!build) {
      res.status(404).json({ error: 'Build not found' });
      return;
    }

    // Log audit
    await AuditLogModel.create({
      logId: `build-view-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      event: {
        type: 'admin',
        action: 'view_build',
        result: 'success'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system_admin',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { 
          buildId: build._id,
          version: build.version,
          adminId: req.admin?.id
        }
      }
    });

    res.json(build);
  } catch (error) {
    console.error('Error fetching build:', error);
    res.status(500).json({ error: 'Failed to fetch build' });
  }
});

/**
 * Create new build
 * POST /api/builds
 */
router.post('/', authenticateToken, upload.single('buildFile'), async (req: Request, res: Response) => {
  try {
    const { version, description, platform, changelog, status } = req.body;

    // Check if version already exists for this platform
    const existingBuild = await AdminBuildModel.findOne({ version, platform });
    if (existingBuild) {
      res.status(400).json({ error: `Build version ${version} already exists for ${platform}` });
      return;
    }

    const buildData: any = {
      version,
      description,
      platform,
      changelog: changelog || '',
      status: status || 'draft',
      createdBy: req.admin?.id,
      releaseDate: status === 'active' ? new Date() : null
    };

    // Handle file upload
    if (req.file) {
      buildData.downloadUrl = `/uploads/builds/${req.file.filename}`;
      buildData.fileSize = req.file.size;
      buildData.fileName = req.file.originalname;
    }

    const build = await AdminBuildModel.create(buildData);
    const populatedBuild = await AdminBuildModel.findById(build._id)
      .populate('createdBy', 'username email');

    // Log audit
    await AuditLogModel.create({
      logId: `build-create-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      event: {
        type: 'admin_action',
        action: 'create_build',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system_admin',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { 
          buildId: build._id,
          version: build.version,
          platform: build.platform,
          hasFile: !!req.file,
          adminId: req.admin?.id
        }
      }
    });

    res.status(201).json(populatedBuild);
  } catch (error) {
    console.error('Error creating build:', error);
    res.status(500).json({ error: 'Failed to create build' });
  }
});

/**
 * Update build
 * PUT /api/builds/:id
 */
router.put('/:id', authenticateToken, upload.single('buildFile'), async (req: Request, res: Response) => {
  try {
    const { version, description, platform, changelog, status } = req.body;
    const buildId = req.params.id;

    const build = await AdminBuildModel.findById(buildId);
    if (!build) {
      res.status(404).json({ error: 'Build not found' });
      return;
    }

    // Check if version already exists for this platform (excluding current build)
    if (version && (version !== build.version || platform !== build.platform)) {
      const existingBuild = await AdminBuildModel.findOne({ 
        version, 
        platform, 
        _id: { $ne: buildId } 
      });
      if (existingBuild) {
        res.status(400).json({ error: `Build version ${version} already exists for ${platform}` });
        return;
      }
    }

    const updateData: any = {
      ...(version && { version }),
      ...(description && { description }),
      ...(platform && { platform }),
      ...(changelog !== undefined && { changelog }),
      ...(status && { status }),
      updatedBy: req.admin?.id,
      updatedAt: new Date()
    };

    // Update release date if status changes to active
    if (status === 'active' && build.status !== 'active') {
      updateData.releaseDate = new Date();
    }

    // Handle file upload
    if (req.file) {
      // Delete old file if exists
      if (build.downloadUrl) {
        const oldFilePath = path.join(__dirname, '../public', build.downloadUrl);
        if (fs.existsSync(oldFilePath)) {
          fs.unlinkSync(oldFilePath);
        }
      }

      updateData.downloadUrl = `/uploads/builds/${req.file.filename}`;
      updateData.fileSize = req.file.size;
      updateData.fileName = req.file.originalname;
    }

    const updatedBuild = await AdminBuildModel.findByIdAndUpdate(
      buildId,
      updateData,
      { new: true }
    ).populate('createdBy', 'username email');

    // Log audit
    await AuditLogModel.create({
      logId: `build-update-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      event: {
        type: 'admin_action',
        action: 'update_build',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system_admin',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { 
          buildId: build._id,
          version: build.version,
          changes: updateData,
          hasNewFile: !!req.file,
          adminId: req.admin?.id
        }
      }
    });

    res.json(updatedBuild);
  } catch (error) {
    console.error('Error updating build:', error);
    res.status(500).json({ error: 'Failed to update build' });
  }
});

/**
 * Delete build
 * DELETE /api/builds/:id
 */
router.delete('/:id', authenticateToken, async (req: Request, res: Response) => {
  try {
    const buildId = req.params.id;

    const build = await AdminBuildModel.findById(buildId);
    if (!build) {
      res.status(404).json({ error: 'Build not found' });
      return;
    }

    // Delete associated file if exists
    if (build.downloadUrl) {
      const filePath = path.join(__dirname, '../public', build.downloadUrl);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }

    await AdminBuildModel.findByIdAndDelete(buildId);

    // Log audit
    await AuditLogModel.create({
      logId: `build-delete-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      event: {
        type: 'admin_action',
        action: 'delete_build',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system_admin',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { 
          buildId: build._id,
          version: build.version,
          platform: build.platform,
          adminId: req.admin?.id
        }
      }
    });

    res.json({ message: 'Build deleted successfully' });
  } catch (error) {
    console.error('Error deleting build:', error);
    res.status(500).json({ error: 'Failed to delete build' });
  }
});

/**
 * Update build status
 * PATCH /api/builds/:id/status
 */
router.patch('/:id/status', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { status } = req.body;
    const buildId = req.params.id;

    if (!['draft', 'active', 'archived'].includes(status)) {
      res.status(400).json({ error: 'Invalid status' });
      return;
    }

    const build = await AdminBuildModel.findById(buildId);
    if (!build) {
      res.status(404).json({ error: 'Build not found' });
      return;
    }

    const updateData: any = { 
      status,
      updatedBy: req.admin?.id,
      updatedAt: new Date()
    };

    // Update release date if status changes to active
    if (status === 'active' && build.status !== 'active') {
      updateData.releaseDate = new Date();
    }

    const updatedBuild = await AdminBuildModel.findByIdAndUpdate(
      buildId,
      updateData,
      { new: true }
    ).populate('createdBy', 'username email');

    // Log audit
    await AuditLogModel.create({
      logId: `build-status-update-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      event: {
        type: 'admin_action',
        action: 'update_build_status',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system_admin',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { 
          buildId: build._id,
          version: build.version,
          oldStatus: build.status,
          newStatus: status,
          adminId: req.admin?.id
        }
      }
    });

    res.json(updatedBuild);
  } catch (error) {
    console.error('Error updating build status:', error);
    res.status(500).json({ error: 'Failed to update build status' });
  }
});

/**
 * Download build file
 * GET /api/builds/:id/download
 */
router.get('/:id/download', authenticateToken, async (req: Request, res: Response) => {
  try {
    const build = await AdminBuildModel.findById(req.params.id);
    if (!build) {
      res.status(404).json({ error: 'Build not found' });
      return;
    }

    if (!build.downloadUrl) {
      res.status(404).json({ error: 'No file available for download' });
      return;
    }

    const filePath = path.join(__dirname, '../public', build.downloadUrl);
    if (!fs.existsSync(filePath)) {
      res.status(404).json({ error: 'Build file not found' });
      return;
    }

    // Increment download count
    await AdminBuildModel.findByIdAndUpdate(build._id, {
      $inc: { downloads: 1 }
    });

    // Log audit
    await AuditLogModel.create({
      logId: `build-download-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      event: {
        type: 'admin_action',
        action: 'download_build',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system_admin',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { 
          buildId: build._id,
          version: build.version,
          platform: build.platform,
          adminId: req.admin?.id
        }
      }
    });

    res.download(filePath, build.fileName || `${build.version}-${build.platform}`);
  } catch (error) {
    console.error('Error downloading build:', error);
    res.status(500).json({ error: 'Failed to download build' });
  }
});

export default router;
