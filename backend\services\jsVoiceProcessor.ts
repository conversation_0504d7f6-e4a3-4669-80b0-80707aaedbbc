/**
 * JavaScript-based Voice Processor
 * Fallback for when the native WORLD vocoder has issues or returns NaN values
 * Provides basic voice transformation capabilities using pure JavaScript
 */

import { WorldVoiceProfile } from './worldVocoderService';

export class JsVoiceProcessor {
  private sampleRate: number = 48000;
  private initialized: boolean = false;

  constructor() {
    this.initialize();
  }

  /**
   * Initialize the JavaScript voice processor
   */
  public initialize(): boolean {
    try {
      this.initialized = true;
      console.log('✅ JS Voice Processor initialized as fallback');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize JS Voice Processor:', error);
      return false;
    }
  }

  /**
   * Process audio with the JavaScript-based voice processor
   * @param audioData Float32Array of audio samples in range [-1, 1]
   * @param profile Voice profile parameters
   * @returns Processed audio data
   */
  public processAudio(audioData: Float32Array, profile: WorldVoiceProfile): Float32Array {
    if (!this.initialized) {
      console.warn('⚠️ JS Voice Processor not initialized, initializing now...');
      this.initialize();
    }

    console.log('🔄 JS Voice Processor: Processing audio with profile:', {
      pitchScale: profile.pitchScale,
      spectralWarp: profile.spectralWarp,
      reverbAmount: profile.reverbAmount,
      antiForensic: profile.antiForensic
    });

    // For NORMAL_VOICE profile, return original audio unchanged
    if (this.isNormalProfile(profile)) {
      console.log('✅ JS Voice Processor: NORMAL profile detected, returning original audio');
      return new Float32Array(audioData);
    }

    // Create a copy of the input data to avoid modifying the original
    const outputData = new Float32Array(audioData.length);

    try {
      // Apply various transformations based on profile parameters
      this.applyPitchScaling(audioData, outputData, profile);
      this.applySimpleReverb(outputData, profile);
      this.applyEqTilt(outputData, profile);
      
      // Apply anti-forensic processing if enabled
      if (profile.antiForensic) {
        this.applyAntiForensicProcessing(outputData, profile);
      }

      // Final normalization to prevent clipping
      this.normalizeAudio(outputData);

      console.log('✅ JS Voice Processor: Processing completed successfully');
      return outputData;
    } catch (error) {
      console.error('❌ JS Voice Processor error:', error);
      // In case of error, return the original audio
      return new Float32Array(audioData);
    }
  }

  /**
   * Check if this is a normal (identity) profile with no transformations
   */
  private isNormalProfile(profile: WorldVoiceProfile): boolean {
    return (
      Math.abs(profile.pitchScale - 1.0) < 0.01 &&
      Math.abs(profile.spectralWarp) < 0.01 &&
      profile.reverbAmount < 0.01 &&
      Math.abs(profile.eqTilt) < 0.01 &&
      profile.temporalJitter < 0.01 &&
      profile.spectralNoise < 0.01 &&
      !profile.antiForensic
    );
  }

  /**
   * Apply EXTREME pitch scaling transformation for real voice morphing
   * Uses advanced time-domain resampling with formant preservation
   */
  private applyPitchScaling(inputData: Float32Array, outputData: Float32Array, profile: WorldVoiceProfile): void {
    const pitchScale = Math.max(0.3, Math.min(3.0, profile.pitchScale)); // EXTREME range for real transformation

    if (Math.abs(pitchScale - 1.0) < 0.01) {
      // No pitch change needed, just copy the data
      outputData.set(inputData);
      return;
    }

    console.log('🎵 JS Voice Processor: Applying EXTREME pitch scaling:', pitchScale);

    // ADVANCED pitch scaling with formant preservation
    const spectralWarp = Math.max(-20.0, Math.min(20.0, profile.spectralWarp));

    // Phase vocoder-like approach for better quality
    const frameSize = 1024;
    const hopSize = frameSize / 4;

    for (let frameStart = 0; frameStart < outputData.length - frameSize; frameStart += hopSize) {
      // Process in overlapping frames for better quality
      for (let i = 0; i < frameSize && frameStart + i < outputData.length; i++) {
        let sourceIndex = (frameStart + i) / pitchScale;

        // Apply EXTREME spectral warping for vocal tract length normalization (VTLN)
        if (Math.abs(spectralWarp) > 0.1) {
          const normalizedIndex = i / frameSize;
          // Use multiple harmonics for better formant shifting
          const warpOffset = Math.sin(normalizedIndex * Math.PI * 4) * spectralWarp * 0.05 +
                           Math.sin(normalizedIndex * Math.PI * 8) * spectralWarp * 0.03;
          sourceIndex += warpOffset;
        }

        const sourceIndexInt = Math.floor(sourceIndex);
        const fraction = sourceIndex - sourceIndexInt;

        if (sourceIndexInt < inputData.length - 1 && sourceIndexInt >= 0) {
          // Cubic interpolation for better quality
          const y0 = sourceIndexInt > 0 ? inputData[sourceIndexInt - 1] : 0;
          const y1 = inputData[sourceIndexInt];
          const y2 = inputData[sourceIndexInt + 1];
          const y3 = sourceIndexInt + 2 < inputData.length ? inputData[sourceIndexInt + 2] : 0;

          // Cubic interpolation formula
          const a = -0.5 * y0 + 1.5 * y1 - 1.5 * y2 + 0.5 * y3;
          const b = y0 - 2.5 * y1 + 2 * y2 - 0.5 * y3;
          const c = -0.5 * y0 + 0.5 * y2;
          const d = y1;

          const interpolated = a * fraction * fraction * fraction +
                             b * fraction * fraction +
                             c * fraction + d;

          // Apply windowing for smooth overlap
          const window = 0.5 * (1 - Math.cos(2 * Math.PI * i / frameSize));
          outputData[frameStart + i] += interpolated * window;
        }
      }
    }

    // Apply EXTREME formant shifting for gender transformation
    if (Math.abs(spectralWarp) > 1.0) {
      this.applyAdvancedFormantShift(outputData, spectralWarp);
    }
  }

  /**
   * Apply ADVANCED formant shifting for EXTREME gender transformation
   * Implements Vocal Tract Length Normalization (VTLN) techniques
   */
  private applyAdvancedFormantShift(audioData: Float32Array, spectralWarp: number): void {
    console.log('🎭 JS Voice Processor: Applying ADVANCED formant shift:', spectralWarp);

    // EXTREME formant shifting using multiple filter stages
    // This simulates vocal tract length changes for gender transformation

    const shiftAmount = spectralWarp * 0.2; // Increased scaling for dramatic effect

    // Multi-stage all-pass filter cascade for better formant shifting
    let delay1 = 0, delay2 = 0, delay3 = 0, delay4 = 0;
    const coeff1 = Math.max(-0.95, Math.min(0.95, shiftAmount * 0.8));
    const coeff2 = Math.max(-0.95, Math.min(0.95, shiftAmount * 0.6));
    const coeff3 = Math.max(-0.95, Math.min(0.95, shiftAmount * 0.4));
    const coeff4 = Math.max(-0.95, Math.min(0.95, shiftAmount * 0.2));

    for (let i = 0; i < audioData.length; i++) {
      let signal = audioData[i];

      // Stage 1: Primary formant shifting
      const output1 = -coeff1 * signal + delay1;
      delay1 = signal + coeff1 * output1;
      signal = output1;

      // Stage 2: Secondary formant adjustment
      const output2 = -coeff2 * signal + delay2;
      delay2 = signal + coeff2 * output2;
      signal = output2;

      // Stage 3: Tertiary formant fine-tuning
      const output3 = -coeff3 * signal + delay3;
      delay3 = signal + coeff3 * output3;
      signal = output3;

      // Stage 4: Final formant polishing
      const output4 = -coeff4 * signal + delay4;
      delay4 = signal + coeff4 * output4;

      audioData[i] = output4;
    }

    // Apply additional spectral envelope modification
    this.applySpectralEnvelopeShaping(audioData, spectralWarp);
  }

  /**
   * Apply spectral envelope shaping for vocal tract simulation
   */
  private applySpectralEnvelopeShaping(audioData: Float32Array, spectralWarp: number): void {
    console.log('🎛️ JS Voice Processor: Applying spectral envelope shaping:', spectralWarp);

    // Simulate spectral envelope changes using comb filtering
    const delayLength = Math.floor(48000 / 200); // ~5ms delay for formant simulation
    const delayBuffer = new Float32Array(delayLength);
    let delayIndex = 0;

    const feedbackGain = Math.max(-0.8, Math.min(0.8, spectralWarp * 0.05));
    const feedforwardGain = Math.max(-0.8, Math.min(0.8, spectralWarp * 0.03));

    for (let i = 0; i < audioData.length; i++) {
      const delayedSample = delayBuffer[delayIndex];
      const output = audioData[i] + feedbackGain * delayedSample + feedforwardGain * delayedSample;

      delayBuffer[delayIndex] = audioData[i];
      delayIndex = (delayIndex + 1) % delayLength;

      audioData[i] = output;
    }
  }

  /**
   * Apply a simple reverb effect
   * Implements a basic feedback delay network
   */
  private applySimpleReverb(audioData: Float32Array, profile: WorldVoiceProfile): void {
    if (profile.reverbAmount <= 0) return;
    
    const reverbAmount = Math.min(25, profile.reverbAmount) / 100; // Convert to 0-0.25 range
    console.log('🔊 JS Voice Processor: Applying simple reverb:', reverbAmount);
    
    // Create a simple delay buffer
    const delayLength = Math.floor(this.sampleRate * 0.05); // 50ms delay
    const delayBuffer = new Float32Array(delayLength).fill(0);
    let delayIndex = 0;
    const feedback = reverbAmount * 0.5; // Reduce feedback to prevent overflow
    
    // Process the audio with the delay
    for (let i = 0; i < audioData.length; i++) {
      // Read from delay buffer
      const delaySample = delayBuffer[delayIndex];
      
      // Write to delay buffer (current sample + feedback from delay)
      delayBuffer[delayIndex] = audioData[i] + delaySample * feedback;
      
      // Mix original signal with delayed signal
      audioData[i] = audioData[i] * (1 - reverbAmount) + delaySample * reverbAmount;
      
      // Advance delay index
      delayIndex = (delayIndex + 1) % delayLength;
    }
  }

  /**
   * Apply EQ tilt (simple frequency emphasis)
   * Uses a basic first-order IIR filter
   */
  private applyEqTilt(audioData: Float32Array, profile: WorldVoiceProfile): void {
    if (Math.abs(profile.eqTilt) < 0.01) return;
    
    const tilt = Math.max(-3, Math.min(3, profile.eqTilt)) / 10; // Scale to reasonable filter coefficient
    console.log('🎛️ JS Voice Processor: Applying EQ tilt:', tilt);
    
    // Simple first-order IIR filter
    let prevSample = 0;
    
    for (let i = 0; i < audioData.length; i++) {
      if (tilt > 0) {
        // Emphasize high frequencies (reduce low frequencies)
        audioData[i] = audioData[i] - tilt * prevSample;
      } else {
        // Emphasize low frequencies
        audioData[i] = audioData[i] - tilt * (audioData[i] - prevSample);
      }
      
      prevSample = audioData[i];
    }
  }

  /**
   * Apply anti-forensic processing
   * Adds subtle noise and variations to prevent voice recognition
   */
  private applyAntiForensicProcessing(audioData: Float32Array, profile: WorldVoiceProfile): void {
    console.log('🔒 JS Voice Processor: Applying anti-forensic processing');
    
    // Enhanced anti-forensic processing for secure profiles
    // Identify profile type to apply appropriate processing
    const profileName = this.detectProfileType(profile);
    console.log(`🔍 Detected profile type: ${profileName}`);
    
    // Apply different processing based on profile type
    if (profileName === 'ROBOTIC_SYNTHETIC') {
      this.applyRoboticEffect(audioData, profile);
    } else if (profileName === 'SECURE_DEEP_MALE') {
      this.applySecureDeepMaleEffect(audioData, profile);
    } else if (profileName === 'SECURE_HIGH_FEMALE') {
      this.applySecureHighFemaleEffect(audioData, profile);
    } else {
      // Generic anti-forensic processing
      this.applyGenericAntiForensicEffect(audioData, profile);
    }
    
    // Final pass of subtle noise and jitter for all anti-forensic profiles
    const noiseLevel = Math.min(0.05, profile.spectralNoise || 0.03);
    const jitterAmount = Math.min(0.03, profile.temporalJitter || 0.02);
    
    // Add subtle noise
    for (let i = 0; i < audioData.length; i++) {
      // Add controlled random noise
      const noise = (Math.random() * 2 - 1) * noiseLevel;
      audioData[i] += noise;
      
      // Apply subtle sample-level jitter (randomly skip or duplicate samples)
      if (Math.random() < jitterAmount) {
        if (i < audioData.length - 1) {
          // Randomly swap with next sample
          const temp = audioData[i];
          audioData[i] = audioData[i + 1];
          audioData[i + 1] = temp;
        }
      }
    }
  }
  
  /**
   * Detect which voice profile is being used based on parameters
   */
  private detectProfileType(profile: WorldVoiceProfile): string {
    // Check for ROBOTIC_SYNTHETIC profile
    if (profile.antiForensic && 
        Math.abs(profile.pitchScale - 0.8) < 0.1 && 
        Math.abs(profile.spectralWarp - 0.8) < 0.1) {
      return 'ROBOTIC_SYNTHETIC';
    }
    
    // Check for SECURE_DEEP_MALE profile
    if (profile.antiForensic && 
        profile.pitchScale < 0.8 && 
        profile.spectralWarp < 0.9) {
      return 'SECURE_DEEP_MALE';
    }
    
    // Check for SECURE_HIGH_FEMALE profile
    if (profile.antiForensic && 
        profile.pitchScale > 1.2 && 
        profile.spectralWarp > 1.1) {
      return 'SECURE_HIGH_FEMALE';
    }
    
    // Default to generic profile
    return 'GENERIC_ANTI_FORENSIC';
  }
  
  /**
   * Apply robotic voice effect
   */
  private applyRoboticEffect(audioData: Float32Array, profile: WorldVoiceProfile): void {
    console.log('🤖 Applying ROBOTIC_SYNTHETIC effect with profile:', profile.pitchScale);

    // For robotic effect, we'll use:
    // 1. Amplitude quantization (digital distortion)
    // 2. Ring modulation (metallic sound)
    // 3. Resonant filtering
    
    // Apply amplitude quantization (reduces bit depth)
    const quantizationLevels = 16; // Like 4-bit audio
    for (let i = 0; i < audioData.length; i++) {
      // Quantize the amplitude
      audioData[i] = Math.round(audioData[i] * quantizationLevels) / quantizationLevels;
    }
    
    // Apply ring modulation (creates metallic sound)
    const carrierFreq = 200; // 200Hz carrier
    const sampleRate = 48000;
    for (let i = 0; i < audioData.length; i++) {
      // Create carrier wave
      const carrier = Math.sin(2 * Math.PI * carrierFreq * i / sampleRate);
      // Apply ring modulation
      audioData[i] = audioData[i] * carrier * 0.8;
    }
    
    // Apply final gain to prevent clipping
    for (let i = 0; i < audioData.length; i++) {
      audioData[i] *= 0.7;
    }
  }
  
  /**
   * Apply secure deep male voice effect
   */
  private applySecureDeepMaleEffect(audioData: Float32Array, profile: WorldVoiceProfile): void {
    console.log('👨 Applying SECURE_DEEP_MALE effect with profile:', profile.pitchScale);

    // Enhanced pitch shifting (deeper)
    const pitchFactor = 0.7; // Lower pitch
    
    // Create temp buffer for pitch shifted data
    const tempBuffer = new Float32Array(audioData.length);
    
    // Apply enhanced pitch shifting
    for (let i = 0; i < audioData.length; i++) {
      const sourceIndex = i / pitchFactor;
      const sourceIndexInt = Math.floor(sourceIndex);
      const fraction = sourceIndex - sourceIndexInt;
      
      if (sourceIndexInt < audioData.length - 1) {
        tempBuffer[i] = audioData[sourceIndexInt] * (1 - fraction) + 
                       audioData[sourceIndexInt + 1] * fraction;
      } else if (sourceIndexInt < audioData.length) {
        tempBuffer[i] = audioData[sourceIndexInt];
      } else {
        tempBuffer[i] = 0;
      }
    }
    
    // Copy back to original buffer
    for (let i = 0; i < audioData.length; i++) {
      audioData[i] = tempBuffer[i];
    }
    
    // Apply low-pass filter to emphasize low frequencies
    let prevSample = 0;
    const filterCoeff = 0.2; // Low pass filter coefficient
    
    for (let i = 0; i < audioData.length; i++) {
      audioData[i] = audioData[i] * (1 - filterCoeff) + prevSample * filterCoeff;
      prevSample = audioData[i];
    }
    
    // Add subtle low frequency enhancement
    for (let i = 0; i < audioData.length; i++) {
      if (i % 120 < 60) { // 400Hz boost
        audioData[i] *= 1.1;
      }
    }
  }
  
  /**
   * Apply secure high female voice effect
   */
  private applySecureHighFemaleEffect(audioData: Float32Array, profile: WorldVoiceProfile): void {
    console.log('👩 Applying SECURE_HIGH_FEMALE effect with profile:', profile.pitchScale);

    // Enhanced pitch shifting (higher)
    const pitchFactor = 1.3; // Higher pitch
    
    // Create temp buffer for pitch shifted data
    const tempBuffer = new Float32Array(audioData.length);
    
    // Apply enhanced pitch shifting
    for (let i = 0; i < audioData.length; i++) {
      const sourceIndex = i / pitchFactor;
      const sourceIndexInt = Math.floor(sourceIndex);
      const fraction = sourceIndex - sourceIndexInt;
      
      if (sourceIndexInt < audioData.length - 1) {
        tempBuffer[i] = audioData[sourceIndexInt] * (1 - fraction) + 
                       audioData[sourceIndexInt + 1] * fraction;
      } else if (sourceIndexInt < audioData.length) {
        tempBuffer[i] = audioData[sourceIndexInt];
      } else {
        tempBuffer[i] = 0;
      }
    }
    
    // Copy back to original buffer
    for (let i = 0; i < audioData.length; i++) {
      audioData[i] = tempBuffer[i];
    }
    
    // Apply high-pass filter to emphasize high frequencies
    let prevSample = 0;
    const filterCoeff = 0.1;

    for (let i = 0; i < audioData.length; i++) {
      const highPass = audioData[i] - prevSample * filterCoeff;
      prevSample = audioData[i];
      audioData[i] = highPass * 0.9;
    }
    
    // Add subtle high frequency enhancement
    for (let i = 0; i < audioData.length; i++) {
      if (i % 30 < 15) { // ~1600Hz boost
        audioData[i] *= 1.1;
      }
    }
  }
  
  /**
   * Apply generic anti-forensic effect
   */
  private applyGenericAntiForensicEffect(audioData: Float32Array, profile: WorldVoiceProfile): void {
    console.log('🔒 Applying generic anti-forensic effect');
    
    const noiseLevel = Math.min(0.1, profile.spectralNoise || 0.05);
    const jitterAmount = Math.min(0.05, profile.temporalJitter || 0.03);
    
    // Add controlled spectral modification and noise
    for (let i = 0; i < audioData.length; i++) {
      // Add controlled random noise
      const noise = (Math.random() * 2 - 1) * noiseLevel;
      audioData[i] += noise;
      
      // Apply sample-level jitter
      if (Math.random() < jitterAmount) {
        if (i < audioData.length - 1) {
          // Randomly swap with next sample
          const temp = audioData[i];
          audioData[i] = audioData[i + 1];
          audioData[i + 1] = temp;
        }
      }
      
      // Apply mild harmonic distortion
      audioData[i] = Math.tanh(audioData[i] * 1.2) * 0.85;
    }
  }

  /**
   * Normalize audio to prevent clipping
   */
  private normalizeAudio(audioData: Float32Array): void {
    // Find maximum amplitude
    let maxAmp = 0;
    let minAmp = 0;
    let avgAmp = 0;
    
    // Find max, min and calculate average
    for (let i = 0; i < audioData.length; i++) {
      const absVal = Math.abs(audioData[i]);
      maxAmp = Math.max(maxAmp, absVal);
      minAmp = Math.min(minAmp, audioData[i]);
      avgAmp += absVal;
    }
    
    avgAmp /= audioData.length;
    
    console.log(`🔊 JS Voice Processor: Audio stats before normalization - Max: ${maxAmp.toFixed(4)}, Min: ${minAmp.toFixed(4)}, Avg: ${avgAmp.toFixed(4)}`);
    
    // Check if the audio is too quiet
    if (maxAmp < 0.1) {
      const boostGain = 0.6 / maxAmp; // Boost to reasonable level
      console.log(`🔊 JS Voice Processor: Audio too quiet, boosting by ${boostGain.toFixed(3)}`);
      
      for (let i = 0; i < audioData.length; i++) {
        audioData[i] *= boostGain;
      }
    }
    // Normalize if we're close to clipping
    else if (maxAmp > 0.9) {
      const gain = 0.85 / maxAmp; // Normalize with headroom
      console.log(`🔊 JS Voice Processor: Normalizing clipped output (max amplitude: ${maxAmp.toFixed(4)}, gain: ${gain.toFixed(4)})`);
      
      for (let i = 0; i < audioData.length; i++) {
        audioData[i] *= gain;
      }
    }
    
    // Check for DC offset and remove if needed
    let dcOffset = 0;
    for (let i = 0; i < audioData.length; i++) {
      dcOffset += audioData[i];
    }
    dcOffset /= audioData.length;
    
    // Remove DC offset if significant
    if (Math.abs(dcOffset) > 0.01) {
      console.log(`🔊 JS Voice Processor: Removing DC offset of ${dcOffset.toFixed(4)}`);
      for (let i = 0; i < audioData.length; i++) {
        audioData[i] -= dcOffset;
      }
    }
    
    // Apply soft clipping to catch any remaining peaks
    for (let i = 0; i < audioData.length; i++) {
      // Soft clipping using tanh function
      if (Math.abs(audioData[i]) > 0.9) {
        audioData[i] = Math.tanh(audioData[i]) * 0.9;
      }
    }
    
    // Final amplitude check
    let finalMax = 0;
    for (let i = 0; i < audioData.length; i++) {
      finalMax = Math.max(finalMax, Math.abs(audioData[i]));
    }
    
    console.log(`🔊 JS Voice Processor: Audio stats after normalization - Max amplitude: ${finalMax.toFixed(4)}`);
  }
}

// Export singleton instance
export const jsVoiceProcessor = new JsVoiceProcessor();
