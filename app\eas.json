{"cli": {"version": ">= 13.0.0", "appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "env": {"EXPO_PUBLIC_BUILD_ENV": "development"}, "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}, "android": {"resourceClass": "medium"}}, "preview": {"distribution": "internal", "env": {"EXPO_PUBLIC_BUILD_ENV": "preview"}, "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}, "android": {"resourceClass": "medium"}}, "production": {"env": {"EXPO_PUBLIC_BUILD_ENV": "production"}, "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}, "android": {"resourceClass": "medium"}}, "github-ci": {"extends": "production", "distribution": "internal", "env": {"EXPO_PUBLIC_BUILD_ENV": "github-ci"}, "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release"}}}, "submit": {"production": {}}}