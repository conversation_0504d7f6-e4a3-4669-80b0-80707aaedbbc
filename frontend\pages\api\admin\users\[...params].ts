// Next.js API route to proxy admin user detail requests to backend
import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';
  
  // Reconstruct the full path from params
  const { params } = req.query;
  const pathSegments = Array.isArray(params) ? params : [params];
  const fullPath = pathSegments.join('/');
  
  const url = `${backendUrl}/api/admin/users/${fullPath}`;

  try {
    const response = await axios({
      method: req.method,
      url,
      headers: {
        ...req.headers,
        host: undefined, // Remove host header for backend
      },
      data: req.body,
      params: req.query.params ? undefined : req.query, // Avoid double params
      validateStatus: () => true,
    });
    
    res.status(response.status).json(response.data);
  } catch (error: any) {
    console.error('Admin user proxy error:', error.message);
    res.status(500).json({ 
      error: 'Proxy error', 
      details: error.message,
      url: url.replace(process.env.NEXT_PUBLIC_BACKEND_URL || '', '[BACKEND_URL]')
    });
  }
}
