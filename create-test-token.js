// Create a valid JWT token for testing
const jwt = require('jsonwebtoken');

const secret = 'your-very-secure-jwt-secret-for-development'; // This matches the .env file

const testPayload = {
  id: '1234567890',
  role: 'admin',
  iat: Math.floor(Date.now() / 1000)
};

const token = jwt.sign(testPayload, secret, { expiresIn: '1h' });

console.log('Test JWT Token:');
console.log(token);
console.log('');
console.log('Use this token in the Authorization header as:');
console.log(`Bearer ${token}`);
