/**
 * Voice System Test Script
 * Quick test to verify the WORLD vocoder system is working
 */

const path = require('path');

// Set up TypeScript compilation for testing
require('ts-node').register({
  project: path.join(__dirname, '..', 'tsconfig.json')
});

async function testVoiceSystem() {
  console.log('🧪 Testing CCALC Voice System...');
  console.log('================================');

  try {
    // Import the services
    const { worldVocoderService } = require('../services/worldVocoderService');
    const { voiceSecurityService } = require('../services/voiceSecurityService');
    const { performanceMonitor } = require('../services/performanceMonitor');

    console.log('✅ All voice services imported successfully');

    // Test 1: Check if WORLD vocoder service is available
    console.log('\n📋 Test 1: Service Availability');
    const isAvailable = worldVocoderService.isAvailable();
    const isNativeAvailable = worldVocoderService.isNativeAvailable();
    
    console.log(`   Voice Processing: ${isAvailable ? '✅ Available' : '❌ Not Available'}`);
    console.log(`   Native WORLD Vocoder: ${isNativeAvailable ? '✅ Available' : '🔄 Fallback Mode'}`);

    if (!isAvailable) {
      throw new Error('Voice processing not available');
    }

    // Test 2: Create a voice processing session
    console.log('\n📋 Test 2: Session Creation');
    const sessionId = await worldVocoderService.createSession('test-user', {
      sampleRate: 48000,
      frameSize: 960,
      qualityLevel: 'balanced'
    });

    console.log(`   Session ID: ${sessionId}`);
    console.log('   ✅ Voice processing session created successfully');

    // Test 3: Process audio frame
    console.log('\n📋 Test 3: Audio Processing');
    const testAudio = new Float32Array(960);
    for (let i = 0; i < testAudio.length; i++) {
      testAudio[i] = Math.sin(2 * Math.PI * 440 * i / 48000) * 0.5; // 440Hz sine wave
    }

    const { WORLD_VOICE_PROFILES } = require('../services/worldVocoderService');
    const profile = WORLD_VOICE_PROFILES.SECURE_DEEP_MALE;
    
    const startTime = Date.now();
    const processedAudio = await worldVocoderService.processAudioFrame(
      sessionId,
      testAudio,
      profile
    );
    const processingTime = Date.now() - startTime;

    if (processedAudio) {
      console.log(`   ✅ Audio processed successfully in ${processingTime}ms`);
      console.log(`   Input length: ${testAudio.length}, Output length: ${processedAudio.length}`);
      console.log(`   Processing mode: ${isNativeAvailable ? 'Native' : 'Fallback'}`);
    } else {
      throw new Error('Audio processing failed');
    }

    // Test 4: Security features
    console.log('\n📋 Test 4: Security Features');
    const securitySessionId = await voiceSecurityService.createSecuritySession('test-user');
    const securityResult = await voiceSecurityService.applySecurityTransformations(
      securitySessionId,
      processedAudio
    );

    if (securityResult) {
      console.log('   ✅ Security transformations applied successfully');
      console.log(`   Security processing time: ${securityResult.processingTime}ms`);
      
      const verification = await voiceSecurityService.verifyAudioSecurity(
        securitySessionId,
        securityResult.securedAudio,
        securityResult.securityMetadata
      );
      
      console.log(`   Security verification: ${verification.isSecure ? '✅ Secure' : '❌ Not Secure'}`);
      console.log(`   Reversibility risk: ${Math.round(verification.reversibilityRisk * 100)}%`);
      console.log(`   Forensic resistance: ${verification.forensicResistance}%`);
    } else {
      console.log('   ⚠️ Security transformations not available');
    }

    // Test 5: Performance monitoring
    console.log('\n📋 Test 5: Performance Monitoring');
    performanceMonitor.recordLatency('worldProcessing', processingTime);
    performanceMonitor.recordLatency('securityProcessing', securityResult?.processingTime || 0);
    performanceMonitor.recordQuality('audioQuality', 85);
    performanceMonitor.recordQuality('morphingAccuracy', 90);

    const metrics = performanceMonitor.getCurrentMetrics();
    console.log(`   ✅ Performance monitoring active`);
    console.log(`   End-to-end latency: ${metrics.latency.endToEnd}ms`);
    console.log(`   Audio quality score: ${metrics.quality.audioQuality}`);

    // Test 6: Voice profiles
    console.log('\n📋 Test 6: Voice Profiles');
    const profileNames = Object.keys(WORLD_VOICE_PROFILES);
    console.log(`   ✅ ${profileNames.length} voice profiles available:`);
    profileNames.forEach(name => {
      console.log(`      - ${name.replace(/_/g, ' ')}`);
    });

    // Cleanup
    console.log('\n🧹 Cleanup');
    worldVocoderService.destroySession(sessionId);
    voiceSecurityService.destroySecuritySession(securitySessionId);
    console.log('   ✅ Sessions cleaned up');

    // Final results
    console.log('\n🎉 Voice System Test Results');
    console.log('============================');
    console.log('✅ All tests passed successfully!');
    console.log('');
    console.log('📊 System Status:');
    console.log(`   Voice Processing: ${isAvailable ? 'OPERATIONAL' : 'OFFLINE'}`);
    console.log(`   Native WORLD Vocoder: ${isNativeAvailable ? 'ENABLED' : 'FALLBACK MODE'}`);
    console.log(`   Security Features: ENABLED`);
    console.log(`   Performance Monitoring: ACTIVE`);
    console.log(`   Voice Profiles: ${profileNames.length} AVAILABLE`);
    console.log('');
    console.log('🚀 System ready for voice calls!');

  } catch (error) {
    console.error('\n❌ Voice System Test Failed:');
    console.error('============================');
    console.error(error.message);
    console.error('');
    console.error('🔧 Troubleshooting:');
    console.error('   1. Make sure all dependencies are installed: npm install');
    console.error('   2. Run voice system setup: npm run setup:voice');
    console.error('   3. Check TypeScript compilation: npm run build');
    console.error('   4. For native WORLD vocoder: npm run build:world');
    process.exit(1);
  }
}

// Run the test
testVoiceSystem().catch(console.error);
