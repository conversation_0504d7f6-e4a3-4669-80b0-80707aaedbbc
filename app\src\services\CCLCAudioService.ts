/**
 * Enhanced Audio Service using expo-av
 * Replacement for react-native-audio-recorder-player
 * Maintains compatibility with CCALC voice modulation system
 */

import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';

interface RecordingStatus {
  canRecord: boolean;
  isRecording: boolean;
  isDoneRecording: boolean;
  durationMillis: number;
}

interface PlaybackStatus {
  isLoaded: boolean;
  isPlaying: boolean;
  positionMillis: number;
  durationMillis: number;
  shouldPlay: boolean;
  volume: number;
}

class CCLCAudioService {
  private recording: Audio.Recording | null = null;
  private sound: Audio.Sound | null = null;
  private recordingUri: string | null = null;

  constructor() {
    this.initializeAudio();
  }

  private async initializeAudio() {
    try {
      // Request permissions
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Audio permissions not granted');
      }

      // Configure audio mode for recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: true,
      });
    } catch (error) {
      console.error('Failed to initialize audio:', error);
    }
  }

  /**
   * Start recording audio with high quality settings
   * Compatible with CCALC voice modulation requirements
   */
  async startRecording(): Promise<string> {
    try {
      // Stop any existing recording
      if (this.recording) {
        await this.stopRecording();
      }

      // Create new recording with high quality settings
      this.recording = new Audio.Recording();
      await this.recording.prepareToRecordAsync({
        android: {
          extension: '.wav',
          outputFormat: Audio.AndroidOutputFormat.DEFAULT,
          audioEncoder: Audio.AndroidAudioEncoder.DEFAULT,
          sampleRate: 44100,
          numberOfChannels: 1,
          bitRate: 128000,
        },
        ios: {
          extension: '.wav',
          outputFormat: Audio.IOSOutputFormat.LINEARPCM,
          audioQuality: Audio.IOSAudioQuality.HIGH,
          sampleRate: 44100,
          numberOfChannels: 1,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
        web: {
          mimeType: 'audio/webm',
          bitsPerSecond: 128000,
        },
      });

      await this.recording.startAsync();
      this.recordingUri = this.recording.getURI();
      
      console.log('✅ Recording started successfully');
      return this.recordingUri || '';
    } catch (error) {
      console.error('Failed to start recording:', error);
      throw error;
    }
  }

  /**
   * Stop recording and return the file URI
   */
  async stopRecording(): Promise<string> {
    try {
      if (!this.recording) {
        throw new Error('No active recording');
      }

      await this.recording.stopAndUnloadAsync();
      const uri = this.recording.getURI();
      this.recording = null;

      console.log('✅ Recording stopped, file saved at:', uri);
      return uri || '';
    } catch (error) {
      console.error('Failed to stop recording:', error);
      throw error;
    }
  }

  /**
   * Get current recording status
   */
  async getRecordingStatus(): Promise<RecordingStatus> {
    if (!this.recording) {
      return {
        canRecord: true,
        isRecording: false,
        isDoneRecording: false,
        durationMillis: 0,
      };
    }

    const status = await this.recording.getStatusAsync();
    return {
      canRecord: status.canRecord,
      isRecording: status.isRecording,
      isDoneRecording: status.isDoneRecording,
      durationMillis: status.durationMillis || 0,
    };
  }

  /**
   * Play audio file with volume control
   */
  async startPlayer(audioPath: string, volume: number = 1.0): Promise<void> {
    try {
      // Stop any existing playback
      if (this.sound) {
        await this.sound.unloadAsync();
      }

      // Load and play the audio file
      const { sound } = await Audio.Sound.createAsync(
        { uri: audioPath },
        { 
          shouldPlay: true,
          volume,
          isLooping: false,
        }
      );

      this.sound = sound;
      console.log('✅ Audio playback started');
    } catch (error) {
      console.error('Failed to start audio playback:', error);
      throw error;
    }
  }

  /**
   * Stop audio playback
   */
  async stopPlayer(): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.stopAsync();
        await this.sound.unloadAsync();
        this.sound = null;
        console.log('✅ Audio playback stopped');
      }
    } catch (error) {
      console.error('Failed to stop audio playback:', error);
      throw error;
    }
  }

  /**
   * Pause audio playback
   */
  async pausePlayer(): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.pauseAsync();
        console.log('✅ Audio playback paused');
      }
    } catch (error) {
      console.error('Failed to pause audio playback:', error);
      throw error;
    }
  }

  /**
   * Resume audio playback
   */
  async resumePlayer(): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.playAsync();
        console.log('✅ Audio playback resumed');
      }
    } catch (error) {
      console.error('Failed to resume audio playback:', error);
      throw error;
    }
  }

  /**
   * Get current playback status
   */
  async getPlaybackStatus(): Promise<PlaybackStatus> {
    if (!this.sound) {
      return {
        isLoaded: false,
        isPlaying: false,
        positionMillis: 0,
        durationMillis: 0,
        shouldPlay: false,
        volume: 0,
      };
    }

    const status = await this.sound.getStatusAsync();
    if (status.isLoaded) {
      return {
        isLoaded: true,
        isPlaying: status.isPlaying || false,
        positionMillis: status.positionMillis || 0,
        durationMillis: status.durationMillis || 0,
        shouldPlay: status.shouldPlay || false,
        volume: status.volume || 0,
      };
    }

    return {
      isLoaded: false,
      isPlaying: false,
      positionMillis: 0,
      durationMillis: 0,
      shouldPlay: false,
      volume: 0,
    };
  }

  /**
   * Set playback volume (0.0 to 1.0)
   */
  async setVolume(volume: number): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.setVolumeAsync(Math.max(0, Math.min(1, volume)));
      }
    } catch (error) {
      console.error('Failed to set volume:', error);
      throw error;
    }
  }

  /**
   * Seek to specific position in playback (milliseconds)
   */
  async seekToPlayer(positionMillis: number): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.setPositionAsync(positionMillis);
      }
    } catch (error) {
      console.error('Failed to seek audio:', error);
      throw error;
    }
  }

  /**
   * Get the directory where audio files are stored
   */
  getAudioDirectory(): string {
    return FileSystem.documentDirectory + 'audio/';
  }

  /**
   * Delete audio file
   */
  async deleteAudioFile(audioPath: string): Promise<void> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(audioPath);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(audioPath);
        console.log('✅ Audio file deleted:', audioPath);
      }
    } catch (error) {
      console.error('Failed to delete audio file:', error);
      throw error;
    }
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.recording) {
        await this.stopRecording();
      }
      if (this.sound) {
        await this.stopPlayer();
      }
    } catch (error) {
      console.error('Failed to cleanup audio service:', error);
    }
  }
}

// Export singleton instance
export default new CCLCAudioService();

// Export types for use in other components
export type { RecordingStatus, PlaybackStatus };
