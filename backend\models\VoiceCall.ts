/**
 * Voice Call Model
 * Stores all voice call metadata and recordings for admin panel access
 */

import mongoose, { Document, Schema } from 'mongoose';

export interface IVoiceCall extends Document {
  callId: string;
  callerId: string;
  recipientId: string;
  initiatorId?: string; // For compatibility with new API
  startTime: Date;
  endTime?: Date;
  duration: number; // in seconds
  status: 'connecting' | 'connected' | 'ended' | 'failed' | 'terminated' | 'initiating' | 'ringing';
  morphingProfile: string;
  voiceProfiles?: { // New WORLD vocoder profiles
    initiator: string;
    recipient: string;
  };
  recordingPath?: string;
  recordingSize?: number; // in bytes
  recordingDuration?: number; // in milliseconds
  transcription?: string;
  callerIP: string;
  deviceFingerprint: string;
  bleDeviceId?: string;
  callQuality: 'excellent' | 'good' | 'fair' | 'poor';
  endedBy?: string; // Who ended the call
  metadata: {
    callerUserAgent: string;
    networkType: string;
    encryptionLevel: string;
    compressionRatio: number;
    audioCodec: string;
    // WORLD vocoder metadata
    worldVocoderEnabled?: boolean;
    realTimeProcessing?: boolean;
    securityLevel?: 'low' | 'medium' | 'high';
    processingStats?: {
      averageLatency: number;
      totalFrames: number;
      errorCount: number;
      qualityMetrics: any;
    };
    // Recording encryption metadata
    encryptionKey?: any;
    adminAccessKey?: any;
    fileIv?: string;
    fileTag?: string;
    originalMimeType?: string;
    // Recording control metadata
    recordingStarted?: Date;
    recordingEnabled?: boolean;
    recordingNotified?: boolean;
  };
  adminNotes?: string;
  flaggedForReview: boolean;
  reviewedBy?: string;
  reviewedAt?: Date;
  getCallStats(): any;
  isParticipant(userId: string): boolean;
  getOtherParticipant(userId: string): string | null;
  updateProcessingStats(stats: any): void;
}

export interface IVoiceCallModel extends mongoose.Model<IVoiceCall> {
  getCallsForAdmin(filter?: any, page?: number, limit?: number): Promise<any[]>;
  getCallAnalytics(options?: { startDate?: Date; endDate?: Date }): Promise<any>;
}

const VoiceCallSchema = new Schema<IVoiceCall>(
  {
    callId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    callerId: {
      type: String,
      required: true,
      index: true
    },
    recipientId: {
      type: String,
      required: true,
      index: true
    },
    startTime: {
      type: Date,
      required: true,
      index: true
    },
    endTime: {
      type: Date
    },
    duration: {
      type: Number,
      default: 0
    },
    initiatorId: {
      type: String,
      index: true
    },
    status: {
      type: String,
      enum: ['connecting', 'connected', 'ended', 'failed', 'terminated', 'initiating', 'ringing'],
      default: 'connecting',
      index: true
    },
    morphingProfile: {
      type: String,
      required: true,
      enum: ['superuser', 'agent', 'secure', 'custom'],
      default: 'agent'
    },
    voiceProfiles: {
      initiator: {
        type: String
      },
      recipient: {
        type: String
      }
    },
    recordingPath: {
      type: String,
      index: true
    },
    recordingSize: {
      type: Number
    },
    recordingDuration: {
      type: Number
    },
    transcription: {
      type: String
    },
    callerIP: {
      type: String,
      required: true
    },
    deviceFingerprint: {
      type: String,
      required: true
    },
    bleDeviceId: {
      type: String
    },
    callQuality: {
      type: String,
      enum: ['excellent', 'good', 'fair', 'poor'],
      default: 'good'
    },
    endedBy: {
      type: String
    },
    metadata: {
      callerUserAgent: { type: String, required: true },
      networkType: { type: String, default: 'unknown' },
      encryptionLevel: { type: String, default: 'AES-256' },
      compressionRatio: { type: Number, default: 0.7 },
      audioCodec: { type: String, default: 'AAC' },
      // WORLD vocoder metadata
      worldVocoderEnabled: { type: Boolean, default: false },
      realTimeProcessing: { type: Boolean, default: true },
      securityLevel: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'high'
      },
      processingStats: {
        averageLatency: { type: Number },
        totalFrames: { type: Number },
        errorCount: { type: Number },
        qualityMetrics: Schema.Types.Mixed
      },
      // Recording encryption metadata
      encryptionKey: Schema.Types.Mixed,
      adminAccessKey: Schema.Types.Mixed,
      fileIv: String,
      fileTag: String,
      originalMimeType: String,
      // Recording control metadata
      recordingStarted: Date,
      recordingEnabled: { type: Boolean, default: true },
      recordingNotified: { type: Boolean, default: false }
    },
    adminNotes: {
      type: String
    },
    flaggedForReview: {
      type: Boolean,
      default: false,
      index: true
    },
    reviewedBy: {
      type: String
    },
    reviewedAt: {
      type: Date
    }
  },
  {
    timestamps: true,
    collection: 'voice_calls'
  }
);

// Indexes for admin panel queries
VoiceCallSchema.index({ callerId: 1, startTime: -1 });
VoiceCallSchema.index({ recipientId: 1, startTime: -1 });
VoiceCallSchema.index({ status: 1, flaggedForReview: 1 });
VoiceCallSchema.index({ startTime: -1 });
VoiceCallSchema.index({ duration: -1 });

// Virtual for call duration in readable format
VoiceCallSchema.virtual('durationFormatted').get(function () {
  const minutes = Math.floor(this.duration / 60);
  const seconds = this.duration % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});

// Add static methods to the schema
VoiceCallSchema.statics.getCallsForAdmin = function (filter: any = {}, page: number = 1, limit: number = 20) {
  const skip = (page - 1) * limit;
  return this.find(filter)
    .populate('callerId', 'username email')
    .populate('recipientId', 'username email')
    .sort({ startTime: -1 })
    .skip(skip)
    .limit(limit)
    .lean();
};

VoiceCallSchema.statics.getCallAnalytics = function (options: { startDate?: Date; endDate?: Date } = {}) {
  const matchStage: any = {};

  if (options.startDate || options.endDate) {
    matchStage.startTime = {};
    if (options.startDate) matchStage.startTime.$gte = options.startDate;
    if (options.endDate) matchStage.startTime.$lte = options.endDate;
  }

  const pipeline = [
    ...(Object.keys(matchStage).length > 0 ? [{ $match: matchStage }] : []),
    {
      $group: {
        _id: null,
        totalCalls: { $sum: 1 },
        completedCalls: {
          $sum: { $cond: [{ $eq: ['$status', 'ended'] }, 1, 0] }
        },
        failedCalls: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        averageDuration: { $avg: '$duration' },
        totalDuration: { $sum: '$duration' },
        recordingCount: {
          $sum: { $cond: [{ $ne: ['$recordingPath', null] }, 1, 0] }
        },
        flaggedCount: {
          $sum: { $cond: ['$flaggedForReview', 1, 0] }
        }
      }
    }
  ];

  return this.aggregate(pipeline);
};

// Add instance methods
VoiceCallSchema.methods.getCallStats = function () {
  return {
    callId: this.callId,
    duration: this.duration,
    status: this.status,
    quality: this.callQuality,
    hasRecording: !!this.recordingPath,
    morphingProfile: this.morphingProfile,
    voiceProfiles: this.voiceProfiles,
    worldVocoderEnabled: this.metadata?.worldVocoderEnabled,
    flagged: this.flaggedForReview
  };
};

VoiceCallSchema.methods.isParticipant = function (userId: string): boolean {
  return this.callerId === userId || this.recipientId === userId || this.initiatorId === userId;
};

VoiceCallSchema.methods.getOtherParticipant = function (userId: string): string | null {
  if (this.callerId === userId) {
    return this.recipientId;
  } else if (this.recipientId === userId) {
    return this.callerId;
  } else if (this.initiatorId === userId) {
    return this.recipientId;
  }
  return null;
};

VoiceCallSchema.methods.updateProcessingStats = function (stats: any): void {
  if (!this.metadata.processingStats) {
    this.metadata.processingStats = {
      averageLatency: 0,
      totalFrames: 0,
      errorCount: 0,
      qualityMetrics: {}
    };
  }

  Object.assign(this.metadata.processingStats, stats);
  this.markModified('metadata.processingStats');
};

export default mongoose.model<IVoiceCall, IVoiceCallModel>('VoiceCall', VoiceCallSchema);
