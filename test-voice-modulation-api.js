/**
 * Test script for Voice Modulation API endpoints
 * Tests the new custom profile management features
 */

const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000/api';

// Test admin credentials (using existing test credentials)
const TEST_ADMIN = {
  username: 'testadmin',
  password: 'testpassword123'
};

let authToken = '';

async function authenticate() {
  try {
    console.log('🔐 Authenticating admin...');
    const response = await fetch(`${BASE_URL}/auth/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_ADMIN)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Authentication failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    authToken = data.token;
    console.log('✅ Authentication successful');
    return true;
  } catch (error) {
    console.error('❌ Authentication failed:', error.message);
    return false;
  }
}

async function testGetAllProfiles() {
  try {
    console.log('\n📋 Testing: Get all voice profiles...');
    const response = await fetch(`${BASE_URL}/voice/profiles/all`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get profiles: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Successfully fetched profiles');
    console.log(`📊 Found ${data.data.totalCount} profiles:`);
    
    data.data.profiles.forEach(profile => {
      console.log(`  - ${profile.name} (${profile.type}): ${profile.description}`);
    });

    return data.data.profiles;
  } catch (error) {
    console.error('❌ Get profiles failed:', error.message);
    return [];
  }
}

async function testCreateCustomProfile() {
  try {
    console.log('\n🎯 Testing: Create custom voice profile...');
    
    const customProfile = {
      name: 'Test Custom Voice',
      description: 'A test custom voice profile created via API',
      pitch: -3,
      tempo: 1.2,
      reverb: 25,
      distortion: 15,
      formant: 100,
      chorus: true,
      normalize: true
    };

    const response = await fetch(`${BASE_URL}/voice/profiles/custom`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(customProfile)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to create profile: ${response.status} - ${errorData.error}`);
    }

    const data = await response.json();
    console.log('✅ Successfully created custom profile');
    console.log(`📝 Profile: ${data.data.profile.name}`);
    console.log(`📄 Description: ${data.data.profile.description}`);
    
    return data.data.profile;
  } catch (error) {
    console.error('❌ Create custom profile failed:', error.message);
    return null;
  }
}

async function testGetConstraints() {
  try {
    console.log('\n⚙️ Testing: Get profile constraints...');
    const response = await fetch(`${BASE_URL}/voice/profiles/constraints`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get constraints: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Successfully fetched constraints');
    console.log('📐 Parameter constraints:');
    
    Object.entries(data.data).forEach(([param, constraints]) => {
      if (constraints.type === 'boolean') {
        console.log(`  - ${param}: boolean`);
      } else {
        console.log(`  - ${param}: ${constraints.min} to ${constraints.max} ${constraints.unit}`);
      }
    });

    return data.data;
  } catch (error) {
    console.error('❌ Get constraints failed:', error.message);
    return null;
  }
}

async function testVoiceModulationAPI() {
  console.log('🎵 Voice Modulation API Test Suite');
  console.log('=====================================');

  // Authenticate first
  const authenticated = await authenticate();
  if (!authenticated) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }

  // Test all endpoints
  await testGetAllProfiles();
  await testCreateCustomProfile();
  await testGetConstraints();

  console.log('\n🎉 Test suite completed!');
  console.log('=====================================');
}

// Run the tests
testVoiceModulationAPI().catch(error => {
  console.error('💥 Test suite failed:', error);
  process.exit(1);
});
