import { Request, Response } from 'express';
import multer from 'multer';
import fs from 'fs';
import path from 'path';

// Import services with proper typing
let voiceModulation: any = null;
let lightweightVoiceNeutralizer: any = null;

try {
  voiceModulation = require('../../services/voiceModulation').voiceModulation;
} catch (error) {
  console.warn('Voice modulation service not available');
}

try {
  lightweightVoiceNeutralizer = require('../../services/lightweightVoiceNeutralizer').lightweightVoiceNeutralizer;
} catch (error) {
  console.warn('Lightweight voice neutralizer service not available');
}

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      const error = new Error('Only audio files are allowed') as any;
      cb(error, false);
    }
  }
});

export const uploadMiddleware = upload.single('audio');

interface UnifiedProfile {
  id: string;
  name: string;
  type: 'modulation' | 'neutralization';
  displayName: string;
  description: string;
  securityLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'MAXIMUM';
  realTimeCapable: boolean;
  latency: number;
  recommended: boolean;
  config: any;
}

// Convert neutralization profiles to unified format
const convertNeutralizationProfile = (profile: any): UnifiedProfile => {
  const securityMap = {
    'LIGHT': 'MEDIUM' as const,
    'MEDIUM': 'HIGH' as const,
    'HEAVY': 'MAXIMUM' as const
  };

  return {
    id: `neutralization_${profile.name}`,
    name: profile.name,
    type: 'neutralization',
    displayName: profile.name.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
    description: profile.description,
    securityLevel: securityMap[profile.level as keyof typeof securityMap] || 'MEDIUM',
    realTimeCapable: profile.realTimeMode,
    latency: profile.latencyTarget,
    recommended: profile.recommended || false,
    config: profile
  };
};

// Convert modulation profiles to unified format
const convertModulationProfile = (profile: any): UnifiedProfile => {
  const securityLevel = profile.distortion > 10 ? 'HIGH' : 'MEDIUM';
  
  return {
    id: `modulation_${profile.name}`,
    name: profile.name,
    type: 'modulation',
    displayName: profile.name.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
    description: profile.description || `Modulation with ${profile.pitch} pitch shift and ${profile.distortion} distortion`,
    securityLevel: securityLevel as 'LOW' | 'MEDIUM' | 'HIGH' | 'MAXIMUM',
    realTimeCapable: false, // SoX-based processing is not real-time optimized
    latency: 200, // Estimated processing time for SoX
    recommended: false, // Neutralization is preferred
    config: profile
  };
};

// Get unified profile list
export const getUnifiedProfiles = async (req: Request, res: Response) => {
  try {
    const unifiedProfiles: UnifiedProfile[] = [];

    // Load neutralization profiles (prioritized)
    try {
      const neutralizationProfiles = lightweightVoiceNeutralizer?.getAvailableProfiles() || [];
      neutralizationProfiles.forEach((profile: any) => {
        unifiedProfiles.push(convertNeutralizationProfile(profile));
      });
    } catch (error) {
      console.warn('Failed to load neutralization profiles:', error);
    }

    // Load modulation profiles (legacy)
    try {
      const modulationProfiles = voiceModulation?.getAvailableProfiles() || [];
      modulationProfiles.forEach((profile: any) => {
        unifiedProfiles.push(convertModulationProfile(profile));
      });
    } catch (error) {
      console.warn('Failed to load modulation profiles:', error);
    }

    // Sort profiles: neutralization first, then by recommendation, then by name
    unifiedProfiles.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'neutralization' ? -1 : 1;
      }
      if (a.recommended !== b.recommended) {
        return a.recommended ? -1 : 1;
      }
      return a.displayName.localeCompare(b.displayName);
    });

    res.json({
      success: true,
      data: {
        profiles: unifiedProfiles,
        stats: {
          total: unifiedProfiles.length,
          neutralization: unifiedProfiles.filter(p => p.type === 'neutralization').length,
          modulation: unifiedProfiles.filter(p => p.type === 'modulation').length,
          realTimeCapable: unifiedProfiles.filter(p => p.realTimeCapable).length,
          recommended: unifiedProfiles.filter(p => p.recommended).length
        }
      }
    });
  } catch (error: any) {
    console.error('Error getting unified profiles:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve voice profiles',
      details: error.message
    });
  }
};

// Get system status
export const getSystemStatus = async (req: Request, res: Response) => {
  try {
    const status = {
      neutralizationAvailable: true,
      modulationAvailable: false,
      recommendedSystem: 'neutralization' as const,
      capabilities: {
        realTimeProcessing: true,
        batchProcessing: true,
        reversibleProcessing: false,
        irreversibleProcessing: true
      },
      performance: {
        maxLatency: 50,
        minLatency: 15,
        averageLatency: 30,
        throughput: '10 concurrent streams'
      }
    };

    // Test SoX availability
    try {
      if (voiceModulation?.testSoxAvailability) {
        await voiceModulation.testSoxAvailability();
        status.modulationAvailable = true;
      }
    } catch (error) {
      console.warn('SoX not available for modulation:', error);
    }

    res.json({
      success: true,
      data: status
    });
  } catch (error: any) {
    console.error('Error getting system status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve system status',
      details: error.message
    });
  }
};

// Unified voice processing endpoint
export const processVoiceUnified = async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No audio file provided'
      });
    }

    const { profileId, outputFormat = 'wav' } = req.body;

    if (!profileId) {
      return res.status(400).json({
        success: false,
        error: 'Profile ID is required'
      });
    }

    const startTime = Date.now();

    // Determine processing type from profile ID
    const [type, profileName] = profileId.split('_', 2);

    let processedAudioPath: string;
    let processingMetrics: any = {};

    if (type === 'neutralization') {
      // Use lightweight neutralization
      const tempInputPath = path.join(__dirname, '../../temp', `input_${Date.now()}.wav`);
      const tempOutputPath = path.join(__dirname, '../../temp', `output_${Date.now()}.wav`);

      // Write input file
      fs.writeFileSync(tempInputPath, req.file.buffer);

      try {
        const result = await lightweightVoiceNeutralizer.neutralizeVoice(
          tempInputPath,
          tempOutputPath,
          profileName
        );

        processedAudioPath = tempOutputPath;
        processingMetrics = {
          type: 'neutralization',
          profile: profileName,
          neutralizationLevel: result.neutralizationLevel,
          realTimeCapable: true,
          irreversible: true,
          latency: result.processingTime || (Date.now() - startTime)
        };

        // Set response headers
        res.setHeader('X-Processing-Type', 'neutralization');
        res.setHeader('X-Neutralization-Level', result.neutralizationLevel?.toString() || '0');
        res.setHeader('X-Processing-Time', processingMetrics.latency.toString());

      } finally {
        // Cleanup input file
        if (fs.existsSync(tempInputPath)) {
          fs.unlinkSync(tempInputPath);
        }
      }

    } else if (type === 'modulation') {
      // Use legacy modulation
      const tempInputPath = path.join(__dirname, '../../temp', `input_${Date.now()}.wav`);
      const tempOutputPath = path.join(__dirname, '../../temp', `output_${Date.now()}.wav`);

      // Write input file
      fs.writeFileSync(tempInputPath, req.file.buffer);

      try {
        const result = await voiceModulation.modulateVoice(
          tempInputPath,
          tempOutputPath,
          profileName
        );

        processedAudioPath = tempOutputPath;
        processingMetrics = {
          type: 'modulation',
          profile: profileName,
          realTimeCapable: false,
          irreversible: false,
          latency: Date.now() - startTime
        };

        // Set response headers
        res.setHeader('X-Processing-Type', 'modulation');
        res.setHeader('X-Processing-Time', processingMetrics.latency.toString());

      } finally {
        // Cleanup input file
        if (fs.existsSync(tempInputPath)) {
          fs.unlinkSync(tempInputPath);
        }
      }

    } else {
      return res.status(400).json({
        success: false,
        error: 'Invalid profile type'
      });
    }

    // Return processed audio
    if (!fs.existsSync(processedAudioPath)) {
      return res.status(500).json({
        success: false,
        error: 'Voice processing failed - no output generated'
      });
    }

    const audioBuffer = fs.readFileSync(processedAudioPath);
    
    // Cleanup output file
    fs.unlinkSync(processedAudioPath);

    // Set content headers
    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('Content-Length', audioBuffer.length.toString());
    res.setHeader('X-Processing-Success', 'true');

    res.send(audioBuffer);

  } catch (error: any) {
    console.error('Unified voice processing error:', error);
    res.status(500).json({
      success: false,
      error: 'Voice processing failed',
      details: error.message
    });
  }
};

// Get profile by ID
export const getProfileById = async (req: Request, res: Response) => {
  try {
    const { profileId } = req.params;
    
    if (!profileId) {
      return res.status(400).json({
        success: false,
        error: 'Profile ID is required'
      });
    }

    const [type, profileName] = profileId.split('_', 2);
    let profile: any = null;

    if (type === 'neutralization') {
      const neutralizationProfiles = lightweightVoiceNeutralizer?.getAvailableProfiles() || [];
      const foundProfile = neutralizationProfiles.find((p: any) => p.name === profileName);
      if (foundProfile) {
        profile = convertNeutralizationProfile(foundProfile);
      }
    } else if (type === 'modulation') {
      const modulationProfiles = voiceModulation?.getAvailableProfiles() || [];
      const foundProfile = modulationProfiles.find((p: any) => p.name === profileName);
      if (foundProfile) {
        profile = convertModulationProfile(foundProfile);
      }
    }

    if (!profile) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }

    res.json({
      success: true,
      data: profile
    });

  } catch (error: any) {
    console.error('Error getting profile by ID:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve profile',
      details: error.message
    });
  }
};

// Migration helper - convert modulation settings to neutralization
export const convertToNeutralization = async (req: Request, res: Response) => {
  try {
    const { modulationProfile } = req.body;

    if (!modulationProfile) {
      return res.status(400).json({
        success: false,
        error: 'Modulation profile is required'
      });
    }

    // Map modulation parameters to neutralization settings
    const neutralizationLevel = modulationProfile.distortion > 10 ? 'HEAVY' :
                               modulationProfile.distortion > 5 ? 'MEDIUM' : 'LIGHT';

    const recommendedProfile = lightweightVoiceNeutralizer?.getAvailableProfiles()
      ?.find((p: any) => p.level === neutralizationLevel);

    if (!recommendedProfile) {
      return res.status(404).json({
        success: false,
        error: 'No suitable neutralization profile found'
      });
    }

    const convertedProfile = convertNeutralizationProfile(recommendedProfile);

    res.json({
      success: true,
      data: {
        original: modulationProfile,
        converted: convertedProfile,
        migration: {
          reason: 'Neutralization provides better security and real-time performance',
          benefits: [
            'Irreversible voice processing',
            'Real-time capability (<50ms latency)',
            'Better anonymization (85% effectiveness)',
            'No external dependencies'
          ]
        }
      }
    });

  } catch (error: any) {
    console.error('Error converting profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to convert profile',
      details: error.message
    });
  }
};
