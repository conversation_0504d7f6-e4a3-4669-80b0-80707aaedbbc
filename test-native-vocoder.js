const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testNativeVocoder() {
    try {
        console.log('🎵 Testing Native Vocoder Availability');
        console.log('=====================================');
        
        // Step 1: Login as admin to get token
        console.log('🔐 Logging in as admin...');
        const loginResponse = await axios.post(`${BASE_URL}/api/auth/admin/login`, {
            username: 'admin',
            password: 'admin123',
            fingerprint: '4b5e57f6eb2f42b9'
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!loginResponse.data.success) {
            console.error('❌ Admin login failed:', loginResponse.data);
            return;
        }

        const adminToken = loginResponse.data.token;
        console.log('✅ Admin login successful');

        // Step 2: Check voice status
        console.log('\n🔍 Checking voice system status...');
        const statusResponse = await axios.get(`${BASE_URL}/api/voice/status`, {
            headers: {
                'Authorization': `Bearer ${adminToken}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('✅ Voice status response:', JSON.stringify(statusResponse.data, null, 2));

        // Step 3: Check native vocoder status specifically
        if (statusResponse.data.status?.nativeVocoder) {
            console.log('\n🎯 Native Vocoder Status:');
            console.log('  - Available:', statusResponse.data.status.nativeVocoder.available ? '✅ YES' : '❌ NO');
            console.log('  - Version:', statusResponse.data.status.nativeVocoder.version);
            console.log('  - Fallback Mode:', statusResponse.data.status.nativeVocoder.fallbackMode ? '⚠️ YES' : '✅ NO');
        } else {
            console.log('\n❌ No native vocoder information in response');
        }

    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

testNativeVocoder();
