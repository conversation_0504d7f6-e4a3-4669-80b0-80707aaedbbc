// Override React Native component types to fix compatibility with React 19.0.0 and iOS compilation
declare module "react-native" {
  import { ComponentType } from "react";

  // Override all React Native component types to be compatible with React 19
  export const View: ComponentType<any>;
  export const Text: ComponentType<any>;
  export const TouchableOpacity: ComponentType<any>;
  export const SafeAreaView: ComponentType<any>;
  export const ScrollView: ComponentType<any>;
  export const FlatList: ComponentType<any>;
  export const TextInput: ComponentType<any>;
  export const Modal: ComponentType<any>;
  export const StatusBar: ComponentType<any>;
  export const KeyboardAvoidingView: ComponentType<any>;
  export const Image: ComponentType<any>;
  export const Pressable: ComponentType<any>;
  export const ActivityIndicator: ComponentType<any>;
  export const RefreshControl: ComponentType<any>;
  export const Vibration: any;
  export const ActionSheetIOS: any;
  export const NativeModules: any;
  export const NativeEventEmitter: any;
  export const Alert: any;
  export const Platform: any;
  export const Dimensions: any;
  export const StyleSheet: any;
  export const Animated: any;
  export const Easing: any;
  export const AppState: any;
  export const Linking: any;
  export const BackHandler: any;

  // Additional exports to fix iOS compilation issues
  export interface ViewStyle {}
  export interface TextStyle {}
  export interface ImageStyle {}
  export type StyleProp<T> = T | T[] | null | undefined;
}

declare module "react-native-gesture-handler" {
  import { ComponentType } from "react";
  export const PanGestureHandler: ComponentType<any>;
  export const GestureHandlerRootView: ComponentType<any>;
  export const Swipeable: ComponentType<any>;
  export const State: any;
}

declare module "expo-av" {
  import { ComponentType } from "react";
  export const Video: ComponentType<any>;
  export const Audio: any;
  export const ResizeMode: any;
}

// Fix NodeJS Timeout type and add iOS-specific type fixes
declare global {
  namespace NodeJS {
    interface Timeout {}
  }

  // Fix iOS compilation nullability issues
  interface Window {
    [key: string]: any;
  }

  // Add missing global types for iOS compatibility
  var __DEV__: boolean;
  var __BUNDLE_START_TIME__: number | undefined;
  var process: {
    env: {
      NODE_ENV: string;
      [key: string]: string | undefined;
    };
  };
}

// Additional React Native module declarations for iOS compatibility
declare module "@react-native-community/netinfo" {
  export interface NetInfoState {
    type: string;
    isConnected: boolean | null;
    isInternetReachable: boolean | null;
    details: any;
  }

  export interface NetInfoSubscription {
    (): void;
  }

  const NetInfo: {
    addEventListener: (listener: (state: NetInfoState) => void) => NetInfoSubscription;
    fetch: () => Promise<NetInfoState>;
  };
  export default NetInfo;
}

// Fix Expo modules type issues
declare module "expo-*" {
  const content: any;
  export default content;
}
