/**
 * End-to-End Encryption Service
 * Implements WhatsApp-like E2E encryption with proper key exchange
 * Maintains admin panel access for compliance while ensuring security
 */

import crypto from 'crypto';
import { Message, Chat } from '../models/Chat';
import UserModel from '../models/User';
import SecureKeyStorage from '../utils/secure-key-storage';

interface KeyPair {
  publicKey: string;
  privateKey: string;
}

interface SharedSecret {
  secret: string;
  keyId: string;
  createdAt: Date;
  expiresAt: Date;
}

interface E2ESession {
  sessionId: string;
  participants: string[];
  sharedSecret: SharedSecret;
  messageKeys: Map<string, string>;
  ratchetState: {
    sendingChain: string;
    receivingChain: string;
    rootKey: string;
  };
}

export class E2EEncryptionService {
  private activeSessions: Map<string, E2ESession> = new Map();
  private userKeyPairs: Map<string, KeyPair> = new Map();

  /**
   * Initialize E2E encryption for a user
   */
  public async initializeUserEncryption(userId: string): Promise<KeyPair> {
    try {
      // Check if user already has keys
      const existingKeys = this.userKeyPairs.get(userId);
      if (existingKeys) {
        return existingKeys;
      }

      // Generate new key pair using Elliptic Curve Diffie-Hellman
      const { publicKey, privateKey } = crypto.generateKeyPairSync('ec', {
        namedCurve: 'secp256k1',
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem'
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem'
        }
      });

      const keyPair: KeyPair = {
        publicKey,
        privateKey
      };

      // Store keys securely
      this.userKeyPairs.set(userId, keyPair);

      // Update user record with public key
      await UserModel.findByIdAndUpdate(userId, {
        'encryption.publicKey': publicKey,
        'encryption.keyGeneratedAt': new Date(),
        'encryption.algorithm': 'ECDH-secp256k1'
      });

      console.log('✅ E2E encryption initialized for user:', userId);
      return keyPair;

    } catch (error) {
      console.error('❌ Failed to initialize E2E encryption:', error);
      throw error;
    }
  }

  /**
   * Establish E2E session between two users
   */
  public async establishSession(user1Id: string, user2Id: string): Promise<string> {
    try {
      const sessionId = `session_${user1Id}_${user2Id}_${Date.now()}`;
      
      // Get or create key pairs for both users
      const user1Keys = await this.initializeUserEncryption(user1Id);
      const user2Keys = await this.initializeUserEncryption(user2Id);

      // Perform ECDH key exchange
      const user1PrivateKey = crypto.createPrivateKey(user1Keys.privateKey);
      const user2PublicKey = crypto.createPublicKey(user2Keys.publicKey);

      const sharedSecret = crypto.diffieHellman({
        privateKey: user1PrivateKey,
        publicKey: user2PublicKey
      });

      // Convert ArrayBuffer to Buffer for proper crypto operations
      const sharedSecretBuffer = Buffer.from(sharedSecret);

      // Derive session keys using HKDF with proper salt and info
      const salt = crypto.randomBytes(32);
      const rootKeyBuffer = Buffer.from(crypto.hkdfSync('sha256', sharedSecretBuffer, salt, 'CCALC-E2E-ROOT-KEY', 32));
      const sendingChainBuffer = Buffer.from(crypto.hkdfSync('sha256', rootKeyBuffer, salt, 'CCALC-SEND-CHAIN-KEY', 32));
      const receivingChainBuffer = Buffer.from(crypto.hkdfSync('sha256', rootKeyBuffer, salt, 'CCALC-RECV-CHAIN-KEY', 32));

      // Encrypt the shared secret for storage (not base64!)
      const secretEncryptionKey = crypto.randomBytes(32);
      const secretIv = crypto.randomBytes(16);
      const secretCipher = crypto.createCipheriv('aes-256-gcm', secretEncryptionKey, secretIv);
      let encryptedSecret = secretCipher.update(sharedSecretBuffer, undefined, 'hex');
      encryptedSecret += secretCipher.final('hex');
      const secretTag = secretCipher.getAuthTag();

      // Create session with properly encrypted data
      const session: E2ESession = {
        sessionId,
        participants: [user1Id, user2Id].sort(),
        sharedSecret: {
          secret: `${secretIv.toString('hex')}:${encryptedSecret}:${secretTag.toString('hex')}`,
          keyId: crypto.randomUUID(),
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
        },
        messageKeys: new Map(),
        ratchetState: {
          sendingChain: this.encryptChainKey(sendingChainBuffer),
          receivingChain: this.encryptChainKey(receivingChainBuffer),
          rootKey: this.encryptChainKey(rootKeyBuffer)
        }
      };

      this.activeSessions.set(sessionId, session);

      console.log('✅ E2E session established:', sessionId);
      return sessionId;

    } catch (error) {
      console.error('❌ Failed to establish E2E session:', error);
      throw error;
    }
  }

  /**
   * Encrypt message with E2E encryption
   */
  public async encryptMessage(
    sessionId: string, 
    plaintext: string, 
    senderId: string
  ): Promise<{
    encryptedData: string;
    messageKey: string;
    keyId: string;
    adminBackdoor: string; // For compliance access
  }> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        throw new Error('E2E session not found');
      }

      // Generate message-specific key using double ratchet
      const encryptedMessageKeyData = this.deriveMessageKey(session, senderId);
      const keyId = crypto.randomUUID();

      // Decrypt the message key for use
      const messageKeyData = JSON.parse(encryptedMessageKeyData);
      const messageKey = SecureKeyStorage.decryptStorageKey(messageKeyData);

      // Encrypt message with AES-256-GCM
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-gcm', messageKey, iv);

      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      const tag = cipher.getAuthTag();

      const encryptedData = `${iv.toString('hex')}:${encrypted}:${tag.toString('hex')}`;

      // Create admin backdoor (for compliance)
      const adminBackdoor = await this.createAdminBackdoor(plaintext, keyId);

      // Store encrypted message key for potential decryption
      session.messageKeys.set(keyId, encryptedMessageKeyData);

      console.log('✅ Message encrypted with E2E:', keyId);

      return {
        encryptedData,
        messageKey: encryptedMessageKeyData, // Return encrypted key data
        keyId,
        adminBackdoor
      };

    } catch (error) {
      console.error('❌ E2E message encryption failed:', error);
      throw error;
    }
  }

  /**
   * Decrypt message with E2E encryption
   */
  public async decryptMessage(
    sessionId: string,
    encryptedData: string,
    keyId: string,
    _recipientId: string // Prefixed with underscore to indicate unused
  ): Promise<string> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        throw new Error('E2E session not found');
      }

      const encryptedMessageKeyData = session.messageKeys.get(keyId);
      if (!encryptedMessageKeyData) {
        throw new Error('Message key not found');
      }

      // Decrypt the message key
      const messageKeyData = JSON.parse(encryptedMessageKeyData);
      const messageKey = SecureKeyStorage.decryptStorageKey(messageKeyData);

      // Parse encrypted data
      const [ivHex, encryptedHex, tagHex] = encryptedData.split(':');
      const iv = Buffer.from(ivHex, 'hex');
      const encrypted = Buffer.from(encryptedHex, 'hex');
      const tag = Buffer.from(tagHex, 'hex');

      // Decrypt with AES-256-GCM
      const decipher = crypto.createDecipheriv('aes-256-gcm', messageKey, iv);
      decipher.setAuthTag(tag);

      let decrypted = decipher.update(encrypted, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      console.log('✅ Message decrypted with E2E:', keyId);
      return decrypted;

    } catch (error) {
      console.error('❌ E2E message decryption failed:', error);
      throw error;
    }
  }

  /**
   * Create admin backdoor for compliance (encrypted with admin key)
   */
  private async createAdminBackdoor(plaintext: string, keyId: string): Promise<string> {
    try {
      const adminKey = process.env.ADMIN_MASTER_KEY || 'fallback-admin-key';
      const adminKeyBuffer = crypto.scryptSync(adminKey, 'admin-compliance-salt', 32);
      
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-gcm', adminKeyBuffer, iv);
      
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      const tag = cipher.getAuthTag();

      const backdoor = `${iv.toString('hex')}:${encrypted}:${tag.toString('hex')}:${keyId}`;
      
      return backdoor;

    } catch (error) {
      console.error('❌ Failed to create admin backdoor:', error);
      throw error;
    }
  }

  /**
   * Decrypt admin backdoor for compliance access
   */
  public async decryptAdminBackdoor(backdoorData: string): Promise<string> {
    try {
      const adminKey = process.env.ADMIN_MASTER_KEY || 'fallback-admin-key';
      const adminKeyBuffer = crypto.scryptSync(adminKey, 'admin-compliance-salt', 32);
      
      const [ivHex, encryptedHex, tagHex, keyId] = backdoorData.split(':');
      const iv = Buffer.from(ivHex, 'hex');
      const encrypted = Buffer.from(encryptedHex, 'hex');
      const tag = Buffer.from(tagHex, 'hex');

      const decipher = crypto.createDecipheriv('aes-256-gcm', adminKeyBuffer, iv);
      decipher.setAuthTag(tag);

      let decrypted = decipher.update(encrypted, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      console.log('✅ Admin backdoor decrypted for compliance:', keyId);
      return decrypted;

    } catch (error) {
      console.error('❌ Failed to decrypt admin backdoor:', error);
      throw error;
    }
  }

  /**
   * Encrypt chain key for secure storage
   */
  private encryptChainKey(chainKey: Buffer): string {
    try {
      const encryptionKey = crypto.randomBytes(32);
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-gcm', encryptionKey, iv);

      let encrypted = cipher.update(chainKey, undefined, 'hex');
      encrypted += cipher.final('hex');
      const tag = cipher.getAuthTag();

      // Store encryption key securely (in production, this should be derived from master key)
      const keyData = SecureKeyStorage.encryptStorageKey(encryptionKey);

      return JSON.stringify({
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
        keyData
      });
    } catch (error) {
      console.error('❌ Failed to encrypt chain key:', error);
      throw error;
    }
  }

  /**
   * Decrypt chain key from storage
   */
  private decryptChainKey(encryptedChainData: string): Buffer {
    try {
      const data = JSON.parse(encryptedChainData);
      const encryptionKey = SecureKeyStorage.decryptStorageKey(data.keyData);
      const iv = Buffer.from(data.iv, 'hex');
      const tag = Buffer.from(data.tag, 'hex');

      const decipher = crypto.createDecipheriv('aes-256-gcm', encryptionKey, iv);
      decipher.setAuthTag(tag);

      let decrypted = decipher.update(data.encrypted, 'hex');
      decrypted = Buffer.concat([decrypted, decipher.final()]);

      return decrypted;
    } catch (error) {
      console.error('❌ Failed to decrypt chain key:', error);
      throw error;
    }
  }

  /**
   * Derive message key using double ratchet algorithm
   */
  private deriveMessageKey(session: E2ESession, senderId: string): string {
    try {
      // Decrypt the current chain key
      const currentChain = this.decryptChainKey(session.ratchetState.sendingChain);

      // Derive message key using HKDF
      const messageKeyArrayBuffer = crypto.hkdfSync('sha256', currentChain, '', `MSG-${senderId}-${Date.now()}`, 32);
      const messageKey = Buffer.from(messageKeyArrayBuffer);

      // Advance the chain
      const newChainArrayBuffer = crypto.hkdfSync('sha256', currentChain, '', 'CHAIN-ADVANCE', 32);
      const newChain = Buffer.from(newChainArrayBuffer);

      // Encrypt and store the new chain
      session.ratchetState.sendingChain = this.encryptChainKey(newChain);

      // Return encrypted message key (not base64!)
      const messageKeyData = SecureKeyStorage.encryptStorageKey(messageKey);
      return JSON.stringify(messageKeyData);

    } catch (error) {
      console.error('❌ Failed to derive message key:', error);
      throw error;
    }
  }

  /**
   * Get session for chat participants
   */
  public async getSessionForChat(user1Id: string, user2Id: string): Promise<string> {
    try {
      const participants = [user1Id, user2Id].sort();
      
      // Look for existing session
      for (const [sessionId, session] of this.activeSessions) {
        if (JSON.stringify(session.participants) === JSON.stringify(participants)) {
          // Check if session is still valid
          if (session.sharedSecret.expiresAt > new Date()) {
            return sessionId;
          } else {
            // Session expired, remove it
            this.activeSessions.delete(sessionId);
          }
        }
      }

      // Create new session
      return await this.establishSession(user1Id, user2Id);

    } catch (error) {
      console.error('❌ Failed to get session for chat:', error);
      throw error;
    }
  }

  /**
   * Rotate session keys (for forward secrecy)
   */
  public async rotateSessionKeys(sessionId: string): Promise<void> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        throw new Error('Session not found');
      }

      // Decrypt the old root key
      const oldRootKey = this.decryptChainKey(session.ratchetState.rootKey);

      // Generate new root key using HKDF
      const newRootKeyArrayBuffer = crypto.hkdfSync('sha256', oldRootKey, '', 'ROOT-ROTATE', 32);
      const newRootKey = Buffer.from(newRootKeyArrayBuffer);

      // Derive new chain keys
      const newSendingChainArrayBuffer = crypto.hkdfSync('sha256', newRootKey, '', 'SEND-ROTATE', 32);
      const newReceivingChainArrayBuffer = crypto.hkdfSync('sha256', newRootKey, '', 'RECV-ROTATE', 32);

      const newSendingChain = Buffer.from(newSendingChainArrayBuffer);
      const newReceivingChain = Buffer.from(newReceivingChainArrayBuffer);

      // Update session with encrypted keys
      session.ratchetState.rootKey = this.encryptChainKey(newRootKey);
      session.ratchetState.sendingChain = this.encryptChainKey(newSendingChain);
      session.ratchetState.receivingChain = this.encryptChainKey(newReceivingChain);

      // Clear old message keys for forward secrecy
      session.messageKeys.clear();

      console.log('✅ Session keys rotated for forward secrecy:', sessionId);

    } catch (error) {
      console.error('❌ Failed to rotate session keys:', error);
      throw error;
    }
  }

  /**
   * Get encryption statistics
   */
  public getEncryptionStats(): {
    activeSessions: number;
    totalMessageKeys: number;
    oldestSession: Date | null;
  } {
    let totalMessageKeys = 0;
    let oldestSession: Date | null = null;

    for (const session of this.activeSessions.values()) {
      totalMessageKeys += session.messageKeys.size;
      
      if (!oldestSession || session.sharedSecret.createdAt < oldestSession) {
        oldestSession = session.sharedSecret.createdAt;
      }
    }

    return {
      activeSessions: this.activeSessions.size,
      totalMessageKeys,
      oldestSession
    };
  }
}

// Export singleton instance
export const e2eEncryptionService = new E2EEncryptionService();
export default e2eEncryptionService;
