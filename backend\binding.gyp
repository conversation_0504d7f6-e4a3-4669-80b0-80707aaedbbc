{"targets": [{"target_name": "world_vocoder", "sources": ["src/world_addon.cpp", "src/world_processor.cpp", "src/world_wrapper.cpp", "lib/world/src/cheaptrick.cpp", "lib/world/src/common.cpp", "lib/world/src/d4c.cpp", "lib/world/src/dio.cpp", "lib/world/src/fft.cpp", "lib/world/src/harvest.cpp", "lib/world/src/matlabfunctions.cpp", "lib/world/src/stonemask.cpp", "lib/world/src/synthesis.cpp", "lib/world/src/synthesisrealtime.cpp"], "include_dirs": ["<!@(node -p \"require('node-addon-api').include\")", "lib/world/src", "src"], "dependencies": ["<!(node -p \"require('node-addon-api').gyp\")"], "cflags!": ["-fno-exceptions"], "cflags_cc!": ["-fno-exceptions"], "defines": ["NAPI_DISABLE_CPP_EXCEPTIONS"], "conditions": [["OS=='win'", {"msvs_settings": {"VCCLCompilerTool": {"ExceptionHandling": 1, "AdditionalOptions": ["/O2", "/Oi", "/Ot", "/Oy", "/GL"]}, "VCLinkerTool": {"LinkTimeCodeGeneration": 1}}}], ["OS=='mac'", {"xcode_settings": {"GCC_ENABLE_CPP_EXCEPTIONS": "YES", "CLANG_CXX_LIBRARY": "libc++", "MACOSX_DEPLOYMENT_TARGET": "10.7", "OTHER_CPLUSPLUSFLAGS": ["-O3", "-ffast-math"]}}], ["OS=='linux'", {"cflags_cc": ["-O3", "-ffast-math", "-march=native"]}]]}]}