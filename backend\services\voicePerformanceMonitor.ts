/**
 * Voice Performance Monitor
 * Tracks and analyzes voice processing performance metrics
 * Ensures sub-100ms processing times and monitors system health
 */

import { EventEmitter } from 'events';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface PerformanceMetric {
    timestamp: Date;
    sessionId?: string;
    userId?: string;
    profileName: string;
    processingTime: number;
    latencyTarget: number;
    inputSize: number;
    outputSize: number;
    withinTarget: boolean;
    processingSteps: string[];
    memoryUsage: number;
    cpuUsage?: number;
}

export interface PerformanceStats {
    totalProcessed: number;
    averageLatency: number;
    successRate: number;
    targetComplianceRate: number;
    profileStats: Record<string, {
        count: number;
        averageLatency: number;
        successRate: number;
        targetComplianceRate: number;
    }>;
    latencyDistribution: {
        under50ms: number;
        under100ms: number;
        under200ms: number;
        over200ms: number;
    };
    recentMetrics: PerformanceMetric[];
}

export interface SystemHealth {
    status: 'healthy' | 'warning' | 'critical';
    averageLatency: number;
    targetComplianceRate: number;
    errorRate: number;
    memoryUsage: number;
    activeProcesses: number;
    recommendations: string[];
}

export class VoicePerformanceMonitor extends EventEmitter {
    private metrics: PerformanceMetric[] = [];
    private maxMetrics: number = 10000; // Keep last 10k metrics
    private alertThresholds = {
        latencyWarning: 150,     // ms
        latencyCritical: 300,    // ms
        errorRateWarning: 0.05,  // 5%
        errorRateCritical: 0.15, // 15%
        memoryWarning: 500,      // MB
        memoryCritical: 1000     // MB
    };
    private logFile: string;

    constructor() {
        super();
        this.logFile = path.join(__dirname, '../logs/voice-performance.log');
        this.ensureLogDirectory();
        
        // Clean up old metrics every hour
        setInterval(() => this.cleanupOldMetrics(), 60 * 60 * 1000);
        
        console.log('Voice Performance Monitor initialized');
    }

    private async ensureLogDirectory(): Promise<void> {
        try {
            const logDir = path.dirname(this.logFile);
            await fs.mkdir(logDir, { recursive: true });
        } catch (error) {
            console.error('Failed to create log directory:', error);
        }
    }

    /**
     * Record a performance metric
     */
    async recordMetric(metric: Omit<PerformanceMetric, 'timestamp' | 'withinTarget' | 'memoryUsage'>): Promise<void> {
        const fullMetric: PerformanceMetric = {
            ...metric,
            timestamp: new Date(),
            withinTarget: metric.processingTime <= metric.latencyTarget,
            memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024 // MB
        };

        // Add to metrics array
        this.metrics.push(fullMetric);
        
        // Cleanup if too many metrics
        if (this.metrics.length > this.maxMetrics) {
            this.metrics = this.metrics.slice(-this.maxMetrics);
        }

        // Log to file
        await this.logMetric(fullMetric);

        // Check for alerts
        this.checkAlerts(fullMetric);

        // Emit events
        this.emit('metricRecorded', fullMetric);
        
        if (!fullMetric.withinTarget) {
            this.emit('latencyWarning', fullMetric);
        }

        console.log(`Performance metric recorded: ${fullMetric.profileName} - ${fullMetric.processingTime}ms (target: ${fullMetric.latencyTarget}ms)`);
    }

    /**
     * Get current performance statistics
     */
    getPerformanceStats(timeWindow?: number): PerformanceStats {
        const windowStart = timeWindow ? new Date(Date.now() - timeWindow) : null;
        const relevantMetrics = windowStart 
            ? this.metrics.filter(m => m.timestamp >= windowStart)
            : this.metrics;

        if (relevantMetrics.length === 0) {
            return {
                totalProcessed: 0,
                averageLatency: 0,
                successRate: 0,
                targetComplianceRate: 0,
                profileStats: {},
                latencyDistribution: {
                    under50ms: 0,
                    under100ms: 0,
                    under200ms: 0,
                    over200ms: 0
                },
                recentMetrics: []
            };
        }

        const totalProcessed = relevantMetrics.length;
        const averageLatency = relevantMetrics.reduce((sum, m) => sum + m.processingTime, 0) / totalProcessed;
        const withinTargetCount = relevantMetrics.filter(m => m.withinTarget).length;
        const targetComplianceRate = withinTargetCount / totalProcessed;

        // Profile statistics
        const profileStats: Record<string, any> = {};
        const profileGroups = relevantMetrics.reduce((groups, metric) => {
            if (!groups[metric.profileName]) {
                groups[metric.profileName] = [];
            }
            groups[metric.profileName].push(metric);
            return groups;
        }, {} as Record<string, PerformanceMetric[]>);

        Object.entries(profileGroups).forEach(([profileName, metrics]) => {
            const count = metrics.length;
            const avgLatency = metrics.reduce((sum, m) => sum + m.processingTime, 0) / count;
            const withinTarget = metrics.filter(m => m.withinTarget).length;
            
            profileStats[profileName] = {
                count,
                averageLatency: avgLatency,
                successRate: 1.0, // Assuming all recorded metrics are successful
                targetComplianceRate: withinTarget / count
            };
        });

        // Latency distribution
        const latencyDistribution = {
            under50ms: relevantMetrics.filter(m => m.processingTime < 50).length,
            under100ms: relevantMetrics.filter(m => m.processingTime < 100).length,
            under200ms: relevantMetrics.filter(m => m.processingTime < 200).length,
            over200ms: relevantMetrics.filter(m => m.processingTime >= 200).length
        };

        return {
            totalProcessed,
            averageLatency,
            successRate: 1.0, // Assuming all recorded metrics are successful
            targetComplianceRate,
            profileStats,
            latencyDistribution,
            recentMetrics: relevantMetrics.slice(-50) // Last 50 metrics
        };
    }

    /**
     * Get system health status
     */
    getSystemHealth(): SystemHealth {
        const recentStats = this.getPerformanceStats(5 * 60 * 1000); // Last 5 minutes
        const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // MB
        
        let status: 'healthy' | 'warning' | 'critical' = 'healthy';
        const recommendations: string[] = [];

        // Check latency
        if (recentStats.averageLatency > this.alertThresholds.latencyCritical) {
            status = 'critical';
            recommendations.push('Critical: Average latency exceeds 300ms. Consider optimizing processing pipeline.');
        } else if (recentStats.averageLatency > this.alertThresholds.latencyWarning) {
            status = 'warning';
            recommendations.push('Warning: Average latency exceeds 150ms. Monitor system performance.');
        }

        // Check target compliance
        if (recentStats.targetComplianceRate < 0.7) {
            status = status === 'critical' ? 'critical' : 'warning';
            recommendations.push(`Target compliance rate is ${(recentStats.targetComplianceRate * 100).toFixed(1)}%. Consider adjusting latency targets or optimizing processing.`);
        }

        // Check memory usage
        if (memoryUsage > this.alertThresholds.memoryCritical) {
            status = 'critical';
            recommendations.push('Critical: Memory usage exceeds 1GB. Restart service or investigate memory leaks.');
        } else if (memoryUsage > this.alertThresholds.memoryWarning) {
            status = status === 'critical' ? 'critical' : 'warning';
            recommendations.push('Warning: Memory usage exceeds 500MB. Monitor for memory leaks.');
        }

        // Add positive recommendations
        if (status === 'healthy') {
            recommendations.push('System is performing optimally.');
            if (recentStats.averageLatency < 50) {
                recommendations.push('Excellent: Average latency is under 50ms.');
            }
            if (recentStats.targetComplianceRate > 0.95) {
                recommendations.push('Excellent: Target compliance rate is above 95%.');
            }
        }

        return {
            status,
            averageLatency: recentStats.averageLatency,
            targetComplianceRate: recentStats.targetComplianceRate,
            errorRate: 0, // Would need error tracking implementation
            memoryUsage,
            activeProcesses: recentStats.totalProcessed,
            recommendations
        };
    }

    /**
     * Get performance trends over time
     */
    getPerformanceTrends(hours: number = 24): any {
        const windowStart = new Date(Date.now() - hours * 60 * 60 * 1000);
        const relevantMetrics = this.metrics.filter(m => m.timestamp >= windowStart);
        
        // Group by hour
        const hourlyStats: Record<string, PerformanceMetric[]> = {};
        relevantMetrics.forEach(metric => {
            const hour = new Date(metric.timestamp).toISOString().slice(0, 13); // YYYY-MM-DDTHH
            if (!hourlyStats[hour]) {
                hourlyStats[hour] = [];
            }
            hourlyStats[hour].push(metric);
        });

        const trends = Object.entries(hourlyStats).map(([hour, metrics]) => ({
            hour,
            count: metrics.length,
            averageLatency: metrics.reduce((sum, m) => sum + m.processingTime, 0) / metrics.length,
            targetComplianceRate: metrics.filter(m => m.withinTarget).length / metrics.length,
            averageInputSize: metrics.reduce((sum, m) => sum + m.inputSize, 0) / metrics.length,
            averageOutputSize: metrics.reduce((sum, m) => sum + m.outputSize, 0) / metrics.length
        }));

        return trends.sort((a, b) => a.hour.localeCompare(b.hour));
    }

    /**
     * Log metric to file
     */
    private async logMetric(metric: PerformanceMetric): Promise<void> {
        try {
            const logEntry = JSON.stringify({
                timestamp: metric.timestamp.toISOString(),
                sessionId: metric.sessionId,
                userId: metric.userId,
                profile: metric.profileName,
                latency: metric.processingTime,
                target: metric.latencyTarget,
                withinTarget: metric.withinTarget,
                inputSize: metric.inputSize,
                outputSize: metric.outputSize,
                steps: metric.processingSteps,
                memory: metric.memoryUsage
            }) + '\n';

            await fs.appendFile(this.logFile, logEntry);
        } catch (error) {
            console.error('Failed to log performance metric:', error);
        }
    }

    /**
     * Check for performance alerts
     */
    private checkAlerts(metric: PerformanceMetric): void {
        if (metric.processingTime > this.alertThresholds.latencyCritical) {
            this.emit('criticalLatency', metric);
        } else if (metric.processingTime > this.alertThresholds.latencyWarning) {
            this.emit('warningLatency', metric);
        }

        if (metric.memoryUsage > this.alertThresholds.memoryCritical) {
            this.emit('criticalMemory', metric);
        } else if (metric.memoryUsage > this.alertThresholds.memoryWarning) {
            this.emit('warningMemory', metric);
        }
    }

    /**
     * Clean up old metrics to prevent memory issues
     */
    private cleanupOldMetrics(): void {
        const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // Keep 24 hours
        const initialCount = this.metrics.length;
        this.metrics = this.metrics.filter(m => m.timestamp >= cutoff);
        
        const removedCount = initialCount - this.metrics.length;
        if (removedCount > 0) {
            console.log(`Cleaned up ${removedCount} old performance metrics`);
        }
    }

    /**
     * Export performance data
     */
    async exportPerformanceData(format: 'json' | 'csv' = 'json'): Promise<string> {
        const stats = this.getPerformanceStats();
        
        if (format === 'json') {
            return JSON.stringify({
                exportTime: new Date().toISOString(),
                stats,
                systemHealth: this.getSystemHealth(),
                trends: this.getPerformanceTrends()
            }, null, 2);
        } else {
            // CSV format
            const headers = 'timestamp,profile,latency,target,withinTarget,inputSize,outputSize,memory\n';
            const rows = this.metrics.map(m => 
                `${m.timestamp.toISOString()},${m.profileName},${m.processingTime},${m.latencyTarget},${m.withinTarget},${m.inputSize},${m.outputSize},${m.memoryUsage}`
            ).join('\n');
            
            return headers + rows;
        }
    }

    /**
     * Reset all metrics (for testing)
     */
    reset(): void {
        this.metrics = [];
        this.emit('metricsReset');
        console.log('Performance metrics reset');
    }
}
