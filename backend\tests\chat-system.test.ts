/**
 * Comprehensive Chat System Tests
 * Tests all major fixes: media linking, real-time messaging, encryption, cleanup
 */

import request from 'supertest';
import mongoose from 'mongoose';
import { app } from '../server';
import { Message, Chat } from '../models/Chat';
import Media from '../models/Media';
import UserModel from '../models/User';
import websocketService from '../services/websocket';
import e2eEncryptionService from '../services/e2e-encryption';
import mediaCleanupService from '../services/media-cleanup';
import SecureKeyStorage from '../utils/secure-key-storage';

describe('Chat System Integration Tests', () => {
  let testUser1: any;
  let testUser2: any;
  let superuser: any;
  let authToken1: string;
  let authToken2: string;
  let superuserToken: string;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.TEST_MONGODB_URI || 'mongodb://localhost:27017/ccalc_test');
    
    // Clean up test data
    await Promise.all([
      UserModel.deleteMany({}),
      Message.deleteMany({}),
      Chat.deleteMany({}),
      Media.deleteMany({})
    ]);

    // Create test users
    testUser1 = await UserModel.create({
      username: 'testuser1',
      expressionHash: 'test_hash_1',
      profile: { displayName: 'Test User 1' },
      status: 'active'
    });

    testUser2 = await UserModel.create({
      username: 'testuser2',
      expressionHash: 'test_hash_2',
      profile: { displayName: 'Test User 2' },
      status: 'active'
    });

    superuser = await UserModel.create({
      username: 'superuser',
      expressionHash: 'super_hash',
      profile: { displayName: 'Super User' },
      status: 'active',
      isSuperuser: true
    });

    // Generate auth tokens (simplified for testing)
    authToken1 = 'test_token_1';
    authToken2 = 'test_token_2';
    superuserToken = 'super_token';
  });

  afterAll(async () => {
    // Clean up and disconnect
    await Promise.all([
      UserModel.deleteMany({}),
      Message.deleteMany({}),
      Chat.deleteMany({}),
      Media.deleteMany({})
    ]);
    await mongoose.disconnect();
  });

  describe('Fix 1: Media Attachment Linking', () => {
    test('should properly link media to messages', async () => {
      const response = await request(app)
        .post('/api/chat/unified/send')
        .set('Authorization', `Bearer ${authToken1}`)
        .attach('attachment', Buffer.from('test file content'), 'test.jpg')
        .field('message', 'Test message with attachment')
        .field('recipientId', testUser2._id.toString());

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message.hasAttachment).toBe(true);
      expect(response.body.message.mediaId).toBeDefined();

      // Verify media record exists and is linked
      const mediaRecord = await Media.findOne({ mediaId: response.body.message.mediaId });
      expect(mediaRecord).toBeTruthy();
      expect(mediaRecord?.messageId).toBeDefined();

      // Verify message exists
      const message = await Message.findById(response.body.message.id);
      expect(message).toBeTruthy();
      expect(message?.mediaAttachment).toBeDefined();
    });

    test('should handle media without orphaning files', async () => {
      const initialMediaCount = await Media.countDocuments();
      
      const response = await request(app)
        .post('/api/chat/unified/send')
        .set('Authorization', `Bearer ${authToken1}`)
        .attach('attachment', Buffer.from('test content'), 'test.pdf')
        .field('message', 'PDF test')
        .field('recipientId', testUser2._id.toString());

      expect(response.status).toBe(200);
      
      const finalMediaCount = await Media.countDocuments();
      expect(finalMediaCount).toBe(initialMediaCount + 1);

      // Verify no orphaned media
      const allMedia = await Media.find({});
      for (const media of allMedia) {
        if (media.messageId) {
          const linkedMessage = await Message.findById(media.messageId);
          expect(linkedMessage).toBeTruthy();
        }
      }
    });
  });

  describe('Fix 2: Real-time WebSocket Messaging', () => {
    test('should establish WebSocket connection', (done) => {
      const mockWs = {
        readyState: 1, // OPEN
        send: jest.fn(),
        on: jest.fn(),
        close: jest.fn()
      };

      // Test WebSocket service
      expect(websocketService.getConnectedUsersCount()).toBeGreaterThanOrEqual(0);
      done();
    });

    test('should broadcast messages in real-time', async () => {
      const broadcastSpy = jest.spyOn(websocketService, 'broadcastNewMessage');
      
      const response = await request(app)
        .post('/api/chat/unified/send')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({
          message: 'Real-time test message',
          recipientId: testUser2._id.toString()
        });

      expect(response.status).toBe(200);
      expect(broadcastSpy).toHaveBeenCalledWith(
        testUser1._id.toString(),
        testUser2._id.toString(),
        expect.any(Object)
      );

      broadcastSpy.mockRestore();
    });
  });

  describe('Fix 3: Secure Key Storage', () => {
    test('should encrypt storage keys securely', () => {
      const testKey = 'test-encryption-key-123';
      const encryptedData = SecureKeyStorage.encryptStorageKey(testKey);
      
      expect(encryptedData.encryptedKey).toBeDefined();
      expect(encryptedData.iv).toBeDefined();
      expect(encryptedData.tag).toBeDefined();
      expect(encryptedData.keyId).toBeDefined();

      // Verify decryption works
      const decryptedKey = SecureKeyStorage.decryptStorageKey(encryptedData);
      expect(decryptedKey.toString()).toBe(testKey);
    });

    test('should create admin access keys', () => {
      const testKey = 'admin-test-key-456';
      const adminKeyData = SecureKeyStorage.encryptKeyWithAdminAccess(testKey);
      
      expect(adminKeyData.encryptedKey).toBeDefined();
      expect(adminKeyData.adminAccessKey).toBeDefined();

      // Verify admin decryption works
      const decryptedKey = SecureKeyStorage.decryptKeyWithAdminAccess(adminKeyData);
      expect(decryptedKey.toString()).toBe(testKey);
    });

    test('should not store keys in plain text', async () => {
      const response = await request(app)
        .post('/api/chat/unified/send')
        .set('Authorization', `Bearer ${authToken1}`)
        .attach('attachment', Buffer.from('secure test'), 'secure.txt')
        .field('message', 'Secure storage test')
        .field('recipientId', testUser2._id.toString());

      expect(response.status).toBe(200);

      // Check that media record doesn't contain plain text keys
      const mediaRecord = await Media.findOne({ mediaId: response.body.message.mediaId });
      expect(mediaRecord?.storage.encryptionKey).toHaveProperty('encryptedKey');
      expect(mediaRecord?.storage.encryptionKey).toHaveProperty('iv');
      expect(mediaRecord?.storage.encryptionKey).toHaveProperty('tag');
      
      // Ensure it's not a plain string
      expect(typeof mediaRecord?.storage.encryptionKey).toBe('object');
    });
  });

  describe('Fix 4: Media Cleanup System', () => {
    test('should identify orphaned media', async () => {
      // Create orphaned media record
      const orphanedMedia = await Media.create({
        mediaId: 'orphaned-test-123',
        uploaderId: testUser1._id,
        messageId: new mongoose.Types.ObjectId(), // Non-existent message
        file: {
          originalName: 'orphaned.jpg',
          encryptedName: 'encrypted_orphaned.jpg',
          mimeType: 'image/jpeg',
          size: 1000,
          category: 'image'
        },
        storage: {
          encryptedPath: '/fake/path/orphaned.jpg',
          encryptionKey: SecureKeyStorage.encryptStorageKey('fake-key'),
          fileIv: 'fake-iv',
          fileTag: 'fake-tag',
          salt: 'fake-salt',
          algorithm: 'aes-256-gcm',
          compressionUsed: false
        },
        isActive: true
      });

      const stats = await mediaCleanupService.getCleanupStats();
      expect(stats.orphanedMediaRecords).toBeGreaterThan(0);
    });

    test('should perform cleanup without errors', async () => {
      const cleanupStats = await mediaCleanupService.performCleanup({
        dryRun: true, // Don't actually delete in tests
        maxAge: 1 // Very short age for testing
      });

      expect(cleanupStats).toHaveProperty('orphanedMediaRecords');
      expect(cleanupStats).toHaveProperty('orphanedFiles');
      expect(cleanupStats).toHaveProperty('expiredMedia');
      expect(cleanupStats.errors).toEqual([]);
    });
  });

  describe('Fix 5: E2E Encryption', () => {
    test('should initialize user encryption', async () => {
      const keyPair = await e2eEncryptionService.initializeUserEncryption(testUser1._id.toString());
      
      expect(keyPair.publicKey).toBeDefined();
      expect(keyPair.privateKey).toBeDefined();
      expect(keyPair.publicKey).toContain('BEGIN PUBLIC KEY');
      expect(keyPair.privateKey).toContain('BEGIN PRIVATE KEY');
    });

    test('should establish E2E session', async () => {
      const sessionId = await e2eEncryptionService.getSessionForChat(
        testUser1._id.toString(),
        testUser2._id.toString()
      );
      
      expect(sessionId).toBeDefined();
      expect(sessionId).toContain('session_');
    });

    test('should encrypt and decrypt messages', async () => {
      const sessionId = await e2eEncryptionService.getSessionForChat(
        testUser1._id.toString(),
        testUser2._id.toString()
      );

      const plaintext = 'Test E2E encrypted message';
      const encrypted = await e2eEncryptionService.encryptMessage(
        sessionId,
        plaintext,
        testUser1._id.toString()
      );

      expect(encrypted.encryptedData).toBeDefined();
      expect(encrypted.keyId).toBeDefined();
      expect(encrypted.adminBackdoor).toBeDefined();

      const decrypted = await e2eEncryptionService.decryptMessage(
        sessionId,
        encrypted.encryptedData,
        encrypted.keyId,
        testUser2._id.toString()
      );

      expect(decrypted).toBe(plaintext);
    });

    test('should provide admin backdoor access', async () => {
      const sessionId = await e2eEncryptionService.getSessionForChat(
        testUser1._id.toString(),
        testUser2._id.toString()
      );

      const plaintext = 'Admin backdoor test message';
      const encrypted = await e2eEncryptionService.encryptMessage(
        sessionId,
        plaintext,
        testUser1._id.toString()
      );

      // Test admin backdoor decryption
      const adminDecrypted = await e2eEncryptionService.decryptAdminBackdoor(
        encrypted.adminBackdoor
      );

      expect(adminDecrypted).toBe(plaintext);
    });
  });

  describe('Fix 6: Unified Chat System', () => {
    test('should handle messages through unified API', async () => {
      const response = await request(app)
        .post('/api/chat/unified/send')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({
          message: 'Unified API test message',
          recipientId: testUser2._id.toString()
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message.id).toBeDefined();
    });

    test('should retrieve messages through unified API', async () => {
      // Send a test message first
      await request(app)
        .post('/api/chat/unified/send')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({
          message: 'Retrieval test message',
          recipientId: testUser2._id.toString()
        });

      const response = await request(app)
        .get('/api/chat/unified/messages')
        .set('Authorization', `Bearer ${authToken1}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.messages)).toBe(true);
    });

    test('should handle superuser chat restrictions', async () => {
      // Mobile user should only be able to chat with superuser
      const response = await request(app)
        .post('/api/chat/unified/send')
        .set('Authorization', `Bearer ${authToken1}`)
        .send({
          message: 'Message to superuser',
          // Don't specify recipientId - should default to superuser
        });

      expect(response.status).toBe(200);
      
      // Verify message was sent to superuser
      const message = await Message.findById(response.body.message.id);
      expect(message?.recipientId.toString()).toBe(superuser._id.toString());
    });
  });

  describe('Integration Test: Complete Message Flow', () => {
    test('should handle complete message flow with all fixes', async () => {
      // 1. Send message with attachment using unified API
      const sendResponse = await request(app)
        .post('/api/chat/unified/send')
        .set('Authorization', `Bearer ${authToken1}`)
        .attach('attachment', Buffer.from('integration test file'), 'integration.txt')
        .field('message', 'Complete integration test message')
        .field('recipientId', testUser2._id.toString());

      expect(sendResponse.status).toBe(200);
      expect(sendResponse.body.success).toBe(true);

      const messageId = sendResponse.body.message.id;
      const mediaId = sendResponse.body.message.mediaId;

      // 2. Verify message exists with proper encryption
      const message = await Message.findById(messageId);
      expect(message).toBeTruthy();
      expect(message?.content.encrypted).toBeDefined();
      expect(message?.e2eEncryption?.sessionId).toBeDefined();
      expect(message?.e2eEncryption?.adminBackdoor).toBeDefined();

      // 3. Verify media is properly linked
      const media = await Media.findOne({ mediaId });
      expect(media).toBeTruthy();
      expect(media?.messageId?.toString()).toBe(messageId);
      expect(media?.storage.encryptionKey.encryptedKey).toBeDefined();

      // 4. Verify real-time broadcast was called
      // (Already tested in WebSocket section)

      // 5. Test message retrieval
      const retrieveResponse = await request(app)
        .get('/api/chat/unified/messages')
        .set('Authorization', `Bearer ${authToken2}`);

      expect(retrieveResponse.status).toBe(200);
      const retrievedMessage = retrieveResponse.body.messages.find(
        (msg: any) => msg.id === messageId
      );
      expect(retrievedMessage).toBeTruthy();
      expect(retrievedMessage.mediaRecord).toBeDefined();

      // 6. Test admin decryption capability
      // (Would require admin service integration)

      console.log('✅ Complete integration test passed');
    });
  });
});

// Performance and stress tests
describe('Performance Tests', () => {
  test('should handle multiple concurrent messages', async () => {
    const promises = [];
    const messageCount = 10;

    for (let i = 0; i < messageCount; i++) {
      promises.push(
        request(app)
          .post('/api/chat/unified/send')
          .set('Authorization', `Bearer ${authToken1}`)
          .send({
            message: `Concurrent message ${i}`,
            recipientId: testUser2._id.toString()
          })
      );
    }

    const responses = await Promise.all(promises);
    
    responses.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    // Verify all messages were created
    const messageCount_db = await Message.countDocuments({
      senderId: testUser1._id
    });
    expect(messageCount_db).toBeGreaterThanOrEqual(messageCount);
  });

  test('should handle large file attachments', async () => {
    const largeBuffer = Buffer.alloc(1024 * 1024); // 1MB
    largeBuffer.fill('A');

    const response = await request(app)
      .post('/api/chat/unified/send')
      .set('Authorization', `Bearer ${authToken1}`)
      .attach('attachment', largeBuffer, 'large_file.txt')
      .field('message', 'Large file test')
      .field('recipientId', testUser2._id.toString());

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
});

export {};
