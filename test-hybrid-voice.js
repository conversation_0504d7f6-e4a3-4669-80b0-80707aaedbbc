const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');

const BASE_URL = 'http://localhost:3000';

const adminCredentials = {
  username: 'admin',
  password: 'admin123'
};

let authToken = '';

async function loginAsAdmin() {
  try {
    console.log('🔐 Logging in as admin...');
    const response = await axios.post(`${BASE_URL}/api/auth/admin/login`, adminCredentials);
    authToken = response.data.token;
    console.log('✅ Admin login successful');
    return authToken;
  } catch (error) {
    console.error('❌ Admin login failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

async function testHybridCapabilities() {
  try {
    console.log('\n🧪 Testing hybrid processing capabilities...');
    const response = await axios.get(`${BASE_URL}/api/voice/hybrid/capabilities`, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Hybrid capabilities response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('❌ Hybrid capabilities test failed:', error.response?.data || error.message);
    return null;
  }
}

async function testHybridProfiles() {
  try {
    console.log('\n📋 Testing hybrid processing profiles...');
    const response = await axios.get(`${BASE_URL}/api/voice/hybrid/profiles`, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Hybrid profiles response:');
    response.data.data.profiles.forEach(profile => {
      console.log(`  • ${profile.name}: ${profile.description}`);
      console.log(`    - Latency Target: ${profile.latencyTarget}ms`);
      console.log(`    - Quality Mode: ${profile.qualityMode}`);
      console.log(`    - Anti-Forensic: ${profile.antiForensic ? 'Yes' : 'No'}`);
    });
    return response.data;
  } catch (error) {
    console.error('❌ Hybrid profiles test failed:', error.response?.data || error.message);
    return null;
  }
}

async function testHybridRealtimePerformance() {
  try {
    console.log('\n⚡ Testing hybrid real-time performance...');
    const response = await axios.post(`${BASE_URL}/api/voice/hybrid/realtime-test`, {
      profile: 'REALTIME_HYBRID_SECURE',
      iterations: 5
    }, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Hybrid real-time performance test:');
    const results = response.data.data;
    console.log(`  • Average Latency: ${results.averageLatency}ms`);
    console.log(`  • Min Latency: ${results.minLatency}ms`);
    console.log(`  • Max Latency: ${results.maxLatency}ms`);
    console.log(`  • Success Rate: ${(results.successRate * 100).toFixed(1)}%`);
    console.log(`  • Test Status: ${results.performanceMet ? '✅ PASSED' : '❌ FAILED'}`);
    
    return response.data;
  } catch (error) {
    console.error('❌ Hybrid real-time test failed:', error.response?.data || error.message);
    return null;
  }
}

async function testHybridProcessing() {
  try {
    console.log('\n🎵 Testing hybrid audio processing...');
    
    // Create a simple test audio buffer (sine wave)
    const sampleRate = 44100;
    const duration = 1; // 1 second
    const frequency = 440; // A4 note
    const samples = sampleRate * duration;
    const buffer = Buffer.alloc(samples * 2); // 16-bit samples
    
    for (let i = 0; i < samples; i++) {
      const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 32767;
      buffer.writeInt16LE(Math.round(sample), i * 2);
    }
    
    const form = new FormData();
    form.append('audio', buffer, {
      filename: 'test-audio.wav',
      contentType: 'audio/wav'
    });
    form.append('profileName', 'REALTIME_HYBRID_SECURE');
    form.append('format', 'wav');
    
    console.log('📤 Sending test audio for hybrid processing...');
    const response = await axios.post(`${BASE_URL}/api/voice/hybrid/process?format=json`, form, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        ...form.getHeaders()
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity
    });
    
    console.log('✅ Hybrid processing response:');
    console.log(`  • Processing Time: ${response.data.data.processingTime}ms`);
    console.log(`  • Audio Size: ${response.data.data.audioSize} bytes`);
    console.log(`  • Profile Used: ${response.data.data.profile}`);
    console.log(`  • Pipeline: ${response.data.data.processing.pipeline.join(' → ')}`);
    
    // Performance metrics
    if (response.data.data.performance) {
      const perf = response.data.data.performance;
      console.log('📊 Performance Metrics:');
      console.log(`  • Neutralization: ${perf.neutralizationTime}ms`);
      console.log(`  • SoX Processing: ${perf.soxTime}ms`);
      console.log(`  • Total Pipeline: ${perf.totalTime}ms`);
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ Hybrid processing test failed:', error.response?.data || error.message);
    return null;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Hybrid Voice Processing System Tests\n');
  console.log('='.repeat(60));
  
  try {
    // Login
    await loginAsAdmin();
    
    // Test all hybrid endpoints
    const capabilities = await testHybridCapabilities();
    const profiles = await testHybridProfiles();
    const performance = await testHybridRealtimePerformance();
    const processing = await testHybridProcessing();
    
    console.log('\n' + '='.repeat(60));
    console.log('📋 TEST SUMMARY');
    console.log('='.repeat(60));
    
    console.log(`✅ Capabilities Test: ${capabilities ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Profiles Test: ${profiles ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Performance Test: ${performance ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Processing Test: ${processing ? 'PASSED' : 'FAILED'}`);
    
    const passedTests = [capabilities, profiles, performance, processing].filter(Boolean).length;
    const totalTests = 4;
    
    console.log(`\n🎯 Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 ALL HYBRID VOICE PROCESSING TESTS PASSED!');
      console.log('\n🔧 The hybrid system is ready for production use with:');
      console.log('   • Complete input tone removal');
      console.log('   • Real-time performance (<100ms)');
      console.log('   • Anti-forensic capabilities');
      console.log('   • Three optimized profiles');
    } else {
      console.log('⚠️  Some tests failed. Please check the backend service.');
    }
    
  } catch (error) {
    console.error('💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  testHybridCapabilities,
  testHybridProfiles,
  testHybridRealtimePerformance,
  testHybridProcessing
};
