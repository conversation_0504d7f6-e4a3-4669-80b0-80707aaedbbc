/**
 * WebSocket Service for Real-time Messaging
 * Enhanced with robust connection management, app lifecycle handling, and network monitoring
 * No typing indicators or online status as per requirements
 */

import { AuthService } from './AuthService';
import { ChatMessage } from './ChatService';
import AppConfig from '../config/app.config';
import { AppState } from 'react-native';
import NetInfo from '@react-native-community/netinfo';

interface WebSocketMessage {
  type: 'message' | 'media' | 'system' | 'error' | 'status_update' | 'ping' | 'pong';
  data: any;
  timestamp: number;
}

type MessageHandler = (message: ChatMessage) => void;
type ConnectionHandler = (connected: boolean) => void;
type ErrorHandler = (error: string) => void;

export class WebSocketService {
  private ws: WebSocket | null = null;
  private authService: AuthService;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 8; // Increased for mobile reliability
  private reconnectDelay = 1000; // Start with 1 second for faster initial recovery
  private maxReconnectDelay = 30000; // Max 30 seconds for mobile networks
  private isConnecting = false;
  private shouldReconnect = true;
  private isAppActive = true;
  private networkConnected = true;

  // Enhanced connection management with mobile-optimized timeouts
  private heartbeatInterval: any = null;
  private heartbeatTimeout: any = null;
  private connectionTimeout: any = null;
  private lastPongReceived = 0;
  private pingInterval = 25000; // Ping every 25 seconds (mobile-optimized)
  private pongTimeout = 15000; // Wait 15 seconds for pong (mobile networks need more time)
  private connectionTimeoutDuration = 30000; // 30 seconds for mobile connections

  // Connection quality monitoring
  private consecutiveFailures = 0;
  private lastSuccessfulConnection = 0;
  private adaptiveMode = false;

  // Message queue for offline scenarios
  private messageQueue: Array<{ type: string; data: any; timestamp: number }> = [];
  private isProcessingQueue = false;

  // Event handlers
  private messageHandlers: MessageHandler[] = [];
  private connectionHandlers: ConnectionHandler[] = [];
  private errorHandlers: ErrorHandler[] = [];

  // Bound methods to prevent listener duplication
  private boundAppStateHandler: (nextAppState: string) => void;
  private appStateListenerAdded = false;

  constructor(authService: AuthService) {
    this.authService = authService;
    this.boundAppStateHandler = this.handleAppStateChange.bind(this);
    this.setupAppLifecycleHandlers();
    this.setupNetworkMonitoring();
  }

  /**
   * Setup app lifecycle handlers for proper connection management
   */
  private setupAppLifecycleHandlers(): void {
    if (!this.appStateListenerAdded) {
      AppState.addEventListener('change', this.boundAppStateHandler);
      this.appStateListenerAdded = true;
      console.log('📱 App state listener added');
    }
  }

  /**
   * Setup network monitoring for connection stability
   */
  private setupNetworkMonitoring(): void {
    NetInfo.addEventListener(state => {
      const wasConnected = this.networkConnected;
      this.networkConnected = state.isConnected ?? false;

      console.log('🌐 Network state changed:', {
        isConnected: this.networkConnected,
        type: state.type,
        wasConnected
      });

      if (!wasConnected && this.networkConnected) {
        // Network came back online
        console.log('🌐 Network reconnected, attempting WebSocket reconnection...');
        this.reconnectAttempts = 0; // Reset attempts on network recovery
        this.connect();
      } else if (wasConnected && !this.networkConnected) {
        // Network went offline
        console.log('🌐 Network disconnected, pausing WebSocket reconnection...');
        this.disconnect();
      }
    });
  }

  /**
   * Handle app state changes (background/foreground)
   */
  private handleAppStateChange(nextAppState: string): void {
    const wasActive = this.isAppActive;
    this.isAppActive = nextAppState === 'active';

    console.log('📱 App state changed:', {
      from: wasActive ? 'active' : 'background',
      to: this.isAppActive ? 'active' : 'background'
    });

    if (!wasActive && this.isAppActive) {
      // App came to foreground
      console.log('📱 App foregrounded, reconnecting WebSocket...');
      this.reconnectAttempts = 0; // Reset attempts on app foreground
      this.connect();
    } else if (wasActive && !this.isAppActive) {
      // App went to background
      console.log('📱 App backgrounded, maintaining WebSocket connection...');
      // Keep connection alive but reduce ping frequency
      this.adjustHeartbeatForBackground();
    }
  }

  /**
   * Adjust heartbeat frequency when app is backgrounded
   */
  private adjustHeartbeatForBackground(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    // Reduce ping frequency in background to conserve battery
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.startHeartbeat(60000); // Ping every minute in background
    }
  }

  /**
   * Connect to WebSocket server with enhanced error handling
   */
  public async connect(): Promise<void> {
    // Don't connect if app is backgrounded or network is offline
    if (!this.isAppActive || !this.networkConnected) {
      console.log('🔌 Skipping WebSocket connection:', {
        appActive: this.isAppActive,
        networkConnected: this.networkConnected
      });
      return;
    }

    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;
    this.shouldReconnect = true;

    // Clear any existing connection timeout
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
    }

    // Set adaptive connection timeout based on connection quality
    const timeoutDuration = this.adaptiveMode ? this.connectionTimeoutDuration * 1.5 : this.connectionTimeoutDuration;
    this.connectionTimeout = setTimeout(() => {
      if (this.isConnecting) {
        console.log('⏰ WebSocket connection timeout after', timeoutDuration / 1000, 'seconds');
        this.isConnecting = false;
        this.consecutiveFailures++;
        if (this.ws) {
          this.ws.close();
        }
        this.scheduleReconnect();
      }
    }, timeoutDuration);

    try {
      const token = this.authService.getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Convert HTTP URL to WebSocket URL
      const wsUrl = AppConfig.backend.baseUrl
        .replace('http://', 'ws://')
        .replace('https://', 'wss://');

      const url = `${wsUrl}/ws/chat?token=${encodeURIComponent(token)}`;

      console.log('🔌 Connecting to WebSocket:', url.replace(token, 'TOKEN_HIDDEN'));

      this.ws = new WebSocket(url);
      this.setupEventHandlers();

    } catch (error) {
      console.error('❌ WebSocket connection failed:', error);
      this.isConnecting = false;
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout);
        this.connectionTimeout = null;
      }
      this.handleError('Failed to connect to real-time messaging');
      this.scheduleReconnect();
    }
  }

  /**
   * Disconnect from WebSocket server with cleanup
   */
  public disconnect(): void {
    this.shouldReconnect = false;

    // Clear all timers
    this.clearAllTimers();

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.isConnecting = false;
    this.reconnectAttempts = 0;
    console.log('🔌 WebSocket disconnected');
  }

  /**
   * Complete cleanup including event listeners
   */
  public cleanup(): void {
    this.disconnect();

    // Remove app state listener
    if (this.appStateListenerAdded) {
      AppState.removeEventListener('change', this.boundAppStateHandler);
      this.appStateListenerAdded = false;
      console.log('📱 App state listener removed');
    }
  }

  /**
   * Determine if we should reconnect based on WebSocket close code
   */
  private shouldReconnectBasedOnCloseCode(code: number): boolean {
    switch (code) {
      case 1000: // Normal closure
      case 1001: // Going away (page unload)
      case 1005: // No status received
        return false;
      case 1006: // Abnormal closure (network issues)
      case 1011: // Server error
      case 1012: // Service restart
      case 1013: // Try again later
      case 1014: // Bad gateway
        return true;
      default:
        // For unknown codes, be conservative and try to reconnect
        return code >= 1002;
    }
  }

  /**
   * Clear all active timers
   */
  private clearAllTimers(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }

  /**
   * Setup WebSocket event handlers with enhanced error handling
   */
  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('✅ WebSocket connected successfully');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000; // Reset to initial delay
      this.consecutiveFailures = 0; // Reset failure counter
      this.lastSuccessfulConnection = Date.now();
      this.lastPongReceived = Date.now();

      // Disable adaptive mode after successful connection
      if (this.adaptiveMode) {
        console.log('📶 Connection quality improved, disabling adaptive mode');
        this.adaptiveMode = false;
      }

      // Clear connection timeout
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout);
        this.connectionTimeout = null;
      }

      // Start heartbeat for connection monitoring
      this.startHeartbeat();

      // Process any queued messages
      this.processMessageQueue();

      this.notifyConnectionHandlers(true);
    };

    this.ws.onmessage = (event) => {
      try {
        const wsMessage: WebSocketMessage = JSON.parse(event.data);
        this.handleWebSocketMessage(wsMessage);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log('🔌 WebSocket connection closed:', {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      });

      this.isConnecting = false;
      this.clearAllTimers();
      this.notifyConnectionHandlers(false);

      // Analyze close code for better reconnection strategy
      const shouldReconnectBasedOnCode = this.shouldReconnectBasedOnCloseCode(event.code);

      if (this.shouldReconnect && shouldReconnectBasedOnCode) {
        // Increment failure counter for unexpected closes
        if (!event.wasClean && event.code !== 1000) {
          this.consecutiveFailures++;
        }
        this.scheduleReconnect();
      } else if (!shouldReconnectBasedOnCode) {
        console.log('🚫 Not reconnecting due to close code:', event.code);
      }
    };

    this.ws.onerror = (error) => {
      console.error('❌ WebSocket error:', error);
      this.isConnecting = false;
      this.clearAllTimers();
      this.handleError('WebSocket connection error');
    };
  }

  /**
   * Start heartbeat mechanism for connection monitoring with adaptive intervals
   */
  private startHeartbeat(interval: number = this.pingInterval): void {
    this.clearAllTimers();

    // Adjust interval based on connection quality
    const adaptiveInterval = this.adaptiveMode ? interval * 1.5 : interval;
    const adaptivePongTimeout = this.adaptiveMode ? this.pongTimeout * 1.5 : this.pongTimeout;

    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        // Send ping to server
        this.sendPing();

        // Set adaptive timeout for pong response
        this.heartbeatTimeout = setTimeout(() => {
          console.log('💔 Heartbeat timeout - no pong received after', adaptivePongTimeout / 1000, 'seconds');
          this.consecutiveFailures++;
          this.handleConnectionLoss();
        }, adaptivePongTimeout);
      }
    }, adaptiveInterval);

    console.log(`💓 Heartbeat started with ${adaptiveInterval}ms interval${this.adaptiveMode ? ' (adaptive)' : ''}`);
  }

  /**
   * Send ping to server
   */
  private sendPing(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const pingMessage: WebSocketMessage = {
        type: 'ping',
        data: { timestamp: Date.now() },
        timestamp: Date.now()
      };

      this.ws.send(JSON.stringify(pingMessage));
      console.log('🏓 Ping sent to server');
    }
  }

  /**
   * Handle connection loss detected by heartbeat
   */
  private handleConnectionLoss(): void {
    console.log('💔 Connection loss detected, forcing reconnection...');

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.isConnecting = false;
    this.notifyConnectionHandlers(false);
    this.scheduleReconnect();
  }

  /**
   * Handle incoming WebSocket messages with heartbeat support
   */
  private handleWebSocketMessage(wsMessage: WebSocketMessage): void {
    console.log('📨 WebSocket message received:', wsMessage.type);

    switch (wsMessage.type) {
      case 'pong':
        this.handlePongReceived(wsMessage);
        break;

      case 'message':
      case 'media':
        this.handleIncomingMessage(wsMessage.data);
        break;

      case 'status_update':
        console.log('📊 Message status update:', wsMessage.data);
        // Handle message status updates (delivered, read, etc.)
        break;

      case 'system':
        console.log('ℹ️ System message:', wsMessage.data.message);
        break;

      case 'error':
        console.error('❌ Server error:', wsMessage.data.message);
        this.handleError(wsMessage.data.message);
        break;

      default:
        console.warn('⚠️ Unknown message type:', wsMessage.type);
    }
  }

  /**
   * Handle pong response from server
   */
  private handlePongReceived(_wsMessage: WebSocketMessage): void {
    this.lastPongReceived = Date.now();

    // Reset consecutive failures on successful pong
    if (this.consecutiveFailures > 0) {
      console.log('📶 Connection quality improved, resetting failure counter');
      this.consecutiveFailures = 0;
    }

    // Clear the pong timeout since we received a response
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }

    console.log('🏓 Pong received from server');
  }

  /**
   * Handle incoming chat message
   */
  private handleIncomingMessage(messageData: any): void {
    try {
      // Validate message data
      if (!messageData || !messageData.id) {
        console.warn('⚠️ Invalid message data received:', messageData);
        return;
      }

      // Safely parse timestamp
      let timestamp = Date.now();
      if (messageData.timestamp) {
        const parsed = new Date(messageData.timestamp);
        if (!isNaN(parsed.getTime())) {
          timestamp = parsed.getTime();
        }
      }

      // Convert WebSocket message to ChatMessage format
      const chatMessage: ChatMessage = {
        id: messageData.id,
        clientId: `ws_${messageData.id}_${timestamp}`, // Unique client ID for WebSocket messages
        chatId: messageData.chatId || 'default-chat',
        text: messageData.content || '',
        sender: messageData.isOwnMessage ? 'user' : 'superuser',
        timestamp,
        status: 'delivered',
        isDelivered: true,
        isRead: false,
        attachment: messageData.attachment ? {
          id: messageData.mediaId || `attachment_${messageData.id}`,
          name: messageData.attachment.name || 'Unknown file',
          type: this.getFileCategory(messageData.attachment.type || 'application/octet-stream'),
          size: messageData.attachment.size || 0,
          uri: messageData.mediaId ? `${AppConfig.backend.baseUrl}/api/chat/unified/media/${messageData.mediaId}` : '',
          mimeType: messageData.attachment.type || 'application/octet-stream'
        } : undefined
      };

      console.log('📨 Processed WebSocket message:', {
        id: chatMessage.id,
        sender: chatMessage.sender,
        hasAttachment: !!chatMessage.attachment,
        timestamp: chatMessage.timestamp
      });

      // Notify all message handlers
      this.messageHandlers.forEach(handler => {
        try {
          handler(chatMessage);
        } catch (error) {
          console.error('❌ Error in message handler:', error);
        }
      });

    } catch (error) {
      console.error('❌ Error processing incoming message:', error, messageData);
    }
  }

  /**
   * Get file category from MIME type
   */
  private getFileCategory(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.includes('pdf') || mimeType.includes('document')) return 'document';
    return 'file';
  }

  /**
   * Schedule reconnection attempt with enhanced adaptive logic
   */
  private scheduleReconnect(): void {
    // Don't reconnect if app is backgrounded or network is offline
    if (!this.isAppActive || !this.networkConnected) {
      console.log('🔄 Skipping reconnection:', {
        appActive: this.isAppActive,
        networkConnected: this.networkConnected
      });
      return;
    }

    // Enable adaptive mode after multiple failures
    if (this.consecutiveFailures >= 3 && !this.adaptiveMode) {
      console.log('📶 Poor connection quality detected, enabling adaptive mode');
      this.adaptiveMode = true;
    }

    if (!this.shouldReconnect || this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ Max reconnection attempts reached or reconnection disabled');
      this.handleError('Unable to maintain real-time connection');
      return;
    }

    this.reconnectAttempts++;

    // Enhanced exponential backoff with adaptive behavior
    let baseDelay = this.reconnectDelay * Math.pow(1.8, this.reconnectAttempts - 1); // Gentler exponential growth

    // Apply adaptive multiplier for poor connections
    if (this.adaptiveMode) {
      baseDelay *= 1.5;
    }

    // Add jitter to prevent thundering herd
    const jitter = Math.random() * Math.min(2000, baseDelay * 0.3);
    const delay = Math.min(baseDelay + jitter, this.maxReconnectDelay);

    console.log(`🔄 Scheduling WebSocket reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${Math.round(delay)}ms${this.adaptiveMode ? ' (adaptive mode)' : ''}`);

    setTimeout(() => {
      if (this.shouldReconnect && this.isAppActive && this.networkConnected) {
        this.connect();
      }
    }, delay);
  }

  /**
   * Force immediate reconnection (useful for manual retry)
   */
  public forceReconnect(): void {
    console.log('🔄 Forcing immediate WebSocket reconnection...');
    this.reconnectAttempts = 0;
    this.disconnect();
    setTimeout(() => {
      this.connect();
    }, 1000);
  }

  /**
   * Get connection status information
   */
  public getConnectionStatus(): {
    connected: boolean;
    reconnectAttempts: number;
    maxReconnectAttempts: number;
    isAppActive: boolean;
    networkConnected: boolean;
    lastPongReceived: number;
  } {
    return {
      connected: this.ws?.readyState === WebSocket.OPEN,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      isAppActive: this.isAppActive,
      networkConnected: this.networkConnected,
      lastPongReceived: this.lastPongReceived
    };
  }

  /**
   * Send ping to keep connection alive
   */
  public ping(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type: 'ping' }));
    }
  }

  /**
   * Check if WebSocket is connected
   */
  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Send message through WebSocket with queuing for offline scenarios
   */
  public sendMessage(message: any): void {
    const wsMessage: WebSocketMessage = {
      type: 'message',
      data: message,
      timestamp: Date.now()
    };

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(wsMessage));
      console.log('📤 Message sent via WebSocket');
    } else {
      // Queue message for when connection is restored
      this.messageQueue.push(wsMessage);
      console.log('📥 Message queued for offline delivery:', this.messageQueue.length, 'messages in queue');

      // Try to reconnect if not already trying
      if (!this.isConnecting) {
        this.connect();
      }
    }
  }

  /**
   * Process queued messages when connection is restored
   */
  private async processMessageQueue(): Promise<void> {
    if (this.isProcessingQueue || this.messageQueue.length === 0) {
      return;
    }

    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.log('📥 Cannot process queue - WebSocket not connected');
      return;
    }

    this.isProcessingQueue = true;
    console.log('📤 Processing message queue:', this.messageQueue.length, 'messages');

    const messagesToSend = [...this.messageQueue];
    this.messageQueue = [];

    for (const message of messagesToSend) {
      try {
        // Check if message is not too old (5 minutes max)
        const messageAge = Date.now() - message.timestamp;
        if (messageAge > 5 * 60 * 1000) {
          console.log('📥 Skipping old queued message:', messageAge / 1000, 'seconds old');
          continue;
        }

        this.ws.send(JSON.stringify(message));
        console.log('📤 Queued message sent');

        // Small delay between messages to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error('❌ Failed to send queued message:', error);
        // Re-queue failed message
        this.messageQueue.unshift(message);
        break;
      }
    }

    this.isProcessingQueue = false;
    console.log('✅ Message queue processing completed');
  }

  /**
   * Add message handler
   */
  public onMessage(handler: MessageHandler): void {
    this.messageHandlers.push(handler);
  }

  /**
   * Remove message handler
   */
  public removeMessageHandler(handler: MessageHandler): void {
    const index = this.messageHandlers.indexOf(handler);
    if (index > -1) {
      this.messageHandlers.splice(index, 1);
    }
  }

  /**
   * Add connection handler
   */
  public onConnection(handler: ConnectionHandler): void {
    this.connectionHandlers.push(handler);
  }

  /**
   * Add error handler
   */
  public onError(handler: ErrorHandler): void {
    this.errorHandlers.push(handler);
  }

  /**
   * Notify connection handlers
   */
  private notifyConnectionHandlers(connected: boolean): void {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(connected);
      } catch (error) {
        console.error('❌ Error in connection handler:', error);
      }
    });
  }

  /**
   * Handle errors
   */
  private handleError(message: string): void {
    this.errorHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('❌ Error in error handler:', error);
      }
    });
  }
}

export default WebSocketService;
