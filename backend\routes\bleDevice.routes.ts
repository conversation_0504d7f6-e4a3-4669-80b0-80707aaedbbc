import { Router } from 'express';
import { auth, requireAdmin } from '../middleware/auth';
import { validate } from '../middleware/validate';
import * as BleDeviceController from '../controllers/admin/bleDevice.controller';
import Joi from 'joi';

const router = Router();

// Validation schemas
const registerBleDeviceSchema = Joi.object({
  deviceId: Joi.string().required(),
  deviceName: Joi.string().required(),
  deviceType: Joi.string().valid('earbud', 'headphone', 'speaker', 'generic').optional(),
  adData: Joi.any().optional(),
  characteristics: Joi.object().optional(),
  signature: Joi.string().optional(),
  signatureCharUuid: Joi.string().optional(),
  services: Joi.array().optional(),
  rssi: Joi.number().optional(),
  registeredBy: Joi.string().valid('script', 'admin', 'user').optional(),
  scriptVersion: Joi.string().optional(),
  manufacturer: Joi.string().optional(),
  model: Joi.string().optional(),
  notes: Joi.string().optional()
});

const updateBleDeviceSchema = Joi.object({
  deviceName: Joi.string().optional(),
  deviceType: Joi.string().valid('earbud', 'headphone', 'speaker', 'generic').optional(),
  voiceCallEnabled: Joi.boolean().optional(),
  notes: Joi.string().optional(),
  tags: Joi.array().optional(),
  manufacturer: Joi.string().optional(),
  model: Joi.string().optional(),
  isActive: Joi.boolean().optional()
});

/**
 * BLE Device Management Routes
 * All routes require admin authentication
 */

// Register new BLE device for a user
router.post(
  '/users/:userId/ble-devices',
  auth,
  requireAdmin,
  validate(registerBleDeviceSchema),
  BleDeviceController.registerBleDevice
);

// Get all BLE devices for a user
router.get(
  '/users/:userId/ble-devices',
  auth,
  requireAdmin,
  BleDeviceController.getUserBleDevices
);

// Get detailed information about a specific BLE device
router.get(
  '/ble-devices/:deviceId',
  auth,
  requireAdmin,
  BleDeviceController.getBleDeviceDetails
);

// Update BLE device settings
router.put(
  '/ble-devices/:deviceId',
  auth,
  requireAdmin,
  validate(updateBleDeviceSchema),
  BleDeviceController.updateBleDevice
);

// Delete/deactivate BLE device
router.delete(
  '/ble-devices/:deviceId',
  auth,
  requireAdmin,
  BleDeviceController.deleteBleDevice
);

// Generate authentication challenge for BLE device
router.post(
  '/ble-devices/:deviceId/challenge',
  auth,
  requireAdmin,
  BleDeviceController.generateBleChallenge
);

export default router;
