const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration for React Native 0.79.4
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  projectRoot: __dirname,
  watchFolders: [
    path.resolve(__dirname, '..'),
  ],
  resolver: {
    // Fix for Hermes require() error
    unstable_enablePackageExports: false,
    // Add node_modules resolution paths for workspace
    nodeModulesPaths: [
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '..', 'node_modules'),
    ],
    // Ensure proper entry point resolution
    platforms: ['ios', 'android', 'native', 'web'],
    // Add explicit resolution for workspace setup
    resolverMainFields: ['react-native', 'browser', 'main'],
  },
  transformer: {
    // Enable Hermes bytecode
    hermesCommand: path.resolve(__dirname, 'node_modules', 'react-native', 'sdks', 'hermesc', process.platform === 'win32' ? 'win64-bin' : 'osx-bin', 'hermesc'),
    minifierPath: 'metro-minify-terser',
  },
  server: {
    // Ensure Metro serves from the correct directory
    enhanceMiddleware: (middleware) => {
      return (req, res, next) => {
        // Force correct working directory
        process.chdir(__dirname);
        return middleware(req, res, next);
      };
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
