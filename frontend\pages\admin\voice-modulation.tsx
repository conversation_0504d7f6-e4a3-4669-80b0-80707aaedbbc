import React, { useState, useEffect, useRef } from 'react';
import Head from 'next/head';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import DashboardCard from '../../components/admin/DashboardCard';
import Button from '../../components/admin/Button';
import Icon from '../../components/admin/Icon';
import VoiceNeutralization from '../../components/VoiceNeutralization';
import { apiClient } from '../../utils/axiosClient';
import tokenManager from '../../utils/tokenManager';

interface VoiceProfile {
  name: string;
  pitch: number;
  tempo: number;
  reverb: number;
  distortion: number;
  formant: number;
  chorus: boolean;
  normalize: boolean;
  description?: string;
  type?: 'standard' | 'custom';
  isEditable?: boolean;
}

interface VoiceConstraints {
  pitch: { min: number; max: number; step: number; unit: string };
  tempo: { min: number; max: number; step: number; unit: string };
  reverb: { min: number; max: number; step: number; unit: string };
  distortion: { min: number; max: number; step: number; unit: string };
  formant: { min: number; max: number; step: number; unit: string };
  chorus: { type: string };
  normalize: { type: string };
}

const VoiceModulationPage: React.FC = () => {
  // State management
  const [activeTab, setActiveTab] = useState<'browse' | 'create' | 'test' | 'neutralization'>('neutralization');
  const [profiles, setProfiles] = useState<VoiceProfile[]>([]);
  const [constraints, setConstraints] = useState<VoiceConstraints | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<VoiceProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Audio recording state
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [testingProfile, setTestingProfile] = useState<boolean>(false);
  const [recordingTime, setRecordingTime] = useState(0);
  
  // Custom profile creation state
  const [customProfile, setCustomProfile] = useState<VoiceProfile>({
    name: '',
    pitch: 0,
    tempo: 1.0,
    reverb: 0,
    distortion: 0,
    formant: 0,
    chorus: false,
    normalize: true,
    description: ''
  });

  // Filter and search
  const [searchTerm, setSearchTerm] = useState('');
  const [profileFilter, setProfileFilter] = useState<'all' | 'standard' | 'custom'>('all');

  const audioRef = useRef<HTMLAudioElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch data on component mount
  useEffect(() => {
    fetchProfiles();
  }, []);

  // Recording timer effect
  useEffect(() => {
    if (isRecording) {
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } else {
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
      }
      setRecordingTime(0);
    }

    return () => {
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
      }
    };
  }, [isRecording]);

  const fetchProfiles = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Use apiClient to make authenticated backend calls
      const response = await apiClient.backend.get('/api/voice/profiles-enhanced');
      
      if (response.data.success && response.data.profiles) {
        setProfiles(response.data.profiles);
      } else {
        throw new Error('Failed to fetch profiles from backend');
      }
    } catch (error: any) {
      console.error('Failed to fetch profiles:', error);
      setError('Failed to load voice profiles');
    } finally {
      setIsLoading(false);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      chunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/wav' });
        const file = new File([audioBlob], 'recording.wav', { type: 'audio/wav' });
        setAudioFile(file);
        
        // Clean up previous audio URL
        if (audioUrl) {
          URL.revokeObjectURL(audioUrl);
        }
        
        const newAudioUrl = URL.createObjectURL(audioBlob);
        setAudioUrl(newAudioUrl);
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
      setError(null);
    } catch (error) {
      console.error('Failed to start recording:', error);
      setError('Failed to access microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const playAudio = () => {
    if (audioRef.current && audioUrl) {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        audioRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  const testProfile = async (profile: VoiceProfile) => {
    if (!audioFile) {
      setError('Please record audio first');
      return;
    }

    try {
      setTestingProfile(true);
      setError(null);
      
      const formData = new FormData();
      formData.append('audio', audioFile);
      formData.append('profileName', profile.name);

      // Use the voice proxy route which handles authentication
      const response = await fetch('/api/voice/test-modulation', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${tokenManager.getToken('admin')}`
        },
        body: formData
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        
        // Clean up previous modulated audio URL
        if (audioUrl && audioUrl.includes('blob:')) {
          URL.revokeObjectURL(audioUrl);
        }
        
        const modulatedAudioUrl = URL.createObjectURL(audioBlob);
        setAudioUrl(modulatedAudioUrl);
        
        // Auto-play the result
        if (audioRef.current) {
          audioRef.current.src = modulatedAudioUrl;
          audioRef.current.play();
          setIsPlaying(true);
        }
        
        setSuccess(`Profile "${profile.name}" tested successfully!`);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Profile test failed');
      }
    } catch (error: any) {
      console.error('Failed to test profile:', error);
      setError(`Failed to test profile: ${error.message}`);
    } finally {
      setTestingProfile(false);
    }
  };

  const createCustomProfile = async () => {
    if (!customProfile.name.trim()) {
      setError('Please enter a profile name');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      // Use apiClient for authenticated backend calls
      const response = await apiClient.backend.post('/api/voice/profiles/custom', customProfile);

      if (response.data.success) {
        setSuccess('Custom profile created successfully!');
        
        // Reset form
        setCustomProfile({
          name: '',
          pitch: 0,
          tempo: 1.0,
          reverb: 0,
          distortion: 0,
          formant: 0,
          chorus: false,
          normalize: true,
          description: ''
        });
        
        // Refresh profiles list
        await fetchProfiles();
        setActiveTab('browse');
      } else {
        throw new Error(response.data.error || 'Failed to create profile');
      }
    } catch (error: any) {
      console.error('Failed to create profile:', error);
      setError(`Failed to create profile: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Filter profiles based on search and filter
  const filteredProfiles = profiles.filter(profile => {
    const matchesSearch = profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         profile.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = profileFilter === 'all' || profile.type === profileFilter;
    return matchesSearch && matchesFilter;
  });

  return (
    <AdminLayout>
      <Head>
        <title>Voice Modulation | CCALC Admin Panel</title>
      </Head>
      
      <div className="container">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Voice Modulation</h1>
            <p className="text-gray-600 mt-1">
              Manage voice profiles and test voice modulation effects
            </p>
          </div>
          <div className="flex space-x-3">
            <Button 
              variant="secondary" 
              onClick={fetchProfiles}
              disabled={isLoading}
            >
              <Icon name="refresh" size={16} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Alert Messages */}
        {error && (
          <DashboardCard className="mb-6 border-red-200 bg-red-50">
            <div className="flex items-center space-x-3 text-red-800">
              <Icon name="error" size={20} />
              <span>{error}</span>
              <button 
                onClick={() => setError(null)}
                className="ml-auto text-red-600 hover:text-red-800"
              >
                <Icon name="close" size={16} />
              </button>
            </div>
          </DashboardCard>
        )}

        {success && (
          <DashboardCard className="mb-6 border-green-200 bg-green-50">
            <div className="flex items-center space-x-3 text-green-800">
              <Icon name="check" size={20} />
              <span>{success}</span>
              <button 
                onClick={() => setSuccess(null)}
                className="ml-auto text-green-600 hover:text-green-800"
              >
                <Icon name="close" size={16} />
              </button>
            </div>
          </DashboardCard>
        )}

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
          {[
            { key: 'neutralization', label: 'Voice Neutralization', icon: 'security' },
            { key: 'browse', label: 'Browse Profiles', icon: 'view' },
            { key: 'create', label: 'Create Profile', icon: 'add' },
            { key: 'test', label: 'Test & Record', icon: 'calls' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium rounded-md transition-all ${
                activeTab === tab.key
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <Icon name={tab.icon as any} size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content Area */}
          <div className="lg:col-span-2">
            {/* Voice Neutralization Tab */}
            {activeTab === 'neutralization' && (
              <DashboardCard title="Voice Neutralization">
                <div className="p-4">
                  <VoiceNeutralization />
                </div>
              </DashboardCard>
            )}

            {/* Browse Profiles Tab */}
            {activeTab === 'browse' && (
              <DashboardCard title="Voice Profiles">
                {/* Search and Filter */}
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Icon name="search" size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search profiles..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                      />
                    </div>
                  </div>
                  <select
                    value={profileFilter}
                    onChange={(e) => setProfileFilter(e.target.value as any)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                  >
                    <option value="all">All Profiles</option>
                    <option value="standard">Standard</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>

                {/* Profiles Grid */}
                {isLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-gray-600">Loading profiles...</p>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {filteredProfiles.map((profile, index) => (
                      <div
                        key={`${profile.name}-${index}`}
                        className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                          selectedProfile?.name === profile.name
                            ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedProfile(profile)}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h4 className="font-semibold text-gray-900">{profile.name}</h4>
                            <p className="text-sm text-gray-600 mt-1">{profile.description}</p>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            profile.type === 'custom' 
                              ? 'bg-purple-100 text-purple-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {profile.type === 'custom' ? 'Custom' : 'Standard'}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-3">
                          <div>Pitch: {profile.pitch > 0 ? '+' : ''}{profile.pitch}</div>
                          <div>Tempo: {profile.tempo}x</div>
                          <div>Reverb: {profile.reverb}%</div>
                          <div>Distortion: {profile.distortion}%</div>
                        </div>
                        
                        {audioFile && (
                          <Button
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              testProfile(profile);
                            }}
                            disabled={testingProfile}
                            className="w-full"
                          >
                            {testingProfile ? 'Testing...' : 'Test Profile'}
                          </Button>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {filteredProfiles.length === 0 && !isLoading && (
                  <div className="text-center py-12">
                    <Icon name="view" size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No profiles found</h3>
                    <p className="text-gray-600">
                      {searchTerm ? 'Try adjusting your search terms' : 'Create your first custom profile to get started'}
                    </p>
                  </div>
                )}
              </DashboardCard>
            )}

            {/* Create Profile Tab */}
            {activeTab === 'create' && (
              <DashboardCard title="Create Custom Profile">
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Profile Name *
                      </label>
                      <input
                        type="text"
                        value={customProfile.name}
                        onChange={(e) => setCustomProfile({ ...customProfile, name: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                        placeholder="Enter profile name"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Description
                      </label>
                      <input
                        type="text"
                        value={customProfile.description}
                        onChange={(e) => setCustomProfile({ ...customProfile, description: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                        placeholder="Optional description"
                      />
                    </div>
                  </div>

                  {/* Voice Parameters */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Pitch: {customProfile.pitch > 0 ? '+' : ''}{customProfile.pitch} semitones
                      </label>
                      <input
                        type="range"
                        min={constraints?.pitch.min || -12}
                        max={constraints?.pitch.max || 12}
                        step={constraints?.pitch.step || 0.1}
                        value={customProfile.pitch}
                        onChange={(e) => setCustomProfile({ ...customProfile, pitch: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>-12</span>
                        <span>0</span>
                        <span>+12</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tempo: {customProfile.tempo}x speed
                      </label>
                      <input
                        type="range"
                        min={constraints?.tempo.min || 0.5}
                        max={constraints?.tempo.max || 2.0}
                        step={constraints?.tempo.step || 0.05}
                        value={customProfile.tempo}
                        onChange={(e) => setCustomProfile({ ...customProfile, tempo: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0.5x</span>
                        <span>1.0x</span>
                        <span>2.0x</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Reverb: {customProfile.reverb}%
                      </label>
                      <input
                        type="range"
                        min={constraints?.reverb.min || 0}
                        max={constraints?.reverb.max || 100}
                        step={constraints?.reverb.step || 1}
                        value={customProfile.reverb}
                        onChange={(e) => setCustomProfile({ ...customProfile, reverb: parseInt(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0%</span>
                        <span>50%</span>
                        <span>100%</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Distortion: {customProfile.distortion}%
                      </label>
                      <input
                        type="range"
                        min={constraints?.distortion.min || 0}
                        max={constraints?.distortion.max || 100}
                        step={constraints?.distortion.step || 1}
                        value={customProfile.distortion}
                        onChange={(e) => setCustomProfile({ ...customProfile, distortion: parseInt(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0%</span>
                        <span>50%</span>
                        <span>100%</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Formant Shift: {customProfile.formant > 0 ? '+' : ''}{customProfile.formant} Hz
                    </label>
                    <input
                      type="range"
                      min={constraints?.formant.min || -1000}
                      max={constraints?.formant.max || 1000}
                      step={constraints?.formant.step || 10}
                      value={customProfile.formant}
                      onChange={(e) => setCustomProfile({ ...customProfile, formant: parseInt(e.target.value) })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>-1000 Hz</span>
                      <span>0 Hz</span>
                      <span>+1000 Hz</span>
                    </div>
                  </div>

                  {/* Advanced Options */}
                  <div className="flex flex-col sm:flex-row gap-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={customProfile.chorus}
                        onChange={(e) => setCustomProfile({ ...customProfile, chorus: e.target.checked })}
                        className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <span className="ml-2 text-sm font-medium text-gray-700">Add Chorus Effect</span>
                    </label>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={customProfile.normalize}
                        onChange={(e) => setCustomProfile({ ...customProfile, normalize: e.target.checked })}
                        className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                      <span className="ml-2 text-sm font-medium text-gray-700">Normalize Audio</span>
                    </label>
                  </div>

                  {/* Create Button */}
                  <div className="flex justify-end">
                    <Button
                      onClick={createCustomProfile}
                      disabled={isLoading || !customProfile.name.trim()}
                    >
                      {isLoading ? 'Creating...' : 'Create Profile'}
                    </Button>
                  </div>
                </div>
              </DashboardCard>
            )}

            {/* Test & Record Tab */}
            {activeTab === 'test' && (
              <DashboardCard title="Test & Record Audio">
                <div className="space-y-6">
                  {/* Recording Section */}
                  <div className="p-6 bg-gray-50 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-4">Audio Recording</h4>
                    
                    <div className="flex flex-col sm:flex-row items-center gap-4 mb-4">
                      {!isRecording ? (
                        <Button
                          onClick={startRecording}
                          variant="primary"
                          className="bg-red-600 hover:bg-red-700"
                        >
                          <Icon name="calls" size={16} />
                          Start Recording
                        </Button>
                      ) : (
                        <Button
                          onClick={stopRecording}
                          variant="secondary"
                        >
                          <Icon name="close" size={16} />
                          Stop Recording ({formatTime(recordingTime)})
                        </Button>
                      )}
                      
                      {audioUrl && (
                        <Button
                          onClick={playAudio}
                          variant="secondary"
                        >
                          <Icon name={isPlaying ? 'close' : 'calls'} size={16} />
                          {isPlaying ? 'Pause' : 'Play'}
                        </Button>
                      )}
                    </div>
                    
                    {isRecording && (
                      <div className="flex items-center space-x-2 text-red-600">
                        <div className="w-3 h-3 bg-red-600 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium">Recording in progress...</span>
                      </div>
                    )}

                    {audioFile && (
                      <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center space-x-2 text-green-800">
                          <Icon name="check" size={16} />
                          <span className="text-sm font-medium">Audio recorded successfully!</span>
                        </div>
                        <p className="text-sm text-green-600 mt-1">
                          You can now test this audio with any profile in the Browse tab.
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Quick Test Section */}
                  {audioFile && selectedProfile && (
                    <div className="p-6 bg-blue-50 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-4">Quick Test</h4>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-600">Selected Profile:</p>
                          <p className="font-medium text-gray-900">{selectedProfile.name}</p>
                        </div>
                        <Button
                          onClick={() => testProfile(selectedProfile)}
                          disabled={testingProfile}
                        >
                          {testingProfile ? 'Testing...' : 'Test Profile'}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </DashboardCard>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Profile Stats */}
            <DashboardCard title="Profile Statistics">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Profiles</span>
                  <span className="font-semibold text-gray-900">{profiles.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Standard Profiles</span>
                  <span className="font-semibold text-blue-600">
                    {profiles.filter(p => p.type === 'standard').length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Custom Profiles</span>
                  <span className="font-semibold text-purple-600">
                    {profiles.filter(p => p.type === 'custom').length}
                  </span>
                </div>
              </div>
            </DashboardCard>

            {/* Selected Profile Details */}
            {selectedProfile && (
              <DashboardCard title="Profile Details">
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold text-gray-900">{selectedProfile.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{selectedProfile.description}</p>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Pitch:</span>
                      <span className="font-medium">
                        {selectedProfile.pitch > 0 ? '+' : ''}{selectedProfile.pitch} semitones
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tempo:</span>
                      <span className="font-medium">{selectedProfile.tempo}x</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Reverb:</span>
                      <span className="font-medium">{selectedProfile.reverb}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Distortion:</span>
                      <span className="font-medium">{selectedProfile.distortion}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Formant:</span>
                      <span className="font-medium">
                        {selectedProfile.formant > 0 ? '+' : ''}{selectedProfile.formant} Hz
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Chorus:</span>
                      <span className="font-medium">{selectedProfile.chorus ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Normalize:</span>
                      <span className="font-medium">{selectedProfile.normalize ? 'Yes' : 'No'}</span>
                    </div>
                  </div>
                </div>
              </DashboardCard>
            )}

            {/* Quick Actions */}
            <DashboardCard title="Quick Actions">
              <div className="space-y-3">
                <Button 
                  variant="secondary" 
                  className="w-full justify-start"
                  onClick={() => setActiveTab('create')}
                >
                  <Icon name="add" size={16} />
                  Create New Profile
                </Button>
                <Button 
                  variant="secondary" 
                  className="w-full justify-start"
                  onClick={() => setActiveTab('test')}
                >
                  <Icon name="calls" size={16} />
                  Record & Test
                </Button>
                <Button 
                  variant="secondary" 
                  className="w-full justify-start"
                  onClick={fetchProfiles}
                  disabled={isLoading}
                >
                  <Icon name="refresh" size={16} />
                  Refresh Profiles
                </Button>
              </div>
            </DashboardCard>
          </div>
        </div>

        {/* Hidden audio element for playback */}
        <audio
          ref={audioRef}
          onEnded={() => setIsPlaying(false)}
          onError={() => setIsPlaying(false)}
          style={{ display: 'none' }}
        />
      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(VoiceModulationPage);
