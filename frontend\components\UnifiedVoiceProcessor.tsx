/**
 * Unified Voice Processor Component
 * Interface for the unified voice processing system with complete input tone removal
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Upload as UploadIcon,
  Download as DownloadIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  HighQuality as QualityIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Info as InfoIcon
} from '@mui/icons-material';

interface UnifiedProfile {
  id: string;
  name: string;
  description: string;
  type: string;
  latencyTarget: number;
  qualityMode: string;
  inputToneRemoval: boolean;
  antiForensic: boolean;
  pipeline: {
    neutralizationFirst: boolean;
    soxModulation: boolean;
    antiForensic: boolean;
    realTimeOptimized: boolean;
  };
}

interface ProcessingResult {
  processingTime: number;
  audioSize: number;
  inputSize: number;
  profile: string;
  latencyTarget: number;
  processing: {
    pipeline: string[];
  };
  performance: {
    withinTarget: boolean;
    qualityMode: string;
  };
  security: {
    inputToneRemoved: boolean;
    antiForensic: boolean;
    reversible: boolean;
  };
}

export const UnifiedVoiceProcessor: React.FC = () => {
  const [profiles, setProfiles] = useState<UnifiedProfile[]>([]);
  const [selectedProfile, setSelectedProfile] = useState<string>('');
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingResult, setProcessingResult] = useState<ProcessingResult | null>(null);
  const [processedAudioUrl, setProcessedAudioUrl] = useState<string | null>(null);
  const [capabilities, setCapabilities] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProfiles();
    loadCapabilities();
  }, []);

  const loadProfiles = async () => {
    try {
      const response = await fetch('/api/voice/unified/profiles', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setProfiles(data.data.profiles);
        if (data.data.profiles.length > 0) {
          setSelectedProfile(data.data.profiles[0].id);
        }
      }
    } catch (error) {
      console.error('Failed to load unified profiles:', error);
      setError('Failed to load voice profiles');
    }
  };

  const loadCapabilities = async () => {
    try {
      const response = await fetch('/api/voice/unified/capabilities', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setCapabilities(data.data);
      }
    } catch (error) {
      console.error('Failed to load capabilities:', error);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith('audio/')) {
        setAudioFile(file);
        setError(null);
      } else {
        setError('Please select an audio file');
      }
    }
  };

  const processAudio = async () => {
    if (!audioFile || !selectedProfile) {
      setError('Please select an audio file and profile');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setProcessingResult(null);

    try {
      const formData = new FormData();
      formData.append('audio', audioFile);
      formData.append('profileName', selectedProfile);

      // First get JSON result for display
      const jsonResponse = await fetch('/api/voice/unified/process?format=json', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: formData
      });

      if (jsonResponse.ok) {
        const jsonData = await jsonResponse.json();
        setProcessingResult(jsonData.data);
      }

      // Then get the actual processed audio
      const audioFormData = new FormData();
      audioFormData.append('audio', audioFile);
      audioFormData.append('profileName', selectedProfile);

      const audioResponse = await fetch('/api/voice/unified/process', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: audioFormData
      });

      if (audioResponse.ok) {
        const audioBlob = await audioResponse.blob();
        const audioUrl = URL.createObjectURL(audioBlob);
        setProcessedAudioUrl(audioUrl);
      } else {
        const errorData = await audioResponse.json();
        setError(errorData.error || 'Processing failed');
      }

    } catch (error) {
      console.error('Processing failed:', error);
      setError('Processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadProcessedAudio = () => {
    if (processedAudioUrl) {
      const a = document.createElement('a');
      a.href = processedAudioUrl;
      a.download = `processed_${selectedProfile}_${Date.now()}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const getLatencyStatus = (processingTime: number, target: number) => {
    if (processingTime <= target) {
      return { color: 'text-green-600', status: 'Excellent', icon: CheckIcon };
    } else if (processingTime <= target * 1.5) {
      return { color: 'text-yellow-600', status: 'Good', icon: WarningIcon };
    } else {
      return { color: 'text-red-600', status: 'Needs Optimization', icon: WarningIcon };
    }
  };

  const selectedProfileData = profiles.find(p => p.id === selectedProfile);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 text-white">
        <div className="flex items-center space-x-3">
          <SecurityIcon className="h-8 w-8" />
          <div>
            <h2 className="text-2xl font-bold">Unified Voice Processing</h2>
            <p className="text-purple-100">Complete input tone removal with SoX + Neutralization</p>
          </div>
        </div>
      </div>

      {/* Capabilities Overview */}
      {capabilities && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Capabilities</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3">
              <SecurityIcon className="h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium">Input Tone Removal</div>
                <div className="text-sm text-gray-500">Complete anonymization</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <SpeedIcon className="h-5 w-5 text-blue-600" />
              <div>
                <div className="font-medium">Real-time Processing</div>
                <div className="text-sm text-gray-500">&lt;100ms latency</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <QualityIcon className="h-5 w-5 text-purple-600" />
              <div>
                <div className="font-medium">Anti-forensic</div>
                <div className="text-sm text-gray-500">Non-reversible</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Profile Selection */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Voice Processing Profiles</h3>
        <div className="space-y-3">
          {profiles.map((profile) => (
            <div
              key={profile.id}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedProfile === profile.id
                  ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedProfile(profile.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="font-medium text-gray-900">{profile.name}</h4>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      profile.latencyTarget <= 50 
                        ? 'bg-green-100 text-green-800' 
                        : profile.latencyTarget <= 80
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {profile.latencyTarget}ms target
                    </span>
                    {profile.inputToneRemoval && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Input Tone Removed
                      </span>
                    )}
                    {profile.antiForensic && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        Anti-forensic
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{profile.description}</p>
                  
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-xs">
                    <div>
                      <span className="font-medium text-gray-500">Quality Mode:</span>
                      <div className="text-gray-900 capitalize">{profile.qualityMode}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-500">Neutralization:</span>
                      <div className={`font-medium ${profile.pipeline.neutralizationFirst ? 'text-green-600' : 'text-gray-400'}`}>
                        {profile.pipeline.neutralizationFirst ? 'Enabled' : 'Disabled'}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-500">SoX Modulation:</span>
                      <div className={`font-medium ${profile.pipeline.soxModulation ? 'text-blue-600' : 'text-gray-400'}`}>
                        {profile.pipeline.soxModulation ? 'Enabled' : 'Disabled'}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-500">Real-time:</span>
                      <div className={`font-medium ${profile.pipeline.realTimeOptimized ? 'text-green-600' : 'text-gray-400'}`}>
                        {profile.pipeline.realTimeOptimized ? 'Optimized' : 'Standard'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Audio Processing */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Audio Processing</h3>
        
        {/* File Upload */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Audio File
          </label>
          <div className="flex items-center space-x-4">
            <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg cursor-pointer hover:bg-blue-700 transition-colors">
              <UploadIcon className="h-5 w-5 mr-2" />
              Choose File
              <input
                type="file"
                accept="audio/*"
                onChange={handleFileUpload}
                className="hidden"
              />
            </label>
            {audioFile && (
              <span className="text-sm text-gray-600">
                {audioFile.name} ({(audioFile.size / 1024 / 1024).toFixed(2)} MB)
              </span>
            )}
          </div>
        </div>

        {/* Process Button */}
        <div className="mb-6">
          <button
            onClick={processAudio}
            disabled={!audioFile || !selectedProfile || isProcessing}
            className={`flex items-center px-6 py-3 rounded-lg font-medium transition-colors ${
              !audioFile || !selectedProfile || isProcessing
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                <PlayIcon className="h-5 w-5 mr-2" />
                Process Audio
              </>
            )}
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <WarningIcon className="h-5 w-5 text-red-600 mr-2" />
              <span className="text-red-800">{error}</span>
            </div>
          </div>
        )}

        {/* Processing Results */}
        {processingResult && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-50 p-6 rounded-lg"
          >
            <h4 className="font-medium text-gray-900 mb-4">Processing Results</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div>
                <span className="font-medium text-gray-600">Processing Time:</span>
                <div className={`text-2xl font-bold ${getLatencyStatus(processingResult.processingTime, processingResult.latencyTarget).color}`}>
                  {processingResult.processingTime}ms
                </div>
                <div className="text-sm text-gray-500">
                  Target: {processingResult.latencyTarget}ms
                </div>
              </div>
              
              <div>
                <span className="font-medium text-gray-600">Performance:</span>
                <div className={`text-lg font-semibold flex items-center ${getLatencyStatus(processingResult.processingTime, processingResult.latencyTarget).color}`}>
                  {React.createElement(getLatencyStatus(processingResult.processingTime, processingResult.latencyTarget).icon, { className: "h-5 w-5 mr-1" })}
                  {getLatencyStatus(processingResult.processingTime, processingResult.latencyTarget).status}
                </div>
              </div>
              
              <div>
                <span className="font-medium text-gray-600">Security:</span>
                <div className="space-y-1">
                  <div className={`text-sm font-medium ${processingResult.security.inputToneRemoved ? 'text-green-600' : 'text-gray-400'}`}>
                    {processingResult.security.inputToneRemoved ? '✓ Input Tone Removed' : '✗ Input Tone Preserved'}
                  </div>
                  <div className={`text-sm font-medium ${processingResult.security.antiForensic ? 'text-purple-600' : 'text-gray-400'}`}>
                    {processingResult.security.antiForensic ? '✓ Anti-forensic Applied' : '✗ No Anti-forensic'}
                  </div>
                </div>
              </div>
            </div>

            {/* Pipeline Steps */}
            <div className="mb-4">
              <span className="font-medium text-gray-600">Processing Pipeline:</span>
              <div className="flex items-center space-x-2 mt-2">
                {processingResult.processing.pipeline.map((step, index) => (
                  <React.Fragment key={step}>
                    <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                      {step}
                    </span>
                    {index < processingResult.processing.pipeline.length - 1 && (
                      <span className="text-gray-400">→</span>
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>

            {/* Download Button */}
            {processedAudioUrl && (
              <button
                onClick={downloadProcessedAudio}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <DownloadIcon className="h-5 w-5 mr-2" />
                Download Processed Audio
              </button>
            )}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default UnifiedVoiceProcessor;
