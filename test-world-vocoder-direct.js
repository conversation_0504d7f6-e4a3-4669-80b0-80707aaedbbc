/**
 * Direct test of the fixed WORLD vocoder to verify output size and quality
 */

const fs = require('fs');
const path = require('path');

// Import the WORLD vocoder service directly
const worldVocoderServicePath = './backend/services/worldVocoderService';

async function testWorldVocoderDirect() {
  try {
    console.log('🔧 Testing WORLD Vocoder Direct Processing');
    console.log('=====================================');
    
    // Import the service - need to use ts-node for TypeScript
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);
    
    // Use the test input file
    const inputFile = path.join(__dirname, 'test-input.wav');
    
    if (!fs.existsSync(inputFile)) {
      console.error('❌ Test input file not found:', inputFile);
      return;
    }
    
    // Get input file size
    const inputStats = fs.statSync(inputFile);
    console.log(`📁 Input file: ${inputFile}`);
    console.log(`📏 Input size: ${inputStats.size} bytes`);
    
    // Read the audio file
    const audioBuffer = fs.readFileSync(inputFile);
    console.log(`🎵 Audio buffer size: ${audioBuffer.length} bytes`);
    
    // Test with different profiles to verify they all work correctly
    const profiles = ['ROBOTIC_SYNTHETIC', 'NORMAL_VOICE', 'DEEP_VOICE', 'HIGH_VOICE'];
    
    for (const profile of profiles) {
      try {
        console.log(`\n🎯 Testing profile: ${profile}`);
        console.log('-----------------------------------');
        
        const startTime = Date.now();
        const result = await processAudioWithProfile(audioBuffer, profile);
        const processingTime = Date.now() - startTime;
        
        if (result && result.success && result.audioBuffer) {
          const outputSize = result.audioBuffer.length;
          const sizeRatio = outputSize / audioBuffer.length;
          
          console.log(`✅ Processing successful`);
          console.log(`📏 Output size: ${outputSize} bytes`);
          console.log(`📊 Size ratio: ${sizeRatio.toFixed(3)}x (should be ~1.0x)`);
          console.log(`⏱️ Processing time: ${processingTime}ms`);
          
          // Check for expected 1:1 ratio (allowing small variance for WAV headers)
          if (sizeRatio >= 0.9 && sizeRatio <= 1.1) {
            console.log(`✅ Size ratio is good (${sizeRatio.toFixed(3)}x)`);
          } else {
            console.log(`⚠️ Size ratio is off: ${sizeRatio.toFixed(3)}x (expected ~1.0x)`);
          }
          
          // Save output for manual inspection
          const outputPath = `./test-output-${profile.toLowerCase()}.wav`;
          fs.writeFileSync(outputPath, result.audioBuffer);
          console.log(`💾 Output saved: ${outputPath}`);
          
        } else {
          console.log(`❌ Processing failed for ${profile}`);
          if (result && result.error) {
            console.log(`   Error: ${result.error}`);
          }
        }
        
      } catch (error) {
        console.error(`❌ Error testing ${profile}:`, error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
testWorldVocoderDirect();
