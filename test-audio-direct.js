// Direct test of audio processing without authentication
const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');

function createTestWav(durationMs, sampleRate = 48000) {
  const samples = Math.floor(sampleRate * durationMs / 1000);
  const headerLength = 44;
  const dataLength = samples * 2; // 16-bit PCM
  const fileLength = headerLength + dataLength;
  
  const buffer = Buffer.alloc(fileLength);
  
  // WAV Header
  buffer.write('RIFF', 0, 4);
  buffer.writeUInt32LE(fileLength - 8, 4);
  buffer.write('WAVE', 8, 4);
  buffer.write('fmt ', 12, 4);
  buffer.writeUInt32LE(16, 16); // fmt chunk size
  buffer.writeUInt16LE(1, 20); // PCM format
  buffer.writeUInt16LE(1, 22); // mono
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(sampleRate * 2, 28); // byte rate
  buffer.writeUInt16LE(2, 32); // block align
  buffer.writeUInt16LE(16, 34); // bits per sample
  buffer.write('data', 36, 4);
  buffer.writeUInt32LE(dataLength, 40);
  
  // Add some simple sine wave data
  for (let i = 0; i < samples; i++) {
    const t = i / sampleRate;
    const amplitude = Math.sin(2 * Math.PI * 440 * t) * 0.3; // 440Hz tone at 30% volume
    const sample = Math.round(amplitude * 32767);
    buffer.writeInt16LE(sample, 44 + i * 2);
  }
  
  return buffer;
}

async function testAudioProcessing() {
  try {
    console.log('Testing audio processing with 440Hz tone...');
    
    // Create test audio (1 second, 440Hz sine wave)
    const testAudio = createTestWav(1000, 48000);
    console.log('Created test audio:', testAudio.length, 'bytes');
    
    // Save test input for comparison
    fs.writeFileSync('./test-input.wav', testAudio);
    console.log('Saved input audio to test-input.wav');
    
    const form = new FormData();
    form.append('audio', testAudio, {
      filename: 'test.wav',
      contentType: 'audio/wav'
    });
    form.append('profileName', 'NORMAL_VOICE');
    
    console.log('Sending request to server...');
    const response = await axios.post('http://localhost:3000/api/voice/realtime/test-profile', form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEyMzQ1Njc4OTAiLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE3NTI5NTE3NTI5NTIzNjUsImV4cCI6MTc1Mjk1NTk2NX0.ykQR6lq_11p8R7sdOxQo0e5y8COOCmquY5bhkB6GyKc' // Valid test token with correct secret
      },
      responseType: 'arraybuffer',
      timeout: 30000
    });
    
    console.log('✅ Response status:', response.status);
    console.log('✅ Response size:', response.data.length);
    console.log('✅ Content-Type:', response.headers['content-type']);
    
    // Save the result
    fs.writeFileSync('./test-output.wav', response.data);
    console.log('✅ Saved output to test-output.wav');
    
    // Compare sizes
    console.log('📊 Size comparison:');
    console.log('   Input:  ', testAudio.length, 'bytes');
    console.log('   Output: ', response.data.length, 'bytes');
    console.log('   Ratio:  ', (response.data.length / testAudio.length).toFixed(2));
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.status, error.response?.statusText);
    if (error.response?.data) {
      try {
        const errorText = Buffer.from(error.response.data).toString();
        console.error('❌ Error details:', errorText);
      } catch (e) {
        console.error('❌ Error data (bytes):', error.response.data.length);
      }
    }
    console.error('❌ Error message:', error.message);
  }
}

testAudioProcessing();
