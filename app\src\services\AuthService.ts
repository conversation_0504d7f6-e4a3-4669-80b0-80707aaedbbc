/**
 * Authentication Service
 * Handles calculator-to-chat authentication flow with backend integration
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { DeviceService } from './DeviceService';
import { NetworkService } from './NetworkService';
import Constants from 'expo-constants';

export interface AuthConfig {
  backendUrl: string;
  frontendUrl: string;
}

export interface AuthenticationResult {
  success: boolean;
  token?: string;
  error?: string;
  shouldTransitionToChat?: boolean;
}

export class AuthService {
  private static instance: AuthService;
  private config: AuthConfig;
  private currentToken: string | null = null;
  private constructor() {
    // Use Expo config for backend/frontend URLs with better fallback logic
    const extra = (Constants.expoConfig as any)?.extra || (Constants.manifest as any)?.extra || {};
    
    // Primary: Use environment variables from Expo config
    // Secondary: Use Constants.expoConfig (modern Expo)
    // Tertiary: Use old manifest format
    // Fallback: Use hardcoded development URLs
    
    const backendUrl = process.env.EXPO_PUBLIC_BACKEND_URL || 
                      extra.backendUrl || 
                      (__DEV__ ? 'http://localhost:3000' : 'http://localhost:3000');
                      
    const frontendUrl = process.env.EXPO_PUBLIC_FRONTEND_URL || 
                       extra.frontendUrl || 
                       (__DEV__ ? 'http://localhost:3001' : 'http://localhost:3001');
    
    this.config = {
      backendUrl,
      frontendUrl,
    };
    
    // Debug: Log the configuration being used
    console.log('🔧 AuthService config:', {
      backendUrl: this.config.backendUrl,
      frontendUrl: this.config.frontendUrl,
      buildEnv: extra.buildEnv || 'unknown',
      debugMode: extra.debugMode,
      isDev: __DEV__,
      hasEnvBackend: !!process.env.EXPO_PUBLIC_BACKEND_URL,
      hasExtraBackend: !!extra.backendUrl
    });
    
    this.loadStoredToken();
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Main authentication flow for calculator
   * This is called when user taps "=" in calculator
   */
  public async authenticateExpression(expression: string): Promise<AuthenticationResult> {
    try {
      console.log('🔐 Starting authentication for expression:', expression);

      // Step 1: Get device fingerprint
      const deviceFingerprint = await DeviceService.getInstance().getDeviceFingerprint();
      
      // Step 2: Authenticate with backend using expression + device fingerprint
      // The backend will handle expression validation internally
      const authResult = await this.authenticateWithBackend(expression, deviceFingerprint);
      
      if (authResult.success && authResult.token) {
        // Store token securely
        await this.storeToken(authResult.token);
        this.currentToken = authResult.token;
        
        console.log('🎉 Authentication successful - transitioning to chat');
        return {
          success: true,
          token: authResult.token,
          shouldTransitionToChat: true,
        };
      }

      console.log('❌ Backend authentication failed');
      return {
        success: false,
        error: authResult.error || 'Authentication failed',
        shouldTransitionToChat: false,
      };

    } catch (error) {
      console.error('Authentication error:', error);
      return {
        success: false,
        error: 'Authentication system error',
        shouldTransitionToChat: false,
      };
    }
  }
  /**
   * Authenticate with backend using expression-only authentication
   */
  private async authenticateWithBackend(
    expression: string, 
    deviceFingerprint: string
  ): Promise<AuthenticationResult> {
    try {
      const url = `${this.config.backendUrl}/api/auth/user/login`;
      const requestBody = {
        username: 'mobileuser', // Fixed username for expression-based auth
        expression, // EXACT expression: "12 + 5*7 - 3*2 + 8/4"
        deviceFingerprint,
      };
      
      console.log('🌐 Making request to:', url);
      console.log('📦 Request body:', requestBody);
      console.log('🔑 Device fingerprint:', deviceFingerprint);
      
      // Test connectivity first
      const networkService = NetworkService.getInstance();
      const isConnected = await networkService.testConnectivity(this.config.backendUrl);
      
      if (!isConnected) {
        throw new Error('Backend connectivity test failed');
      }
      
      // Make the authentication request using NetworkService
      const networkResponse = await networkService.post(url, requestBody, {
        'X-Device-Fingerprint': deviceFingerprint,
      });

      console.log('📡 Response status:', networkResponse.status);
      console.log('📡 Response ok:', networkResponse.ok);
      console.log('🔍 Backend response:', networkResponse.data);
      
      if (!networkResponse.ok) {
        throw new Error(networkResponse.data?.error || 'Authentication failed');
      }

      // Backend returns token directly, not wrapped in success object
      const hasToken = networkResponse.data?.token && networkResponse.data.token.length > 0;
      
      return {
        success: hasToken,
        token: networkResponse.data?.token,
        error: hasToken ? undefined : 'No token received',
      };
    } catch (error) {
      console.error('Backend authentication error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  /**
   * Calculate normal mathematical result (for disguise)
   */
  public calculateExpression(expression: string): number | string {
    try {
      // Remove any non-mathematical characters for safety
      const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
      
      // Use a safe mathematical parser instead of Function constructor
      // which is not supported in Hermes
      const result = this.safeEvaluate(sanitized);
      
      if (typeof result === 'number' && !isNaN(result)) {
        return result;
      }
      
      return 'Error';
    } catch (error) {
      console.error('Calculation error:', error);
      return 'Error';
    }
  }

  /**
   * Safe mathematical expression evaluator for Hermes compatibility
   */
  private safeEvaluate(expression: string): number {
    // Simple calculator implementation that doesn't use eval or Function
    try {
      // Replace common operations
      let expr = expression.trim();
      
      // Handle parentheses recursively
      while (expr.includes('(')) {
        const start = expr.lastIndexOf('(');
        const end = expr.indexOf(')', start);
        if (end === -1) throw new Error('Mismatched parentheses');
        
        const subExpr = expr.substring(start + 1, end);
        const subResult = this.safeEvaluate(subExpr);
        expr = expr.substring(0, start) + subResult.toString() + expr.substring(end + 1);
      }
      
      // Handle multiplication and division first
      expr = this.processOperations(expr, ['*', '/']);
      
      // Handle addition and subtraction
      expr = this.processOperations(expr, ['+', '-']);
      
      return parseFloat(expr);
    } catch (error) {
      throw new Error('Invalid expression');
    }
  }

  /**
   * Process mathematical operations in order of precedence
   */
  private processOperations(expression: string, operators: string[]): string {
    let expr = expression;
    
    for (const op of operators) {
      while (expr.includes(op)) {
        const regex = new RegExp(`(-?\\d+(?:\\.\\d+)?)\\s*\\${op}\\s*(-?\\d+(?:\\.\\d+)?)`);
        const match = expr.match(regex);
        
        if (!match) break;
        
        const left = parseFloat(match[1]);
        const right = parseFloat(match[2]);
        let result: number;
        
        switch (op) {
          case '+':
            result = left + right;
            break;
          case '-':
            result = left - right;
            break;
          case '*':
            result = left * right;
            break;
          case '/':
            if (right === 0) throw new Error('Division by zero');
            result = left / right;
            break;
          default:
            throw new Error('Unknown operator');
        }
        
        expr = expr.replace(match[0], result.toString());
      }
    }
    
    return expr;
  }

  /**
   * Check if user is authenticated for chat
   */
  public async isAuthenticated(): Promise<boolean> {
    try {
      if (!this.currentToken) {
        await this.loadStoredToken();
      }

      if (!this.currentToken) {
        return false;
      }

      // Verify token with backend
      const response = await fetch(`${this.config.backendUrl}/api/auth/verify`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.currentToken}`,
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Token verification error:', error);
      return false;
    }
  }

  /**
   * Get current authentication token
   */
  public getToken(): string | null {
    return this.currentToken;
  }

  /**
   * Get current user information from stored token
   */
  public getCurrentUser(): any | null {
    if (!this.currentToken) {
      return null;
    }

    try {
      // Decode JWT token to get user info using compatible base64 decoding
      const base64Url = this.currentToken.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      
      // Use compatible base64 decoding for Hermes
      const jsonPayload = this.base64Decode(base64);

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('❌ AuthService: Error decoding token:', error);
      return null;
    }
  }

  /**
   * Compatible base64 decoder for Hermes
   */
  private base64Decode(str: string): string {
    try {
      // Add padding if needed
      const padding = '='.repeat((4 - str.length % 4) % 4);
      const padded = str + padding;
      
      // Use atob if available (most cases)
      if (typeof atob !== 'undefined') {
        return decodeURIComponent(atob(padded).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
      }
      
      // Fallback implementation for environments without atob
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
      let result = '';
      
      for (let i = 0; i < padded.length; i += 4) {
        const encoded = padded.slice(i, i + 4);
        let binary = '';
        
        for (let j = 0; j < encoded.length; j++) {
          if (encoded[j] !== '=') {
            const index = chars.indexOf(encoded[j]);
            binary += index.toString(2).padStart(6, '0');
          }
        }
        
        for (let k = 0; k < binary.length; k += 8) {
          const byte = binary.slice(k, k + 8);
          if (byte.length === 8) {
            result += String.fromCharCode(parseInt(byte, 2));
          }
        }
      }
      
      return decodeURIComponent(result.split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
    } catch (error) {
      console.error('❌ Base64 decode error:', error);
      return '';
    }
  }

  /**
   * Logout and clear stored token
   */
  public async logout(): Promise<void> {
    try {
      if (this.currentToken) {
        // Notify backend of logout
        await fetch(`${this.config.backendUrl}/api/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.currentToken}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage
      this.currentToken = null;
      await AsyncStorage.removeItem('auth_token');
    }
  }

  /**
   * Store token securely
   */
  private async storeToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('auth_token', token);
    } catch (error) {
      console.error('Token storage error:', error);
    }
  }

  /**
   * Load stored token
   */
  private async loadStoredToken(): Promise<void> {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      this.currentToken = token;
    } catch (error) {
      console.error('Token loading error:', error);
      this.currentToken = null;
    }
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<AuthConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  public getConfig(): AuthConfig {
    return { ...this.config };
  }

  /**
   * Get device fingerprint (delegates to DeviceService)
   */
  public async getDeviceFingerprint(): Promise<string> {
    return await DeviceService.getInstance().getDeviceFingerprint();
  }
}

export default AuthService;
