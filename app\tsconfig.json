{"compilerOptions": {"jsx": "react-jsx", "strict": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "moduleResolution": "bundler", "resolveJsonModule": true, "noEmit": true, "target": "esnext", "lib": ["esnext", "dom"], "allowJs": true, "isolatedModules": true, "baseUrl": ".", "paths": {"*": ["*", "*.ios", "*.android"]}, "module": "esnext", "typeRoots": ["./types", "./node_modules/@types"]}, "extends": "expo/tsconfig.base", "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "types/**/*.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"]}