/**
 * Advanced Voice Profiles for Real-Time Processing
 * Specialized profiles designed for complete input tone removal and anti-forensic capabilities
 * Optimized for real-time voice calls with <100ms latency
 */

import { UnifiedVoiceProfile } from './unifiedVoiceProcessor';
import { NEUTRALIZATION_PROFILES } from './lightweightVoiceNeutralizer';
import { VOICE_PROFILES } from './voiceModulation';

export const ADVANCED_VOICE_PROFILES: Record<string, UnifiedVoiceProfile> = {
    // ULTRA-SECURE PROFILES - Maximum security with complete input tone removal
    
    ULTRA_SECURE_STEALTH: {
        name: 'Ultra Secure Stealth',
        description: 'Maximum security profile with complete input tone removal and anti-forensic protection - designed for sensitive communications',
        type: 'unified',
        pipeline: {
            neutralizationFirst: true,
            soxModulation: true,
            antiForensic: true,
            realTimeOptimized: true
        },
        neutralization: {
            ...NEUTRALIZATION_PROFILES.REAL_TIME_MEDIUM,
            preserveClarity: true,
            processing: {
                ...NEUTRALIZATION_PROFILES.REAL_TIME_MEDIUM.processing,
                f0Neutralization: true,
                formantNormalization: true,
                spectralSmoothing: 0.8,      // High spectral smoothing
                noiseLevel: 0.02             // Higher noise for masking
            }
        },
        modulation: {
            ...VOICE_PROFILES.ANONYMOUS,
            pitch: -8,                       // Significant pitch change
            tempo: 0.85,                     // Slower tempo
            reverb: 25,                      // More reverb for masking
            distortion: 15,                  // Higher distortion
            formant: -400,                   // Significant formant shift
            chorus: true,
            normalize: true,
            customSoxArgs: [
                'highpass 80',               // Remove low frequencies
                'lowpass 7000',              // Remove high frequencies
                'compand 0.3,1 6:-70,-60,-20', // Dynamic range compression
                'tremolo 0.1 40'             // Subtle tremolo for masking
            ]
        },
        performance: {
            latencyTarget: 80,
            qualityMode: 'balanced',
            parallelProcessing: true,
            streamingMode: true
        },
        security: {
            inputToneRemoval: true,
            reversible: false,
            antiForensic: true,
            encryptionSupport: true
        }
    },

    ULTRA_SECURE_PHANTOM: {
        name: 'Ultra Secure Phantom',
        description: 'Phantom voice profile that completely removes all identifiable characteristics - impossible to trace back to original speaker',
        type: 'unified',
        pipeline: {
            neutralizationFirst: true,
            soxModulation: true,
            antiForensic: true,
            realTimeOptimized: true
        },
        neutralization: {
            ...NEUTRALIZATION_PROFILES.OFFLINE_HEAVY,
            preserveClarity: true,
            processing: {
                ...NEUTRALIZATION_PROFILES.OFFLINE_HEAVY.processing,
                f0Neutralization: true,
                formantNormalization: true,
                spectralSmoothing: 0.9,      // Maximum spectral smoothing
                noiseLevel: 0.025           // High noise injection
            }
        },
        modulation: {
            ...VOICE_PROFILES.DEEP_SECURE,
            pitch: -10,                      // Extreme pitch change
            tempo: 0.80,                     // Significantly slower
            reverb: 30,                      // Heavy reverb
            distortion: 20,                  // High distortion
            formant: -500,                   // Extreme formant shift
            chorus: true,
            normalize: true,
            customSoxArgs: [
                'highpass 100',              // Aggressive high-pass
                'lowpass 6000',              // Aggressive low-pass
                'compand 0.2,1 6:-70,-60,-15', // Heavy compression
                'flanger 0.6 0.87 3.0 0.9 0.5', // Flanger for masking
                'phaser 0.8 0.74 3.0 0.4 0.5'   // Phaser for additional masking
            ]
        },
        performance: {
            latencyTarget: 90,
            qualityMode: 'balanced',
            parallelProcessing: true,
            streamingMode: true
        },
        security: {
            inputToneRemoval: true,
            reversible: false,
            antiForensic: true,
            encryptionSupport: true
        }
    },

    // SPEED-OPTIMIZED PROFILES - Fast processing with good security
    
    SPEED_SECURE_LIGHTNING: {
        name: 'Speed Secure Lightning',
        description: 'Ultra-fast processing with input tone removal - optimized for <30ms latency',
        type: 'unified',
        pipeline: {
            neutralizationFirst: true,
            soxModulation: true,
            antiForensic: false,             // Disabled for speed
            realTimeOptimized: true
        },
        neutralization: {
            ...NEUTRALIZATION_PROFILES.REAL_TIME_LIGHT,
            preserveClarity: true,
            processing: {
                ...NEUTRALIZATION_PROFILES.REAL_TIME_LIGHT.processing,
                f0Neutralization: true,
                formantNormalization: false, // Disabled for speed
                spectralSmoothing: 0.1,      // Minimal smoothing
                noiseLevel: 0.005           // Minimal noise
            }
        },
        modulation: {
            ...VOICE_PROFILES.SECURE_MALE,
            pitch: -3,                       // Minimal pitch change
            tempo: 0.95,                     // Slight tempo change
            reverb: 5,                       // Minimal reverb
            distortion: 3,                   // Minimal distortion
            formant: -100,                   // Minimal formant shift
            chorus: false,                   // Disabled for speed
            normalize: true,
            customSoxArgs: [
                'norm -1'                    // Only normalization
            ]
        },
        performance: {
            latencyTarget: 30,
            qualityMode: 'speed',
            parallelProcessing: true,
            streamingMode: true
        },
        security: {
            inputToneRemoval: true,
            reversible: false,
            antiForensic: false,
            encryptionSupport: false
        }
    },

    // QUALITY-OPTIMIZED PROFILES - Best quality with security
    
    QUALITY_SECURE_CRYSTAL: {
        name: 'Quality Secure Crystal',
        description: 'Maximum quality processing with complete input tone removal - optimized for clarity and security',
        type: 'unified',
        pipeline: {
            neutralizationFirst: true,
            soxModulation: true,
            antiForensic: true,
            realTimeOptimized: false        // Quality over speed
        },
        neutralization: {
            ...NEUTRALIZATION_PROFILES.OFFLINE_HEAVY,
            preserveClarity: true,
            processing: {
                ...NEUTRALIZATION_PROFILES.OFFLINE_HEAVY.processing,
                f0Neutralization: true,
                formantNormalization: true,
                spectralSmoothing: 0.6,      // Balanced smoothing
                noiseLevel: 0.008           // Minimal noise for clarity
            }
        },
        modulation: {
            ...VOICE_PROFILES.ANONYMOUS,
            pitch: -6,                       // Moderate pitch change
            tempo: 0.88,                     // Moderate tempo change
            reverb: 18,                      // Moderate reverb
            distortion: 10,                  // Moderate distortion
            formant: -250,                   // Moderate formant shift
            chorus: true,
            normalize: true,
            customSoxArgs: [
                'highpass 60',               // Gentle high-pass
                'lowpass 8000',              // Gentle low-pass
                'compand 0.3,1 6:-70,-60,-25', // Gentle compression
                'equalizer 1000 0.5q -2',   // Reduce harsh frequencies
                'equalizer 3000 0.5q -1'    // Smooth high frequencies
            ]
        },
        performance: {
            latencyTarget: 120,
            qualityMode: 'quality',
            parallelProcessing: true,
            streamingMode: false
        },
        security: {
            inputToneRemoval: true,
            reversible: false,
            antiForensic: true,
            encryptionSupport: true
        }
    },

    // SPECIALIZED PROFILES - For specific use cases
    
    EMERGENCY_SECURE: {
        name: 'Emergency Secure',
        description: 'Emergency profile for critical situations - maximum security with acceptable latency',
        type: 'unified',
        pipeline: {
            neutralizationFirst: true,
            soxModulation: true,
            antiForensic: true,
            realTimeOptimized: true
        },
        neutralization: {
            ...NEUTRALIZATION_PROFILES.OFFLINE_HEAVY,
            preserveClarity: true,
            processing: {
                ...NEUTRALIZATION_PROFILES.OFFLINE_HEAVY.processing,
                f0Neutralization: true,
                formantNormalization: true,
                spectralSmoothing: 0.95,
                noiseLevel: 0.03
            }
        },
        modulation: {
            ...VOICE_PROFILES.DEEP_SECURE,
            pitch: -7,
            tempo: 0.87,
            reverb: 22,
            distortion: 12,
            formant: -350,
            chorus: true,
            normalize: true,
            customSoxArgs: [
                'highpass 90',
                'lowpass 6500',
                'compand 0.25,1 6:-70,-60,-18',
                'tremolo 0.05 20'            // Subtle tremolo
            ]
        },
        performance: {
            latencyTarget: 70,
            qualityMode: 'balanced',
            parallelProcessing: true,
            streamingMode: true
        },
        security: {
            inputToneRemoval: true,
            reversible: false,
            antiForensic: true,
            encryptionSupport: true
        }
    },

    BROADCAST_SECURE: {
        name: 'Broadcast Secure',
        description: 'Optimized for broadcast scenarios with complete anonymity and professional quality',
        type: 'unified',
        pipeline: {
            neutralizationFirst: true,
            soxModulation: true,
            antiForensic: true,
            realTimeOptimized: false
        },
        neutralization: {
            ...NEUTRALIZATION_PROFILES.OFFLINE_HEAVY,
            preserveClarity: true,
            processing: {
                ...NEUTRALIZATION_PROFILES.OFFLINE_HEAVY.processing,
                f0Neutralization: true,
                formantNormalization: true,
                spectralSmoothing: 0.5,      // Balanced for broadcast quality
                noiseLevel: 0.005            // Minimal noise for broadcast
            }
        },
        modulation: {
            ...VOICE_PROFILES.ANONYMOUS,
            pitch: -5,
            tempo: 0.90,
            reverb: 15,
            distortion: 8,
            formant: -200,
            chorus: false,                   // Cleaner for broadcast
            normalize: true,
            customSoxArgs: [
                'highpass 80',
                'lowpass 7500',
                'compand 0.3,1 6:-70,-60,-30', // Broadcast-style compression
                'equalizer 200 1q -1',      // Reduce muddiness
                'equalizer 2000 0.5q 1',    // Enhance presence
                'equalizer 5000 0.5q -0.5'  // Smooth highs
            ]
        },
        performance: {
            latencyTarget: 150,
            qualityMode: 'quality',
            parallelProcessing: true,
            streamingMode: false
        },
        security: {
            inputToneRemoval: true,
            reversible: false,
            antiForensic: true,
            encryptionSupport: true
        }
    }
};

/**
 * Get profile recommendations based on requirements
 */
export function getProfileRecommendations(requirements: {
    maxLatency?: number;
    prioritySecurity?: boolean;
    priorityQuality?: boolean;
    prioritySpeed?: boolean;
    useCase?: 'call' | 'broadcast' | 'emergency' | 'general';
}): string[] {
    const recommendations: string[] = [];
    
    if (requirements.useCase === 'emergency') {
        recommendations.push('EMERGENCY_SECURE');
    }
    
    if (requirements.useCase === 'broadcast') {
        recommendations.push('BROADCAST_SECURE', 'QUALITY_SECURE_CRYSTAL');
    }
    
    if (requirements.prioritySpeed || (requirements.maxLatency && requirements.maxLatency < 50)) {
        recommendations.push('SPEED_SECURE_LIGHTNING');
    }
    
    if (requirements.prioritySecurity) {
        recommendations.push('ULTRA_SECURE_STEALTH', 'ULTRA_SECURE_PHANTOM');
    }
    
    if (requirements.priorityQuality) {
        recommendations.push('QUALITY_SECURE_CRYSTAL', 'BROADCAST_SECURE');
    }
    
    // Default recommendations
    if (recommendations.length === 0) {
        recommendations.push('ULTRA_SECURE_STEALTH', 'SPEED_SECURE_LIGHTNING', 'QUALITY_SECURE_CRYSTAL');
    }
    
    return recommendations;
}
