/**
 * Isolated Media Service
 * Implements Instagram-like image selection that only shows selected images
 * Prevents access to full photo library for privacy
 */

import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import { MediaAttachment } from './MediaService';

interface IsolatedMediaItem {
  id: string;
  uri: string;
  name: string;
  type: string;
  size: number;
  mimeType: string;
  isSelected: boolean;
  isolatedUri?: string; // Isolated copy in app directory
  thumbnailUri?: string;
}

interface MediaSelectionOptions {
  allowsEditing?: boolean;
  quality?: number;
  allowsMultipleSelection?: boolean;
  mediaTypes?: 'images' | 'videos' | 'all';
}

export class IsolatedMediaService {
  private isolatedMediaDir: string;
  private selectedMedia: Map<string, IsolatedMediaItem> = new Map();

  constructor() {
    this.isolatedMediaDir = `${FileSystem.documentDirectory}CCALC_Isolated_Media/`;
    this.initializeIsolatedDirectory();
  }

  /**
   * Initialize isolated media directory
   */
  private async initializeIsolatedDirectory(): Promise<void> {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.isolatedMediaDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.isolatedMediaDir, { intermediates: true });
        console.log('✅ Isolated media directory created');
      }
    } catch (error) {
      console.error('❌ Failed to create isolated media directory:', error);
    }
  }

  /**
   * Request camera permissions
   */
  private async requestCameraPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Please grant camera permission to take photos.',
          [{ text: 'OK' }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('❌ Camera permission error:', error);
      return false;
    }
  }

  /**
   * Request media library permissions (optimized for Instagram-style limited access)
   */
  private async requestMediaLibraryPermissions(): Promise<boolean> {
    try {
      // First check current permission status
      const currentPermission = await ImagePicker.getMediaLibraryPermissionsAsync();

      if (currentPermission.status === 'granted') {
        return true;
      }

      // Request permission with optimized messaging
      const { status, accessPrivileges } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status === 'granted') {
        // Log the type of access granted for debugging
        console.log('📱 Media library access granted:', {
          status,
          accessPrivileges,
          canAskAgain: currentPermission.canAskAgain
        });
        return true;
      }

      // Handle different permission states
      if (status === 'denied' && currentPermission.canAskAgain) {
        Alert.alert(
          'Photos & Videos Access',
          'To share photos and videos, please allow access to your photo library. You can choose to share only selected photos.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => {
              // On iOS, this will open the app settings
              console.log('📱 User should open Settings to grant permission');
            }}
          ]
        );
      } else {
        Alert.alert(
          'Limited Photo Access',
          'Please select photos to share. Only selected photos will be accessible to the app.',
          [{ text: 'OK' }]
        );
      }

      return false;
    } catch (error) {
      console.error('❌ Media library permission error:', error);
      return false;
    }
  }

  /**
   * Take photo with camera (Instagram-like)
   */
  public async takePhoto(options: MediaSelectionOptions = {}): Promise<MediaAttachment | null> {
    try {
      const hasPermission = await this.requestCameraPermissions();
      if (!hasPermission) return null;

      console.log('📷 Opening camera for photo capture...');

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: options.allowsEditing ?? true,
        quality: options.quality ?? 0.8,
        base64: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log('📷 Camera capture cancelled');
        return null;
      }

      const asset = result.assets[0];

      // Create isolated copy
      const isolatedMedia = await this.createIsolatedCopy(asset);
      if (!isolatedMedia) return null;

      // Add to selected media
      this.selectedMedia.set(isolatedMedia.id, isolatedMedia);

      console.log('✅ Photo captured and isolated:', isolatedMedia.name);

      return this.convertToMediaAttachment(isolatedMedia);

    } catch (error) {
      console.error('❌ Camera capture error:', error);
      Alert.alert('Error', 'Failed to capture photo. Please try again.');
      return null;
    }
  }

  /**
   * Select image(s) from library (limited access)
   * Returns single MediaAttachment for single selection, array for multiple selection
   */
  public async selectFromLibrary(options: MediaSelectionOptions = {}): Promise<MediaAttachment | MediaAttachment[] | null> {
    try {
      const hasPermission = await this.requestMediaLibraryPermissions();
      if (!hasPermission) return null;

      console.log('📱 Opening photo library with limited access...');

      const mediaTypeOptions = options.mediaTypes === 'videos'
        ? ImagePicker.MediaTypeOptions.Videos
        : options.mediaTypes === 'all'
          ? ImagePicker.MediaTypeOptions.All
          : ImagePicker.MediaTypeOptions.Images;

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: mediaTypeOptions,
        allowsEditing: options.allowsEditing ?? false, // Disable editing for multiple selection
        quality: options.quality ?? 0.8,
        allowsMultipleSelection: options.allowsMultipleSelection ?? false,
        selectionLimit: options.allowsMultipleSelection ? 10 : 1, // Max 10 images
        base64: false,
        // Optimized settings for better performance
        exif: false, // Don't include EXIF data for faster processing
        presentationStyle: 'pageSheet', // Better UX on iOS
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log('📱 Library selection cancelled');
        return null;
      }

      // Handle multiple selection (max 10 images)
      if (options.allowsMultipleSelection && result.assets.length > 1) {
        const maxImages = 10;
        const assetsToProcess = result.assets.slice(0, maxImages);

        console.log(`📱 Processing ${assetsToProcess.length} selected images (max ${maxImages})`);

        const mediaAttachments: MediaAttachment[] = [];

        for (const asset of assetsToProcess) {
          const isolatedMedia = await this.createIsolatedCopy(asset);
          if (isolatedMedia) {
            this.selectedMedia.set(isolatedMedia.id, isolatedMedia);
            mediaAttachments.push(this.convertToMediaAttachment(isolatedMedia));
          }
        }

        console.log('✅ Multiple images selected and isolated:', mediaAttachments.length);
        return mediaAttachments;
      } else {
        // Single selection
        const asset = result.assets[0];

        // Create isolated copy
        const isolatedMedia = await this.createIsolatedCopy(asset);
        if (!isolatedMedia) return null;

        // Add to selected media
        this.selectedMedia.set(isolatedMedia.id, isolatedMedia);

        console.log('✅ Image selected and isolated:', isolatedMedia.name);
        return this.convertToMediaAttachment(isolatedMedia);
      }

    } catch (error) {
      console.error('❌ Library selection error:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
      return null;
    }
  }

  /**
   * Select document (optimized for all storage locations and file types)
   */
  public async selectDocument(): Promise<MediaAttachment | null> {
    try {
      console.log('📄 Opening optimized document picker...');

      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*', // Allow all file types for maximum compatibility
        copyToCacheDirectory: true, // Copy to cache for better access
        multiple: false,
        // Optimized settings for better storage access
        presentationStyle: 'pageSheet', // Better UX on iOS
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log('📄 Document selection cancelled');
        return null;
      }

      const asset = result.assets[0];

      // Validate file size (50MB limit)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (asset.size && asset.size > maxSize) {
        Alert.alert(
          'File Too Large',
          `The selected file is ${(asset.size / (1024 * 1024)).toFixed(1)}MB. Please select a file smaller than 50MB.`,
          [{ text: 'OK' }]
        );
        return null;
      }

      // Create isolated copy
      const isolatedMedia = await this.createIsolatedCopy({
        uri: asset.uri,
        fileName: asset.name,
        fileSize: asset.size,
        mimeType: asset.mimeType || 'application/octet-stream',
      });

      if (!isolatedMedia) return null;

      // Add to selected media
      this.selectedMedia.set(isolatedMedia.id, isolatedMedia);

      console.log('✅ Document selected and isolated:', isolatedMedia.name);

      return this.convertToMediaAttachment(isolatedMedia);

    } catch (error) {
      console.error('❌ Document selection error:', error);
      Alert.alert('Error', 'Failed to select document. Please try again.');
      return null;
    }
  }

  /**
   * Create optimized isolated copy of selected media
   */
  private async createIsolatedCopy(asset: any): Promise<IsolatedMediaItem | null> {
    try {
      const startTime = Date.now();
      const id = `isolated_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const originalUri = asset.uri;
      const fileName = asset.fileName || asset.name || `media_${id}`;
      const fileSize = asset.fileSize || asset.size || 0;
      const mimeType = asset.mimeType || asset.type || this.getMimeTypeFromUri(originalUri);

      // Create isolated file path
      const fileExtension = this.getFileExtension(fileName);
      const isolatedFileName = `${id}${fileExtension}`;
      const isolatedUri = `${this.isolatedMediaDir}${isolatedFileName}`;

      // Ensure isolated directory exists
      await this.initializeIsolatedDirectory();

      // Optimized file copy with error handling
      try {
        await FileSystem.copyAsync({
          from: originalUri,
          to: isolatedUri,
        });
      } catch (copyError) {
        console.error('❌ File copy failed, trying alternative method:', copyError);

        // Alternative method: read and write (slower but more reliable)
        const fileContent = await FileSystem.readAsStringAsync(originalUri, {
          encoding: FileSystem.EncodingType.Base64,
        });
        await FileSystem.writeAsStringAsync(isolatedUri, fileContent, {
          encoding: FileSystem.EncodingType.Base64,
        });
      }

      // Verify the copy
      const fileInfo = await FileSystem.getInfoAsync(isolatedUri);
      if (!fileInfo.exists) {
        throw new Error('Failed to create isolated copy');
      }

      const copyDuration = Date.now() - startTime;
      console.log(`✅ File copied to isolated storage in ${copyDuration}ms:`, isolatedFileName);

      // Generate thumbnail for images/videos
      let thumbnailUri: string | undefined;
      if (mimeType.startsWith('image/')) {
        thumbnailUri = isolatedUri; // For images, use the image itself as thumbnail
      }

      const isolatedMedia: IsolatedMediaItem = {
        id,
        uri: originalUri,
        name: fileName,
        type: this.getFileCategory(mimeType),
        size: fileSize,
        mimeType,
        isSelected: true,
        isolatedUri,
        thumbnailUri,
      };

      console.log('✅ Created isolated copy:', {
        id,
        name: fileName,
        size: this.formatFileSize(fileSize),
        isolatedPath: isolatedFileName
      });

      return isolatedMedia;

    } catch (error) {
      console.error('❌ Failed to create isolated copy:', error);
      return null;
    }
  }

  /**
   * Convert isolated media to MediaAttachment format
   */
  private convertToMediaAttachment(isolatedMedia: IsolatedMediaItem): MediaAttachment {
    return {
      id: isolatedMedia.id,
      name: isolatedMedia.name,
      type: isolatedMedia.type,
      size: isolatedMedia.size,
      uri: isolatedMedia.isolatedUri || isolatedMedia.uri, // Use isolated URI
      mimeType: isolatedMedia.mimeType,
      isImage: isolatedMedia.mimeType.startsWith('image/'),
      isVideo: isolatedMedia.mimeType.startsWith('video/'),
      isAudio: isolatedMedia.mimeType.startsWith('audio/'),
      thumbnailUri: isolatedMedia.thumbnailUri,
    };
  }

  /**
   * Get all selected (isolated) media
   */
  public getSelectedMedia(): MediaAttachment[] {
    return Array.from(this.selectedMedia.values()).map(item =>
      this.convertToMediaAttachment(item)
    );
  }

  /**
   * Remove media from selection and delete isolated copy
   */
  public async removeSelectedMedia(mediaId: string): Promise<boolean> {
    try {
      const media = this.selectedMedia.get(mediaId);
      if (!media) return false;

      // Delete isolated file
      if (media.isolatedUri) {
        const fileInfo = await FileSystem.getInfoAsync(media.isolatedUri);
        if (fileInfo.exists) {
          await FileSystem.deleteAsync(media.isolatedUri);
          console.log('🗑️ Deleted isolated media file:', media.name);
        }
      }

      // Remove from selection
      this.selectedMedia.delete(mediaId);
      console.log('✅ Removed media from selection:', media.name);

      return true;

    } catch (error) {
      console.error('❌ Failed to remove selected media:', error);
      return false;
    }
  }

  /**
   * Clear all selected media
   */
  public async clearAllSelectedMedia(): Promise<void> {
    try {
      const mediaIds = Array.from(this.selectedMedia.keys());

      for (const mediaId of mediaIds) {
        await this.removeSelectedMedia(mediaId);
      }

      console.log('✅ Cleared all selected media');

    } catch (error) {
      console.error('❌ Failed to clear selected media:', error);
    }
  }

  /**
   * Get file extension from filename
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot > 0 ? fileName.substring(lastDot) : '';
  }

  /**
   * Get MIME type from URI
   */
  private getMimeTypeFromUri(uri: string): string {
    const extension = this.getFileExtension(uri).toLowerCase();

    const mimeTypes: { [key: string]: string } = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.mp4': 'video/mp4',
      '.mov': 'video/quicktime',
      '.avi': 'video/x-msvideo',
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.pdf': 'application/pdf',
      '.txt': 'text/plain',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  }

  /**
   * Get file category from MIME type
   */
  private getFileCategory(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.includes('pdf') || mimeType.includes('document')) return 'document';
    return 'file';
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get storage usage statistics
   */
  public async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    formattedSize: string;
  }> {
    try {
      let totalSize = 0;
      let totalFiles = 0;

      for (const media of this.selectedMedia.values()) {
        if (media.isolatedUri) {
          const fileInfo = await FileSystem.getInfoAsync(media.isolatedUri);
          if (fileInfo.exists && fileInfo.size) {
            totalSize += fileInfo.size;
            totalFiles++;
          }
        }
      }

      return {
        totalFiles,
        totalSize,
        formattedSize: this.formatFileSize(totalSize),
      };

    } catch (error) {
      console.error('❌ Failed to get storage stats:', error);
      return {
        totalFiles: 0,
        totalSize: 0,
        formattedSize: '0 Bytes',
      };
    }
  }

  /**
   * Clean up old isolated media files (run periodically)
   */
  public async cleanupOldMedia(): Promise<void> {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.isolatedMediaDir);
      if (!dirInfo.exists) return;

      const files = await FileSystem.readDirectoryAsync(this.isolatedMediaDir);
      const now = Date.now();
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
      let cleanedCount = 0;

      for (const fileName of files) {
        try {
          const filePath = `${this.isolatedMediaDir}${fileName}`;
          const fileInfo = await FileSystem.getInfoAsync(filePath);

          if (fileInfo.exists && fileInfo.modificationTime) {
            const fileAge = now - fileInfo.modificationTime;

            if (fileAge > maxAge) {
              await FileSystem.deleteAsync(filePath, { idempotent: true });
              cleanedCount++;
            }
          }
        } catch (error) {
          console.warn('⚠️ Failed to clean up file:', fileName, error);
        }
      }

      if (cleanedCount > 0) {
        console.log(`🗑️ Cleaned up ${cleanedCount} old isolated media files`);
      }
    } catch (error) {
      console.error('❌ Failed to cleanup old media:', error);
    }
  }

  /**
   * Clear all selected media and optionally clean up files
   */
  public async clearSelectedMedia(deleteFiles: boolean = false): Promise<void> {
    try {
      if (deleteFiles) {
        for (const media of this.selectedMedia.values()) {
          if (media.isolatedUri) {
            const fileInfo = await FileSystem.getInfoAsync(media.isolatedUri);
            if (fileInfo.exists) {
              await FileSystem.deleteAsync(media.isolatedUri, { idempotent: true });
            }
          }
        }
      }

      this.selectedMedia.clear();
      console.log('🗑️ Selected media cleared');
    } catch (error) {
      console.error('❌ Failed to clear selected media:', error);
    }
  }
}

export default IsolatedMediaService;
