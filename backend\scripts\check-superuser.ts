/**
 * Check if superuser exists in database
 */

import mongoose from 'mongoose';
import UserModel from '../models/User';
import { connectDatabase, disconnectDatabase } from '../config/database';

async function checkSuperuser() {
  try {
    console.log('🔍 Connecting to database...');
    await connectDatabase();

    console.log('🔍 Checking for superuser...');
    const superuser = await UserModel.findOne({ isSuperuser: true });

    if (superuser) {
      console.log('✅ Superuser found:', {
        id: superuser._id,
        username: superuser.username,
        displayName: superuser.displayName,
        status: superuser.status
      });
    } else {
      console.log('❌ No superuser found in database');
      console.log('💡 You may need to create a superuser using the createAdmin script');
    }

    // Also check regular users
    const users = await UserModel.find({ isSuperuser: { $ne: true } });
    console.log(`📊 Found ${users.length} regular users`);

    await disconnectDatabase();
  } catch (error) {
    console.error('❌ Error checking superuser:', error);
    process.exit(1);
  }
}

checkSuperuser();
