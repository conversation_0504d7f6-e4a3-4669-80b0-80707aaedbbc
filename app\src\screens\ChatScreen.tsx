/**
 * iOS Chat Screen Component
 * WhatsApp-like instant media preview with persistent upload indicators
 * Features: Swipe-to-reply, upload progress, retry functionality
 * Optimized for iPhone experience
 */

import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
  Animated,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Haptics from 'expo-haptics';
import { theme } from '../utils/theme';
import { chatStyles, chatColors } from '../styles/ChatStyles';
import { AuthService } from '../services/AuthService';
import { ChatService, ChatMessage } from '../services/ChatService';
import { AppConfig } from '../config/app.config';
import WebSocketService from '../services/WebSocketService';
import { ChatListItem } from './ChatListScreen';
import { MediaAttachment } from '../services/MediaService';
import { MediaCacheManager } from '../services/MediaCacheManager';
import { VoiceService } from '../services/VoiceService';
import { AttachmentPicker } from '../components/AttachmentPicker';
import { ImageViewer } from '../components/ImageViewer';
import { SwipeableMessage } from '../components/SwipeableMessage';
import { UploadProgressOverlay } from '../components/UploadProgressOverlay';
import { ErrorBanner } from '../components/ErrorBanner';
import { FadeInMedia } from '../components/FadeInMedia';
import { ImageGridPreview } from '../components/ImageGridPreview';
import { VideoPreview } from '../components/VideoPreview';
import { VideoPlayer } from '../components/VideoPlayer';
import { MessageStatus } from '../components/MessageStatus';

interface ChatScreenProps {
  onLogout: () => void;
  onBack: () => void;
  chatUser?: ChatListItem;
}

export const ChatScreen: React.FC<ChatScreenProps> = ({ onLogout, onBack, chatUser }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAttachment, setSelectedAttachment] = useState<MediaAttachment | null>(null);
  const [selectedAttachments, setSelectedAttachments] = useState<MediaAttachment[]>([]);
  const [showAttachmentPicker, setShowAttachmentPicker] = useState(false);
  const [superuserName, setSuperuserName] = useState('Support');
  const [selectedImage, setSelectedImage] = useState<{ visible: boolean; uri: string }>({
    visible: false,
    uri: '',
  });
  const [selectedVideo, setSelectedVideo] = useState<{ visible: boolean; attachment: MediaAttachment | null }>({
    visible: false,
    attachment: null,
  });
  const [replyToMessage, setReplyToMessage] = useState<ChatMessage | null>(null);
  const [errorBanner, setErrorBanner] = useState<{ message: string; onRetry?: () => void } | null>(null);
  // --- Retry Cooldown State ---
  const [retryCooldowns, setRetryCooldowns] = useState<{ [attachmentId: string]: number }>({});
  
  const flatListRef = useRef<any>(null);
  const chatService = ChatService.getInstance();
  const authService = AuthService.getInstance();
  const webSocketService = new WebSocketService(authService);
  const mediaCacheManager = MediaCacheManager.getInstance();

  // Utility function to parse reply information from message text
  const parseReplyInfo = (text: string) => {
    const replyMatch = text.match(/^@([a-f0-9-]+)\s+(.*)$/);
    if (replyMatch) {
      return {
        replyToId: replyMatch[1],
        actualText: replyMatch[2],
        isReply: true
      };
    }
    return {
      replyToId: null,
      actualText: text,
      isReply: false
    };
  };

  // Find the original message being replied to
  const findReplyToMessage = (replyToId: string): ChatMessage | null => {
    return messages.find(msg => msg.id === replyToId) || null;
  };

  // Initialize chat connection with WebSocket
  useEffect(() => {
    initializeChat();

    // Set up WebSocket for real-time messaging
    webSocketService.onMessage((newMessage) => {
      console.log('📨 Real-time message received:', newMessage);
      setMessages(prev => {
        // Avoid duplicates - check both ID and content/timestamp
        const exists = prev.some(msg =>
          msg.id === newMessage.id ||
          (msg.sender === newMessage.sender &&
           Math.abs(msg.timestamp - newMessage.timestamp) < 5000 && // Within 5 seconds
           msg.text === newMessage.text)
        );

        if (exists) {
          console.log('🔄 Skipping duplicate WebSocket message:', newMessage.id);
          return prev;
        }

        // Only add messages from other users via WebSocket
        // Our own messages are already added via HTTP response
        if (newMessage.sender === 'user') {
          console.log('🔄 Skipping own message from WebSocket:', newMessage.id);
          return prev;
        }

        return [...prev, newMessage].sort((a, b) => a.timestamp - b.timestamp);
      });
    });

    webSocketService.onConnection((connected) => {
      setIsConnected(connected);
      console.log(connected ? '✅ Real-time messaging connected' : '❌ Real-time messaging disconnected');
    });

    webSocketService.onError((error) => {
      console.error('❌ WebSocket error:', error);
      // No longer showing red banner - messages will show appropriate status indicators instead
      setIsConnected(false);
    });

    // Connect to WebSocket
    webSocketService.connect();

    // Fallback status check (less frequent since we have real-time updates)
    const statusInterval = setInterval(() => {
      updateChatStatus();
    }, 30000); // Check every 30 seconds as fallback

    return () => {
      clearInterval(statusInterval);
      webSocketService.disconnect();
    };
  }, []);

  const initializeChat = async () => {
    try {
      setIsLoading(true);

      // PERFORMANCE OPTIMIZATION: Load messages first for instant UI
      console.log('🚀 Fast-loading messages for instant UI...');
      await loadMessagesProgressive();

      // Show UI immediately, then do background operations
      setIsLoading(false);

      // Background operations (non-blocking)
      Promise.all([
        updateChatStatus(),
        // Media sync in background - don't block UI
        mediaCacheManager.syncWithServer().catch(error => {
          console.warn('❌ Background media sync failed:', error);
        }),
        // Preload critical media in background
        mediaCacheManager.preloadCriticalMedia().catch(error => {
          console.warn('❌ Background media preload failed:', error);
        })
      ]).catch(error => {
        console.warn('❌ Background operations failed:', error);
      });

    } catch (error) {
      console.error('Failed to initialize chat:', error);
      setIsLoading(false);
    }
  };

  const loadMessages = async () => {
    try {
      const result = await chatService.getMessages(50, 0);
      if (result.success && result.messages) {
        setMessages(result.messages.sort((a, b) => a.timestamp - b.timestamp));

        // Scroll to bottom after loading messages
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } else {
        console.error('Failed to load messages:', result.error);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  // PERFORMANCE OPTIMIZATION: Progressive message loading for sub-3 second load times
  const loadMessagesProgressive = async () => {
    try {
      console.time('⚡ Progressive message loading');

      // Step 1: Try cached messages first (instant)
      try {
        const cachedMessages = await chatService.loadCachedMessages();
        if (cachedMessages.length > 0) {
          console.log('⚡ Loaded cached messages instantly:', cachedMessages.length);
          setMessages(cachedMessages.sort((a, b) => a.timestamp - b.timestamp));

          // Scroll to bottom immediately
          setTimeout(() => {
            flatListRef.current?.scrollToEnd({ animated: false });
          }, 50);
        }
      } catch (cacheError) {
        console.warn('⚠️ Cache loading failed:', cacheError);
      }

      // Step 2: Load recent messages (fast batch)
      const recentResult = await chatService.getRecentMessages(20); // Optimized fast loading
      if (recentResult.success && recentResult.messages) {
        const recentMessages = recentResult.messages.sort((a, b) => a.timestamp - b.timestamp);
        setMessages(recentMessages);

        console.log('⚡ Loaded recent messages:', recentMessages.length);

        // Scroll to bottom
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }

      console.timeEnd('⚡ Progressive message loading');

      // Step 3: Load remaining messages in background (non-blocking)
      setTimeout(async () => {
        try {
          const fullResult = await chatService.getMessages(50, 0);
          if (fullResult.success && fullResult.messages) {
            const allMessages = fullResult.messages.sort((a, b) => a.timestamp - b.timestamp);
            setMessages(allMessages);
            console.log('⚡ Background: Loaded full message history:', allMessages.length);
          }
        } catch (error) {
          console.warn('⚠️ Background message loading failed:', error);
        }
      }, 100);

    } catch (error) {
      console.error('❌ Progressive message loading failed:', error);
      // Fallback to regular loading
      await loadMessages();
    }
  };
  const updateChatStatus = async () => {
    try {
      const status = await chatService.getChatStatus();
      setIsConnected(status.isConnected);
      // Note: No longer tracking superuser online status as per requirements
    } catch (error) {
      console.error('Error updating chat status:', error);
    }
  };
  // No automatic demo messages as per requirements - chat starts empty
  useEffect(() => {
    setIsConnected(true);
    // Note: No longer tracking superuser online status as per requirements
  }, []);

  // Handle attachment selection
  const handleAttachment = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowAttachmentPicker(true);
  }, []);

  // Handle attachment selection from picker
  const handleAttachmentSelect = useCallback(async (attachment: MediaAttachment | MediaAttachment[]) => {
    try {
      // Handle multiple attachments
      if (Array.isArray(attachment)) {
        console.log(`🔄 Processing ${attachment.length} selected attachments`);
        const cachedAttachments: MediaAttachment[] = [];

        for (const item of attachment) {
          if (item.isImage || item.isVideo) {
            console.log('🔄 Caching media for instant preview:', item.name);
            const cachedAttachment = await mediaCacheManager.cacheMediaForInstantPreview(item);
            cachedAttachments.push(cachedAttachment);
          } else {
            cachedAttachments.push(item);
          }
        }

        setSelectedAttachments(cachedAttachments);
        console.log(`✅ ${cachedAttachments.length} attachments cached successfully`);
      } else {
        // Single attachment (existing logic)
        if (attachment.isImage || attachment.isVideo) {
          console.log('🔄 Caching media for instant preview:', attachment.name);
          const cachedAttachment = await mediaCacheManager.cacheMediaForInstantPreview(attachment);
          setSelectedAttachment(cachedAttachment);
          console.log('✅ Media cached successfully:', cachedAttachment.id);
        } else {
          setSelectedAttachment(attachment);
        }
      }
    } catch (error) {
      console.error('❌ Failed to cache media:', error);
      // Still use attachment but show warning
      if (Array.isArray(attachment)) {
        setSelectedAttachments(attachment);
      } else {
        setSelectedAttachment(attachment);
      }
      // Media preview unavailable but file will still be sent - no banner needed
      console.log('⚠️ Media preview unavailable, but file will still be sent');
    }
    setShowAttachmentPicker(false);
  }, [mediaCacheManager]);

  // Remove selected attachment
  const removeAttachment = useCallback(() => {
    setSelectedAttachment(null);
  }, []);

  const sendMessage = useCallback(async () => {
    if ((!inputText.trim() && !selectedAttachment && selectedAttachments.length === 0) || !isConnected) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const messageId = Date.now().toString();
    const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    // Handle reply formatting - WhatsApp-like
    let messageText = inputText.trim() || '';
    
    // Ensure there's always a text value for media messages to satisfy backend validation
    if (selectedAttachment) {
      if (messageText === '') {
        if (selectedAttachment.isImage) {
          messageText = '📷 Image';
        } else if (selectedAttachment.isVideo) {
          messageText = '🎥 Video';
        } else if (selectedAttachment.isAudio) {
          messageText = '🎵 Audio';
        } else {
          messageText = `📎 ${selectedAttachment.name}`;
        }
      }
    } else if (selectedAttachments.length > 0) {
      if (messageText === '') {
        const imageCount = selectedAttachments.filter(a => a.isImage).length;
        const videoCount = selectedAttachments.filter(a => a.isVideo).length;
        const audioCount = selectedAttachments.filter(a => a.isAudio).length;
        const fileCount = selectedAttachments.length - imageCount - videoCount - audioCount;

        const parts = [];
        if (imageCount > 0) parts.push(`📷 ${imageCount} image${imageCount > 1 ? 's' : ''}`);
        if (videoCount > 0) parts.push(`🎥 ${videoCount} video${videoCount > 1 ? 's' : ''}`);
        if (audioCount > 0) parts.push(`🎵 ${audioCount} audio file${audioCount > 1 ? 's' : ''}`);
        if (fileCount > 0) parts.push(`📎 ${fileCount} file${fileCount > 1 ? 's' : ''}`);

        messageText = parts.join(', ');
      }
    }
    
    if (replyToMessage) {
      messageText = `@${replyToMessage.id} ${messageText}`;
    }
    
    const newMessage: ChatMessage = {
      id: messageId,
      clientId: clientId, // Stable client-side ID for React keys
      chatId: 'default-chat', // Using default chat ID
      text: messageText,
      sender: 'user',
      timestamp: Date.now(),
      status: isConnected ? 'sending' : 'queued', // Show appropriate status based on connection
      isDelivered: false,
      isRead: false,
      attachment: selectedAttachment ? {
        id: selectedAttachment.id,
        name: selectedAttachment.name,
        type: selectedAttachment.type,
        size: selectedAttachment.size,
        uri: selectedAttachment.uri,
        mimeType: selectedAttachment.mimeType,
        isImage: selectedAttachment.isImage,
        isVideo: selectedAttachment.isVideo,
        isAudio: selectedAttachment.isAudio,
      } : undefined,
    };

    // Add message to local state immediately
    setMessages(prev => [...prev, newMessage]);
    setInputText('');
    setSelectedAttachment(null);
    setSelectedAttachments([]);
    setReplyToMessage(null); // Clear reply after sending

    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      // Cache media for instant preview if it's an image/video and not already cached
      let attachmentToSend = selectedAttachment;
      if (selectedAttachment && (selectedAttachment.isImage || selectedAttachment.isVideo) && !selectedAttachment.id) {
        console.log('🔄 Caching media before send:', selectedAttachment.name);
        attachmentToSend = await mediaCacheManager.cacheMediaForInstantPreview(selectedAttachment);
        
        // Update the message with cached attachment info
        newMessage.attachment = {
          ...attachmentToSend,
          id: attachmentToSend.id,
          name: attachmentToSend.name,
          type: attachmentToSend.type,
          size: attachmentToSend.size,
          uri: attachmentToSend.uri,
          mimeType: attachmentToSend.mimeType,
          isImage: attachmentToSend.isImage,
          isVideo: attachmentToSend.isVideo,
          isAudio: attachmentToSend.isAudio,
        };
        
        // Update messages with cached attachment
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, attachment: newMessage.attachment }
            : msg
        ));
      }

      // Handle multiple attachments by sending them as separate messages
      if (selectedAttachments.length > 0) {
        console.log(`📤 Sending ${selectedAttachments.length} attachments as separate messages`);

        // Send text message first if there's text
        if (messageText && messageText !== '📷 Image' && messageText !== '🎥 Video' && messageText !== '🎵 Audio') {
          const textResult = await chatService.sendMessage(messageText);
          if (!textResult.success) {
            console.error('Text message send failed:', textResult.error);
          }
        }

        // Send each attachment as a separate message
        for (let i = 0; i < selectedAttachments.length; i++) {
          const attachment = selectedAttachments[i];
          const attachmentMessageId = `${messageId}_attachment_${i}`;
          const attachmentClientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          let attachmentText = '';
          if (attachment.isImage) {
            attachmentText = '📷 Image';
          } else if (attachment.isVideo) {
            attachmentText = '🎥 Video';
          } else if (attachment.isAudio) {
            attachmentText = '🎵 Audio';
          } else {
            attachmentText = `📎 ${attachment.name}`;
          }

          const attachmentMessage: ChatMessage = {
            id: attachmentMessageId,
            clientId: attachmentClientId,
            chatId: 'default-chat',
            text: attachmentText,
            sender: 'user',
            timestamp: Date.now() + i, // Slight offset for ordering
            status: 'sent',
            isDelivered: false,
            isRead: false,
            attachment: {
              id: attachment.id,
              name: attachment.name,
              type: attachment.type,
              size: attachment.size,
              uri: attachment.uri,
              mimeType: attachment.mimeType,
              isImage: attachment.isImage,
              isVideo: attachment.isVideo,
              isAudio: attachment.isAudio,
            },
          };

          // Add attachment message to local state
          setMessages(prev => [...prev, attachmentMessage]);

          try {
            const attachmentResult = await chatService.sendMessage(attachmentText, attachment);

            if (attachmentResult.success && attachmentResult.message) {
              // Update attachment message status
              setMessages(prev => prev.map(msg =>
                msg.id === attachmentMessageId
                  ? {
                      ...msg,
                      isDelivered: true,
                      status: 'delivered',
                      serverId: attachmentResult.message!.id
                    }
                  : msg
              ));
            } else {
              // Mark attachment message as failed
              setMessages(prev => prev.map(msg =>
                msg.id === attachmentMessageId
                  ? { ...msg, status: 'failed' }
                  : msg
              ));
            }
          } catch (error) {
            console.error(`Failed to send attachment ${i + 1}:`, error);
            setMessages(prev => prev.map(msg =>
              msg.id === attachmentMessageId
                ? { ...msg, status: 'failed' }
                : msg
            ));
          }
        }

        return; // Exit early for multiple attachments
      }

      // Use ChatService to send message with attachment support (single attachment)
      const result = await chatService.sendMessage(newMessage.text, attachmentToSend || undefined);

      if (result.success && result.message) {
        // Update message status and ID, but keep the temporary ID for React keys
        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? {
                ...msg,
                isDelivered: true,
                status: 'delivered',
                // Keep the original messageId for React key consistency
                // The server ID is stored but not used as React key
                serverId: result.message!.id
              }
            : msg
        ));

        // If this message has an attachment, update the media cache with server info
        if ((result.message as any).mediaId && selectedAttachment) {
          try {
            const mediaId = (result.message as any).mediaId;
            const serverMediaUrl = `${AppConfig.backend.baseUrl}/api/chat/unified/media/${mediaId}`;

            // Update the local media entry with server info
            await mediaCacheManager.updateUploadStatus(
              selectedAttachment.id,
              'uploaded',
              serverMediaUrl
            );

            // Create a server media cache entry for WebSocket messages
            await mediaCacheManager.createServerMediaEntry(
              selectedAttachment.id,
              mediaId,
              serverMediaUrl
            );

            console.log('📦 Updated media cache with server info:', {
              localId: selectedAttachment.id,
              serverId: mediaId,
              serverUrl: serverMediaUrl
            });
          } catch (error) {
            console.warn('⚠️ Failed to update media cache with server info:', error);
          }
        }

        // Removed automated superuser response - let superuser respond naturally
      } else {
        // Handle send failure - keep message with failed status instead of removing
        console.error('Message send failed:', result.error);
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, status: 'failed' }
            : msg
        ));
        
        // No error banner - message status will show failed state
        console.log('❌ Message send failed, status updated to failed');
      }
    } catch (error) {
      console.error('Send message error:', error);
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, status: 'failed' }
          : msg
      ));
      
      // No error banner - message status will show failed state
      console.log('❌ Network error during send, status updated to failed');
    }
  }, [inputText, isConnected, selectedAttachment, authService, chatService]);

  // Retry message function
  const retryMessage = useCallback(async (messageId: string, messageText: string, attachment?: MediaAttachment) => {
    try {
      const result = await chatService.sendMessage(messageText, attachment);
      
      if (result.success && result.message) {
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, status: 'sent', isDelivered: true, id: result.message!.id }
            : msg
        ));
      } else {
        throw new Error(result.error || 'Failed to retry message');
      }
    } catch (error) {
      console.error('Retry message error:', error);
      // No error banner - message will remain in failed state
      console.log('❌ Message retry failed, keeping failed status');
    }
  }, [inputText, isConnected, selectedAttachment, authService, chatService]);

  // Removed automated response function
  const handleLogout = useCallback(async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    Alert.alert(
      'Logout',
      'Are you sure you want to logout? This will clear your session.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await authService.logout();
            onLogout();
          },
        },
      ]
    );
  }, [authService, onLogout]);  const initiateVoiceCall = useCallback(async () => {    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    try {
      // Check BLE device connection first
      // TODO: Implement device service check
      const bleConnected = true; // Placeholder - will be implemented with device service
      
      if (!bleConnected) {
        Alert.alert(
          'BLE Required', 
          'Voice calls require BLE authentication. Please ensure your authenticated device is connected.',
          [{ text: 'OK' }]
        );
        return;
      }

      Alert.alert(
        'Secure Voice Call',
        'Start encrypted voice call with voice morphing enabled?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Start Call',
            onPress: async () => {
              try {
                console.log('🎤 Starting voice call with morphing...');
                // Use VoiceService
                const voiceService = VoiceService.getInstance();
                  // Determine user role for morphing profile
                // TODO: Implement getUserRole in AuthService
                const userRole = 'agent'; // Placeholder - will be implemented in AuthService
                
                // Start call with automatic recording and morphing
                const call = await voiceService.startVoiceCall('superuser', userRole);
                
                Alert.alert(
                  'Call Connected', 
                  `Secure voice call active with ${call.morphingProfile} profile. Recording enabled for security.`,
                  [
                    {
                      text: 'End Call',
                      style: 'destructive',
                      onPress: () => voiceService.endVoiceCall()
                    }
                  ]
                );
                
              } catch (error) {
                console.error('❌ Voice call failed:', error);
                Alert.alert('Call Failed', 'Unable to establish secure voice connection.');
              }
            },
          },
        ]
      );    } catch (error) {
      console.error('❌ Voice call initialization failed:', error);
      Alert.alert('Error', 'Unable to initialize voice call system.');
    }
  }, [authService]); // Removed deviceService dependency

  // --- Media and UI Handler Functions ---
  const handleImagePress = useCallback((attachment: MediaAttachment) => {
    if (attachment.isVideo) {
      setSelectedVideo({ visible: true, attachment });
    } else if (attachment.uri) {
      setSelectedImage({ visible: true, uri: attachment.uri });
    }
  }, []);

  const handleAttachmentPress = useCallback((attachment: MediaAttachment) => {
    // Open file or show download option
    Alert.alert('Attachment', `File: ${attachment.name}`, [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Download', onPress: () => console.log('Download:', attachment.name) }
    ]);
  }, []);

  const handleRetryUpload = useCallback(async (messageId: string, attachment: MediaAttachment) => {
    if (!attachment.id || !canRetry(attachment.id)) {
      console.log('⚠️ Retry blocked:', {
        hasId: !!attachment.id,
        canRetry: attachment.id ? canRetry(attachment.id) : false,
        cooldown: attachment.id ? retryCooldowns[attachment.id] : undefined
      });
      return;
    }
    
    console.log('🔄 Retrying upload for:', attachment.id, attachment.name);
    setRetryCooldown(attachment.id);
    
    try {
      // Update upload status to uploading
      await mediaCacheManager.updateUploadStatus(attachment.id, 'uploading');
      
      // Find the original message text
      const message = messages.find(msg => msg.id === messageId);
      if (!message) {
        throw new Error('Original message not found');
      }
      
      // Retry the entire send operation
      const result = await chatService.sendMessage(message.text, attachment);
      
      if (result.success) {
        await mediaCacheManager.updateUploadStatus(attachment.id, 'uploaded');
        console.log('✅ Retry successful for:', attachment.id);
      } else {
        throw new Error(result.error || 'Retry failed');
      }
    } catch (error) {
      console.error('❌ Retry upload failed:', error);
      if (attachment.id) {
        await mediaCacheManager.updateUploadStatus(attachment.id, 'failed');
      }
      
      // No error banner - upload status will show failed state
      console.log('❌ Upload retry failed:', error instanceof Error ? error.message : 'Unknown error');
    }
  }, [retryCooldowns, messages, chatService, mediaCacheManager]);

  const canRetry = useCallback((attachmentId: string) => {
    const cooldown = retryCooldowns[attachmentId];
    if (!cooldown) return true;
    return Date.now() > cooldown;
  }, [retryCooldowns]);

  const setRetryCooldown = useCallback((attachmentId: string, ms: number = 5000) => {
    setRetryCooldowns(prev => ({ ...prev, [attachmentId]: Date.now() + ms }));
  }, []);

  const renderMessage = ({ item }: { item: ChatMessage }) => {
    const isUser = item.sender === 'user';
    const hasAttachment = !!item.attachment;

    // Parse reply information from message text
    const replyInfo = parseReplyInfo(item.text || '');
    const replyToMessage = replyInfo.isReply ? findReplyToMessage(replyInfo.replyToId!) : null;
    // --- Robustly infer isImage/isVideo from mimeType if missing ---
    let attachment = item.attachment;
    if (attachment && attachment.mimeType) {
      if (typeof attachment.isImage === 'undefined') {
        attachment.isImage = attachment.mimeType.startsWith('image/');
      }
      if (typeof attachment.isVideo === 'undefined') {
        attachment.isVideo = attachment.mimeType.startsWith('video/');
      }
    }
    const isImageMessage = hasAttachment && attachment?.isImage && attachment?.mimeType?.startsWith('image/');
    const isVideoMessage = hasAttachment && attachment?.isVideo && attachment?.mimeType?.startsWith('video/');
    const isMediaMessage = isImageMessage || isVideoMessage;
    
    // Get cached URI for media display (WhatsApp-like instant preview)
    const getDisplayUri = (attachment: MediaAttachment) => {
      if (attachment.id && (attachment.isImage || attachment.isVideo)) {
        if (attachment.isVideo && attachment.thumbnailUri) {
          return attachment.thumbnailUri;
        }
        const cachedUri = mediaCacheManager.getDisplayUriSync(attachment.id);
        if (cachedUri && typeof cachedUri === 'string' && cachedUri.trim() !== '') {
          return cachedUri;
        }
      }
      if (attachment.uri && typeof attachment.uri === 'string' && attachment.uri.trim() !== '') {
        return attachment.uri;
      }
      return null; // Never return ''
    };
    
    // Get appropriate media label for status text (without showing random IDs)
    const getMediaLabel = (attachment: MediaAttachment) => {
      if (attachment.isImage) return 'Photo';
      if (attachment.isVideo) return 'Video';
      if (attachment.isAudio) return 'Audio';
      // For documents, use a simplified name without random UUIDs
      const simplifiedName = attachment.name
        .replace(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i, '')  // Remove UUIDs
        .replace(/\d+_\w+/g, '')  // Remove patterns like 12345_a1b2c3
        .replace(/^attachment_\d+_\w+/g, '')  // Remove attachment_123456_a1b2c3 patterns
        .replace(/\s+/g, ' ')  // Replace multiple spaces with a single space
        .trim();
      return simplifiedName || 'File';
    };
    
    return (
      <SwipeableMessage
        onSwipeToReply={() => setReplyToMessage(item)}
        isUser={isUser}
      >
        <View style={[
          chatStyles.messageContainer,
          isUser ? chatStyles.userMessageContainer : chatStyles.receivedMessageContainer
        ]}>
          {/* For media messages (images/videos), render without the bubble padding */}
          {isMediaMessage ? (
            <Animated.View style={[
              chatStyles.imageMessageBubble,
              isUser ? chatStyles.userImageMessage : chatStyles.receivedImageMessage,
              { shadowColor: isUser ? '#007AFF' : '#000', shadowOpacity: 0.15, shadowRadius: 6, elevation: 4, marginBottom: 6, }
            ]}>
              <TouchableOpacity 
                style={chatStyles.imageAttachment}
                onPress={() => handleImagePress(attachment!)}
                activeOpacity={0.85}
                onLongPress={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy)}
                accessibilityLabel={isVideoMessage ? 'View video' : 'View image'}
              >
                {isVideoMessage ? (
                  <View style={chatStyles.videoContainer}>
                    {(() => {
                      const thumbUri = attachment!.thumbnailUri;
                      const displayUri = getDisplayUri(attachment!);
                      
                      // Logic: For videos, prioritize thumbnails and never try to load video files directly in Image components
                      if (thumbUri && thumbUri.trim() !== '') {
                        return (
                          <FadeInMedia
                            uri={thumbUri}
                            style={chatStyles.attachmentImage}
                            resizeMode="cover"
                            onError={(error) => {
                              console.warn('🎥 Video thumbnail load error:', error.nativeEvent.error, 'for:', attachment!.name);
                            }}
                            fallbackIcon="🎥"
                            fallbackText="Video preview"
                            fallbackStyle={chatStyles.mediaErrorPlaceholder}
                          />
                        );
                      } else if (displayUri && !displayUri.endsWith('.mov') && !displayUri.endsWith('.mp4')) {
                        // Only use displayUri if it's not a video file (likely a thumbnail)
                        return (
                          <FadeInMedia
                            uri={displayUri}
                            style={chatStyles.attachmentImage}
                            resizeMode="cover"
                            onError={(error) => {
                              console.warn('🎥 Video preview load error:', error.nativeEvent.error, 'for:', attachment!.name);
                            }}
                            fallbackIcon="🎥"
                            fallbackText="Video preview"
                            fallbackStyle={chatStyles.mediaErrorPlaceholder}
                          />
                        );
                      } else {
                        // Fallback to placeholder for videos with no thumbnails
                        return (
                          <View style={[chatStyles.attachmentImage, chatStyles.mediaErrorPlaceholder]}>
                            <Text style={chatStyles.mediaErrorIcon}>🎥</Text>
                            <Text style={chatStyles.mediaErrorText}>Video</Text>
                            <Text style={[chatStyles.mediaErrorText, {fontSize: 12, marginTop: 2}]}>
                              {Math.round(attachment!.size / (1024 * 1024) * 10) / 10} MB
                            </Text>
                          </View>
                        );
                      }
                    })()}
                    <Animated.View style={[chatStyles.videoPlayButton, { opacity: 0.92 }]}> 
                      <Text style={chatStyles.playIcon}>▶️</Text>
                    </Animated.View>
                  </View>
                ) : (
                  (() => {
                    const displayUri = getDisplayUri(attachment!);
                    if (displayUri) {
                      return (
                        <FadeInMedia
                          uri={displayUri && displayUri.trim() !== '' ? displayUri : null}
                          style={chatStyles.attachmentImage}
                          resizeMode="cover"
                          onError={(error) => {
                            console.warn('📸 Image load error:', error.nativeEvent.error, 'for:', attachment!.name);
                          }}
                          fallbackIcon="🖼️"
                          fallbackText="Image unavailable"
                          fallbackStyle={chatStyles.mediaErrorPlaceholder}
                        />
                      );
                    } else {
                      return (
                        <View style={[chatStyles.attachmentImage, chatStyles.mediaErrorPlaceholder]}>
                          <Text style={chatStyles.mediaErrorIcon}>🖼️</Text>
                          <Text style={chatStyles.mediaErrorText}>Image unavailable</Text>
                        </View>
                      );
                    }
                  })()
                )}
              </TouchableOpacity>
              
              {/* Upload Progress Overlay */}
              {attachment?.id && (
                <UploadProgressOverlay
                  uploadStatus={mediaCacheManager.getCachedMediaInfo(attachment.id)?.uploadStatus || 'pending'}
                  uploadProgress={mediaCacheManager.getCachedMediaInfo(attachment.id)?.uploadProgress || 0}
                  onRetry={() => handleRetryUpload(item.id, attachment)}
                  retryDisabled={!canRetry(attachment.id)}
                />
              )}
              
              {/* Media Message Footer (timestamp inside image) */}
              <View style={chatStyles.imageMessageFooter}>
                <Text style={chatStyles.imageTimestamp}>
                  {isVideoMessage ? '🎥 Video' : '📷 Photo'} • {new Date(item.timestamp).toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </Text>
                {/* WhatsApp-style message status for user messages */}
                {isUser && (
                  <MessageStatus
                    status={item.status as any}
                    isConnected={isConnected}
                    size={12}
                    color="#FFFFFF"
                  />
                )}
              </View>
            </Animated.View>
          ) : (
            /* Regular text messages */
            <Animated.View style={[
              chatStyles.messageBubble,
              isUser ? chatStyles.userMessage : chatStyles.receivedMessage,
              { shadowColor: isUser ? '#007AFF' : '#000', shadowOpacity: 0.08, shadowRadius: 4, elevation: 2, }
            ]}>
              {/* Reply Preview */}
              {replyToMessage && (
                <View style={chatStyles.replyMessagePreview}>
                  <Text style={chatStyles.replyToText}>
                    {replyToMessage.sender === 'user' ? 'You' : 'Support'}
                  </Text>
                  <Text style={chatStyles.replyMessageText} numberOfLines={2}>
                    {replyToMessage.text || '📎 Attachment'}
                  </Text>
                </View>
              )}
              
              {/* Attachment (Non-media files) */}
              {hasAttachment && !isMediaMessage && (
                <TouchableOpacity 
                  style={chatStyles.fileAttachment}
                  onPress={() => handleAttachmentPress(attachment!)}
                  activeOpacity={0.8}
                >
                  <Text style={chatStyles.fileIcon}>
                    {attachment!.mimeType?.startsWith('audio/') ? '🎵' : '📄'}
                  </Text>
                  <View style={chatStyles.fileInfo}>
                    <Text style={chatStyles.fileName} numberOfLines={1}>
                      {attachment!.name}
                    </Text>
                    <Text style={chatStyles.fileSize}>
                      {(attachment!.size / 1024).toFixed(1)}KB
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
              
              {/* Message Text */}
              {replyInfo.actualText && !replyInfo.actualText.startsWith('📎') && replyInfo.actualText.trim() !== '' && (
                <Text style={[
                  chatStyles.messageText,
                  isUser ? chatStyles.userMessageText : chatStyles.receivedMessageText
                ]}>
                  {replyInfo.actualText}
                </Text>
              )}
              
              {/* Message Footer */}
              <View style={chatStyles.messageFooter}>
                <Text style={[
                  chatStyles.timestamp,
                  isUser ? chatStyles.userTimestamp : chatStyles.receivedTimestamp
                ]}>
                  {new Date(item.timestamp).toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </Text>
                {/* WhatsApp-style message status for user messages */}
                {isUser && (
                  <MessageStatus
                    status={item.status as any}
                    isConnected={isConnected}
                    size={14}
                  />
                )}
              </View>
            </Animated.View>
          )}
        </View>
      </SwipeableMessage>
    );
  };

  return (
    <GestureHandlerRootView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      
      {/* Header */}
      <SafeAreaView style={styles.headerSafeArea}>
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
              <Text style={styles.logoutText}>← Exit</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>Administrator</Text>
            {/* Removed status indicator for clean header */}
          </View>
          
          <View style={styles.headerRight}>
            <TouchableOpacity onPress={initiateVoiceCall} style={styles.callButton}>
              <Text style={styles.callButtonText}>📞</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>

      {/* Error Banner removed - using message status indicators instead */}

      <KeyboardAvoidingView 
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        {/* Messages List */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item: any, index: any) => item.clientId || `${item.id}_${index}_${item.timestamp}`}
          style={styles.messagesList}
          contentContainerStyle={[styles.messagesContent, { paddingBottom: 20 }]}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => {
            setTimeout(() => {
              flatListRef.current?.scrollToEnd({ animated: true });
            }, 100);
          }}
        />

        {/* Note: No typing indicators as per requirements */}

        {/* Input Area */}
        <SafeAreaView style={styles.inputSafeArea}>
          {/* Reply Bar - WhatsApp-like reply preview */}
          {replyToMessage && (
            <View style={chatStyles.replyBar}>
              <View style={chatStyles.replyPreview}>
                <View style={chatStyles.replyIndicator} />
                <View style={chatStyles.replyContent}>
                  <Text style={chatStyles.replyAuthor}>
                    {replyToMessage.sender === 'user' ? 'You' : 'Support'}
                  </Text>
                  <Text style={chatStyles.replyText} numberOfLines={1}>
                    {replyToMessage.attachment ? '📎 Attachment' : replyToMessage.text}
                  </Text>
                </View>
              </View>
              <TouchableOpacity
                style={chatStyles.replyCloseButton}
                onPress={() => setReplyToMessage(null)}
                accessibilityLabel="Cancel reply"
              >
                <Text style={chatStyles.replyCloseText}>×</Text>
              </TouchableOpacity>
            </View>
          )}
          
          {/* Attachment Preview */}
          {selectedAttachment && (
            <View style={styles.attachmentPreview}>
              {(selectedAttachment.isImage || selectedAttachment.isVideo) ? (
                <View style={styles.mediaPreviewContainer}>
                  {selectedAttachment.isVideo ? (
                    <VideoPreview
                      attachment={selectedAttachment}
                      style={styles.mediaPreviewImage}
                      showPlayButton={true}
                    />
                  ) : (
                    <Image
                      source={{ uri: selectedAttachment.uri }}
                      style={styles.mediaPreviewImage}
                      resizeMode="cover"
                    />
                  )}
                  <View style={styles.mediaPreviewDetails}>
                    <Text style={styles.mediaPreviewSize}>
                      {(selectedAttachment.size / (1024 * 1024)).toFixed(1)} MB
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={styles.removeAttachmentButton}
                    onPress={removeAttachment}
                  >
                    <Text style={styles.removeAttachmentText}>×</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.attachmentPreviewInfo}>
                  <Text style={styles.attachmentPreviewIcon}>
                    {selectedAttachment.isAudio ? '🎵' : '📄'}
                  </Text>
                  <View style={styles.attachmentPreviewDetails}>
                    <Text style={styles.attachmentPreviewName} numberOfLines={1}>
                      {selectedAttachment.name}
                    </Text>
                    <Text style={styles.attachmentPreviewSize}>
                      {selectedAttachment.size > 1024 * 1024 ? 
                        `${(selectedAttachment.size / (1024 * 1024)).toFixed(1)} MB` : 
                        `${Math.round(selectedAttachment.size / 1024)} KB`}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={styles.removeAttachmentButton}
                    onPress={removeAttachment}
                  >
                    <Text style={styles.removeAttachmentText}>×</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          )}

          {/* Multiple Attachments Preview */}
          {selectedAttachments.length > 0 && (
            <View style={styles.attachmentPreview}>
              <ImageGridPreview
                images={selectedAttachments.filter(a => a.isImage)}
                onRemoveImage={(index) => {
                  const imageAttachments = selectedAttachments.filter(a => a.isImage);
                  const attachmentToRemove = imageAttachments[index];
                  setSelectedAttachments(prev => prev.filter(a => a.id !== attachmentToRemove.id));
                }}
                onImagePress={(image, index) => {
                  setSelectedImage({ visible: true, uri: image.uri });
                }}
              />
              {selectedAttachments.filter(a => !a.isImage).length > 0 && (
                <View style={styles.nonImageAttachments}>
                  <Text style={styles.nonImageAttachmentsText}>
                    + {selectedAttachments.filter(a => !a.isImage).length} other file{selectedAttachments.filter(a => !a.isImage).length > 1 ? 's' : ''}
                  </Text>
                </View>
              )}
              <TouchableOpacity
                style={styles.removeAllAttachmentsButton}
                onPress={() => setSelectedAttachments([])}
              >
                <Text style={styles.removeAttachmentText}>Clear All</Text>
              </TouchableOpacity>
            </View>
          )}

          <View style={styles.inputContainer}>
            <TouchableOpacity
              style={styles.attachmentButton}
              onPress={handleAttachment}
            >
              <Text style={styles.attachmentButtonText}>+</Text>
            </TouchableOpacity>
            
            <View style={styles.textInputContainer}>
              <TextInput
                style={[
                  styles.textInput,
                  { height: Math.max(40, Math.min(100, inputText.split('\n').length * 20 + 20)) }
                ]}
                value={inputText}
                onChangeText={setInputText}
                placeholder="Message"
                placeholderTextColor="#8E8E93"
                multiline
                maxLength={1000}
                editable={isConnected}
                onFocus={() => {
                  setTimeout(() => {
                    flatListRef.current?.scrollToEnd({ animated: true });
                  }, 300);
                }}
              />
            </View>
            <TouchableOpacity
              style={[
                styles.sendButton,
                ((!inputText.trim() && !selectedAttachment && selectedAttachments.length === 0) || !isConnected) && styles.sendButtonDisabled
              ]}
              onPress={sendMessage}
              disabled={(!inputText.trim() && !selectedAttachment && selectedAttachments.length === 0) || !isConnected}
            >
              <Text style={styles.sendButtonText}>→</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </KeyboardAvoidingView>

      {/* Attachment Picker */}
      <AttachmentPicker
        visible={showAttachmentPicker}
        onClose={() => setShowAttachmentPicker(false)}
        onSelect={handleAttachmentSelect}
      />

      {/* Image Viewer Modal */}
      <ImageViewer
        visible={selectedImage.visible}
        imageUri={selectedImage.uri}
        onClose={() => setSelectedImage({ visible: false, uri: '' })}
      />

      {/* Video Player Modal */}
      <VideoPlayer
        visible={selectedVideo.visible}
        videoAttachment={selectedVideo.attachment}
        onClose={() => setSelectedVideo({ visible: false, attachment: null })}
      />
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: chatColors.chatBackground, // Use improved background
  },
  headerSafeArea: {
    backgroundColor: chatColors.headerBackground,
    borderBottomWidth: 0.5,
    borderBottomColor: chatColors.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 2,
  },
  chatContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16, // More generous padding
    backgroundColor: chatColors.headerBackground,
    height: 64, // Slightly taller for better proportions
    justifyContent: 'space-between',
  },
  headerLeft: {
    flex: 1,
  },
  headerCenter: {
    flex: 2,
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#000000',
    fontFamily: '-apple-system',
  },
  headerSubtitle: {
    fontSize: 12,
    color: 'rgba(0, 0, 0, 0.5)',
    fontFamily: '-apple-system',
    marginTop: 1,
  },
  logoutButton: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  logoutText: {
    fontSize: 16,
    color: '#007AFF',
    fontFamily: '-apple-system',
    fontWeight: '500',
  },
  callButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  callButtonText: {
    fontSize: 16,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: theme.spacing.md,
  },
  messageContainer: {
    paddingHorizontal: theme.spacing.md,
    marginVertical: theme.spacing.xs / 2,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  superuserMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  userMessage: {
    backgroundColor: theme.colors.systemBlue,
    borderBottomRightRadius: 6,
  },
  superuserMessage: {
    backgroundColor: theme.colors.systemGray5,
    borderBottomLeftRadius: 6,
  },
  messageText: {
    fontSize: theme.typography.sizes.body,
    lineHeight: 22,
    fontFamily: theme.typography.families.systemText,
  },
  userMessageText: {
    color: theme.colors.background,
  },
  superuserMessageText: {
    color: theme.colors.label,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: theme.spacing.xs,
  },
  timestamp: {
    fontSize: theme.typography.sizes.caption2,
    fontFamily: theme.typography.families.system,
  },
  userTimestamp: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  superuserTimestamp: {
    color: theme.colors.tertiaryLabel,
  },
  typingContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
  },
  typingText: {
    fontSize: theme.typography.sizes.caption,
    color: theme.colors.secondaryLabel,
    fontStyle: 'italic',
    fontFamily: theme.typography.families.system,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 14, // More generous padding
    backgroundColor: chatColors.inputBackground,
    borderTopWidth: 0.5,
    borderTopColor: chatColors.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 4,
  },
  inputSafeArea: {
    backgroundColor: chatColors.inputBackground,
  },
  textInputContainer: {
    flex: 1,
    backgroundColor: '#F7F8FA', // Subtle background differentiation
    borderRadius: 22, // More rounded for modern look
    paddingHorizontal: 18,
    paddingVertical: 12,
    marginHorizontal: 8,
    minHeight: 44, // Larger touch target
    maxHeight: 120,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: chatColors.lightBorder,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.02,
    shadowRadius: 2,
    elevation: 1,
  },
  textInput: {
    fontSize: 16,
    color: '#000000',
    fontFamily: Platform.OS === 'ios' ? '-apple-system' : 'Roboto',
    textAlignVertical: 'center',
    paddingTop: 0,
    paddingBottom: 0,
    lineHeight: 20,
    maxHeight: 100,
  },
  sendButton: {
    width: 44, // Larger touch target
    height: 44,
    borderRadius: 22, // Perfect circle
    backgroundColor: chatColors.accent,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: chatColors.accent,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25, // More pronounced shadow for depth
    shadowRadius: 4,
    elevation: 3,
    transform: [{ scale: 1 }],
  },
  sendButtonDisabled: {
    backgroundColor: 'rgba(0, 122, 255, 0.25)',
    shadowOpacity: 0.1,
    transform: [{ scale: 0.95 }], // Slightly smaller when disabled
  },
  sendButtonText: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  // Attachment Styles
  attachmentContainer: {
    marginBottom: 8,
  },
  imageAttachment: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  attachmentImage: {
    width: 200,
    height: 150,
    borderRadius: 12,
  },
  fileAttachment: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 8,
  },
  attachmentIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  attachmentInfo: {
    flex: 1,
  },
  attachmentName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  attachmentSize: {
    fontSize: 12,
    opacity: 0.7,
  },
  userAttachmentText: {
    color: 'white',
  },
  superuserAttachmentText: {
    color: theme.colors.label,
  },
  // Attachment Preview Styles
  attachmentPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.systemGray6,
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 8,
    overflow: 'hidden',
  },
  mediaPreviewContainer: {
    width: '100%',
    height: 160,
    position: 'relative',
  },
  mediaPreviewImage: {
    width: '100%',
    height: '100%',
  },
  videoPlayOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
  mediaPreviewDetails: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    backgroundColor: 'rgba(0,0,0,0.4)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
  },
  mediaPreviewSize: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  attachmentPreviewInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  attachmentPreviewIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  attachmentPreviewDetails: {
    flex: 1,
  },
  attachmentPreviewName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.label,
    marginBottom: 2,
  },
  attachmentPreviewSize: {
    fontSize: 12,
    color: theme.colors.secondaryLabel,
  },
  removeAttachmentButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.systemRed,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  removeAttachmentText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  attachmentButton: {
    width: 44, // Match send button size
    height: 44,
    borderRadius: 22, // Perfect circle
    backgroundColor: '#F7F8FA', // Subtle background
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    borderWidth: 1,
    borderColor: chatColors.lightBorder,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 1,
  },
  attachmentButtonText: {
    fontSize: 20, // Proper icon size
    color: chatColors.accent,
    fontWeight: '600',
  },
  nonImageAttachments: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  nonImageAttachmentsText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  removeAllAttachmentsButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
});
