/**
 * Jest Test Setup
 * Global test configuration and utilities
 */

import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.MASTER_ENCRYPTION_KEY = 'test-master-encryption-key';
process.env.ADMIN_MASTER_KEY = 'test-admin-master-key';
process.env.TEST_MONGODB_URI = process.env.TEST_MONGODB_URI || 'mongodb://localhost:27017/ccalc_test';

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test utilities
global.testUtils = {
  generateTestUser: (overrides = {}) => ({
    username: `test_user_${Date.now()}`,
    expressionHash: 'test_hash',
    profile: { displayName: 'Test User' },
    status: 'active',
    ...overrides
  }),
  
  generateTestMessage: (senderId: string, recipientId: string, overrides = {}) => ({
    senderId,
    recipientId,
    content: {
      encrypted: 'test_encrypted_content',
      iv: 'test_iv',
      salt: 'test_salt'
    },
    messageType: 'text',
    status: 'sent',
    deviceFingerprint: 'test_fingerprint',
    bleUUID: 'test_ble_uuid',
    ...overrides
  }),
  
  generateTestMedia: (uploaderId: string, overrides = {}) => ({
    mediaId: `test_media_${Date.now()}`,
    uploaderId,
    file: {
      originalName: 'test.jpg',
      encryptedName: 'encrypted_test.jpg',
      mimeType: 'image/jpeg',
      size: 1000,
      category: 'image'
    },
    storage: {
      encryptedPath: '/test/path/encrypted_test.jpg',
      encryptionKey: {
        encryptedKey: 'test_encrypted_key',
        iv: 'test_iv',
        tag: 'test_tag',
        keyId: 'test_key_id'
      },
      fileIv: 'test_file_iv',
      fileTag: 'test_file_tag',
      salt: 'test_salt',
      algorithm: 'aes-256-gcm',
      compressionUsed: false
    },
    isActive: true,
    ...overrides
  })
};

export {};
