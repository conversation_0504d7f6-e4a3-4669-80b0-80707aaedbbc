// import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Devi<PERSON> } from 'react-native-ble-plx';
import { Alert, Platform } from 'react-native';
import { UserService } from './UserService';

// Mock BLE Manager for development - replace with react-native-ble-plx in production
interface MockDevice {
  id: string;
  name: string;
  rssi: number;
}

class MockBleManager {
  async state(): Promise<string> {
    return 'PoweredOn';
  }

  startDeviceScan(serviceUUIDs: any, options: any, callback: (error: any, device: MockDevice | null) => void): void {
    // Simulate finding some earbud devices
    setTimeout(() => {
      callback(null, { id: 'airpods-1', name: 'AirPods Pro', rssi: -45 });
    }, 1000);
    setTimeout(() => {
      callback(null, { id: 'galaxy-buds-1', name: 'Galaxy Buds Pro', rssi: -60 });
    }, 2000);
  }

  stopDeviceScan(): void {
    console.log('Mock: Stopped device scan');
  }

  async connectToDevice(deviceId: string): Promise<any> {
    return {
      id: deviceId,
      discoverAllServicesAndCharacteristics: async () => {}
    };
  }

  async isDeviceConnected(deviceId: string): Promise<boolean> {
    return true; // Mock always connected
  }

  async cancelDeviceConnection(deviceId: string): Promise<void> {
    console.log(`Mock: Disconnected from ${deviceId}`);
  }
}

interface BLEDevice {
  deviceId: string;
  name: string;
  isEarbud: boolean;
  isActive: boolean;
  isVerified: boolean;
  rssi?: number;
  lastSeen: Date;
}

class BLEService {
  private bleManager: MockBleManager;
  private connectedDevices: Map<string, BLEDevice> = new Map();
  private activeEarbud: BLEDevice | null = null;
  private isScanning: boolean = false;
  private scanTimeout: any = null;
  private connectionMonitor: any = null;

  // Known earbud device name patterns
  private earbud_patterns = [
    'AirPods',
    'Galaxy Buds',
    'WF-1000XM4',
    'FreeBuds',
    'Jabra',
    'Bose',
    'BeatsX',
    'PowerBeats',
    'Liberty',
    'Elite',
    'JBL',
    'Sennheiser'
  ];

  constructor() {
    this.bleManager = new MockBleManager();
    this.initializeBLE();
  }

  private async initializeBLE(): Promise<void> {
    try {
      const state = await this.bleManager.state();
      if (state !== 'PoweredOn') {
        Alert.alert('Bluetooth Required', 'Please enable Bluetooth to use voice calls.');
        return;
      }
      
      // Start monitoring for device changes
      this.startConnectionMonitoring();
    } catch (error) {
      console.error('BLE initialization error:', error);
    }
  }

  private isEarbudDevice(deviceName: string): boolean {
    return this.earbud_patterns.some(pattern => 
      deviceName.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  public async scanForDevices(duration: number = 10000): Promise<BLEDevice[]> {
    if (this.isScanning) {
      console.log('Already scanning...');
      return Array.from(this.connectedDevices.values());
    }

    this.isScanning = true;
    const discoveredDevices: BLEDevice[] = [];

    try {
      // Clear previous scan timeout
      if (this.scanTimeout) {
        clearTimeout(this.scanTimeout);
      }

      console.log('Starting BLE scan...');
      
      this.bleManager.startDeviceScan(null, null, (error: any, device: any) => {
        if (error) {
          console.error('Scan error:', error);
          return;
        }

        if (device && device.name) {
          const bleDevice: BLEDevice = {
            deviceId: device.id,
            name: device.name,
            isEarbud: this.isEarbudDevice(device.name),
            isActive: device.rssi ? device.rssi > -70 : false,
            isVerified: false,
            rssi: device.rssi || undefined,
            lastSeen: new Date()
          };

          // Update or add device
          this.connectedDevices.set(device.id, bleDevice);
          
          if (bleDevice.isEarbud && bleDevice.isActive) {
            discoveredDevices.push(bleDevice);
            console.log(`Found earbud: ${bleDevice.name} (RSSI: ${bleDevice.rssi})`);
          }
        }
      });

      // Stop scanning after duration
      this.scanTimeout = setTimeout(() => {
        this.stopScan();
      }, duration);

      return discoveredDevices;
    } catch (error) {
      console.error('Scan error:', error);
      this.isScanning = false;
      return [];
    }
  }

  public stopScan(): void {
    if (this.isScanning) {
      this.bleManager.stopDeviceScan();
      this.isScanning = false;
      console.log('BLE scan stopped');
    }
    
    if (this.scanTimeout) {
      clearTimeout(this.scanTimeout);
      this.scanTimeout = null;
    }
  }

  public async verifyEarbud(deviceId: string): Promise<boolean> {
    try {
      const device = this.connectedDevices.get(deviceId);
      if (!device || !device.isEarbud) {
        console.log('Device not found or not an earbud');
        return false;
      }

      // Connect to device for verification
      const connectedDevice = await this.bleManager.connectToDevice(deviceId);
      await connectedDevice.discoverAllServicesAndCharacteristics();

      // Mark as verified and active
      device.isVerified = true;
      device.isActive = true;
      this.activeEarbud = device;
      this.connectedDevices.set(deviceId, device);

      console.log(`Earbud verified: ${device.name}`);

      // Register with backend
      await this.registerEarbudWithBackend(device);

      return true;
    } catch (error) {
      console.error('Earbud verification failed:', error);
      return false;
    }
  }

  private async registerEarbudWithBackend(device: BLEDevice): Promise<void> {
    try {
      const userService = UserService.getInstance();
      const userResult = await userService.getCurrentUser();
      if (!userResult.success || !userResult.user) {
        throw new Error('User not authenticated');
      }

      const registrationData = {
        user_id: userResult.user.id,
        device_id: device.deviceId,
        device_name: device.name,
        device_type: 'earbud',
        connection_status: 'active',
        last_seen: device.lastSeen.toISOString(),
        rssi: device.rssi
      };

      const response = await fetch('/api/ble/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(registrationData)
      });

      if (!response.ok) {
        throw new Error('Failed to register earbud with backend');
      }

      console.log('Earbud registered with backend successfully');
    } catch (error) {
      console.error('Backend registration failed:', error);
    }
  }

  public getActiveEarbud(): BLEDevice | null {
    return this.activeEarbud;
  }

  public getConnectedEarbuds(): BLEDevice[] {
    return Array.from(this.connectedDevices.values()).filter(
      device => device.isEarbud && device.isActive
    );
  }

  public async enforceAudioRoute(): Promise<boolean> {
    if (!this.activeEarbud) {
      Alert.alert('No Earbud', 'No verified earbud connected. Voice calls require earbud verification.');
      return false;
    }

    try {
      // For iOS, we would use AVAudioSession to route audio
      // This is a simplified implementation
      console.log(`Enforcing audio route to: ${this.activeEarbud.name}`);
      
      // In a real implementation, you would:
      // 1. Set AVAudioSession category to playAndRecord
      // 2. Set preferred input/output to the BLE device
      // 3. Monitor for route changes
      
      return true;
    } catch (error) {
      console.error('Audio route enforcement failed:', error);
      return false;
    }
  }

  private startConnectionMonitoring(): void {
    if (this.connectionMonitor) {
      clearInterval(this.connectionMonitor);
    }

    this.connectionMonitor = setInterval(() => {
      this.checkActiveConnections();
    }, 5000); // Check every 5 seconds
  }

  private async checkActiveConnections(): Promise<void> {
    if (!this.activeEarbud) return;

    try {
      // Check if active earbud is still connected
      const isConnected = await this.bleManager.isDeviceConnected(this.activeEarbud.deviceId);
      
      if (!isConnected) {
        console.log(`Active earbud ${this.activeEarbud.name} disconnected`);
        this.activeEarbud.isActive = false;
        this.activeEarbud.isVerified = false;
        this.activeEarbud = null;

        // Notify any active calls that BLE connection is lost
        Alert.alert(
          'Earbud Disconnected',
          'Your earbud has been disconnected. Voice calls will be ended for security.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Connection monitoring error:', error);
    }
  }

  public async disconnectEarbud(deviceId: string): Promise<void> {
    try {
      await this.bleManager.cancelDeviceConnection(deviceId);
      
      const device = this.connectedDevices.get(deviceId);
      if (device) {
        device.isActive = false;
        device.isVerified = false;
        
        if (this.activeEarbud?.deviceId === deviceId) {
          this.activeEarbud = null;
        }
      }
      
      console.log(`Disconnected from device: ${deviceId}`);
    } catch (error) {
      console.error('Disconnect error:', error);
    }
  }

  public cleanup(): void {
    this.stopScan();
    
    if (this.connectionMonitor) {
      clearInterval(this.connectionMonitor);
      this.connectionMonitor = null;
    }

    this.connectedDevices.clear();
    this.activeEarbud = null;
  }
}

export default new BLEService();
export type { BLEDevice };
