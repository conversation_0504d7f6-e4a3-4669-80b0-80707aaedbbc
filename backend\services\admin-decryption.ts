/**
 * Admin Decryption Service
 * Allows admin panel to decrypt messages and media for compliance
 * Uses secure key storage while maintaining admin access
 */

import { Message } from '../models/Chat';
import Media from '../models/Media';
import SecureKeyStorage from '../utils/secure-key-storage';
import { decryptMessage, decryptFile } from '../utils/encryption';
import e2eEncryptionService from './e2e-encryption';
import fs from 'fs/promises';

interface DecryptedMessage {
  id: string;
  senderId: string;
  recipientId: string;
  content: string;
  messageType: string;
  timestamp: Date;
  hasAttachment: boolean;
  attachment?: {
    originalName: string;
    mimeType: string;
    size: number;
    decryptedContent?: Buffer;
  };
}

interface DecryptedChat {
  chatId: string;
  participants: string[];
  messages: DecryptedMessage[];
  totalMessages: number;
}

export class AdminDecryptionService {
  private adminMasterKey: string;

  constructor(adminMasterKey?: string) {
    this.adminMasterKey = adminMasterKey || process.env.ADMIN_MASTER_KEY || 'fallback-admin-key';
    
    if (this.adminMasterKey === 'fallback-admin-key') {
      console.warn('⚠️ WARNING: Using fallback admin key. Set ADMIN_MASTER_KEY in production!');
    }
  }

  /**
   * Decrypt a single message for admin panel
   */
  async decryptMessage(messageId: string): Promise<DecryptedMessage | null> {
    try {
      const message = await Message.findById(messageId);
      if (!message) {
        throw new Error('Message not found');
      }

      let decryptedContent = 'Unable to decrypt';

      // Try E2E decryption first (using admin backdoor)
      if (message.e2eEncryption?.adminBackdoor) {
        try {
          decryptedContent = await e2eEncryptionService.decryptAdminBackdoor(
            message.e2eEncryption.adminBackdoor
          );
          console.log('✅ Decrypted using E2E admin backdoor');
        } catch (e2eError) {
          console.error('Failed to decrypt E2E message with admin backdoor:', e2eError);

          // Fallback to legacy admin access key
          if (message.content.adminAccessKey) {
            try {
              const decryptionKey = SecureKeyStorage.decryptKeyWithAdminAccess(
                message.content.adminAccessKey,
                this.adminMasterKey
              );

              const decryptedResult = await decryptMessage(
                {
                  encrypted: message.content.encrypted,
                  iv: message.content.iv,
                  tag: message.content.adminAccessKey?.tag || message.content.encryptionKey?.tag || ''
                },
                decryptionKey.toString('hex')
              );

              decryptedContent = decryptedResult;
              console.log('✅ Decrypted using legacy admin key');
            } catch (legacyError) {
              console.error('Failed to decrypt message with legacy admin key:', legacyError);
            }
          }
        }
      } else if (message.content.adminAccessKey) {
        // Legacy decryption for older messages
        try {
          const decryptionKey = SecureKeyStorage.decryptKeyWithAdminAccess(
            message.content.adminAccessKey,
            this.adminMasterKey
          );

          const decryptedResult = await decryptMessage(
            {
              encrypted: message.content.encrypted,
              iv: message.content.iv,
              tag: message.content.adminAccessKey?.tag || message.content.encryptionKey?.tag || ''
            },
            decryptionKey.toString('hex')
          );

          decryptedContent = decryptedResult;
          console.log('✅ Decrypted using legacy admin key');
        } catch (decryptError) {
          console.error('Failed to decrypt message with admin key:', decryptError);
        }
      }

      // Handle media attachment if present
      let attachment: DecryptedMessage['attachment'];
      if (message.mediaAttachment) {
        try {
          const mediaRecord = await Media.findOne({ messageId: message._id });
          if (mediaRecord) {
            attachment = {
              originalName: mediaRecord.file.originalName,
              mimeType: mediaRecord.file.mimeType,
              size: mediaRecord.file.size,
            };

            // Decrypt media content if admin access is available
            if (mediaRecord.storage.adminAccessKey) {
              try {
                const mediaDecryptionKey = SecureKeyStorage.decryptKeyWithAdminAccess(
                  mediaRecord.storage.adminAccessKey,
                  this.adminMasterKey
                );

                const encryptedFileBuffer = await fs.readFile(mediaRecord.storage.encryptedPath);
                const decryptedFileBuffer = await decryptFile(
                  {
                    encrypted: encryptedFileBuffer,
                    iv: mediaRecord.storage.fileIv,
                    tag: mediaRecord.storage.fileTag || ''
                  },
                  mediaDecryptionKey.toString('hex')
                );

                attachment.decryptedContent = decryptedFileBuffer;
              } catch (mediaDecryptError) {
                console.error('Failed to decrypt media file:', mediaDecryptError);
              }
            }
          }
        } catch (mediaError) {
          console.error('Error loading media attachment:', mediaError);
        }
      }

      return {
        id: (message._id as any).toString(),
        senderId: message.senderId.toString(),
        recipientId: message.recipientId.toString(),
        content: decryptedContent,
        messageType: message.messageType,
        timestamp: message.createdAt,
        hasAttachment: !!message.mediaAttachment,
        attachment,
      };

    } catch (error) {
      console.error('Error decrypting message:', error);
      return null;
    }
  }

  /**
   * Decrypt all messages in a chat for admin panel
   */
  async decryptChat(chatId: string, limit: number = 100, offset: number = 0): Promise<DecryptedChat | null> {
    try {
      const messages = await Message.find({ 
        $or: [
          { senderId: chatId },
          { recipientId: chatId }
        ]
      })
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit);

      const decryptedMessages: DecryptedMessage[] = [];
      
      for (const message of messages) {
        const decrypted = await this.decryptMessage((message._id as any).toString());
        if (decrypted) {
          decryptedMessages.push(decrypted);
        }
      }

      // Get unique participants
      const participantIds = new Set<string>();
      messages.forEach(msg => {
        participantIds.add(msg.senderId.toString());
        participantIds.add(msg.recipientId.toString());
      });

      const totalCount = await Message.countDocuments({
        $or: [
          { senderId: chatId },
          { recipientId: chatId }
        ]
      });

      return {
        chatId,
        participants: Array.from(participantIds),
        messages: decryptedMessages,
        totalMessages: totalCount,
      };

    } catch (error) {
      console.error('Error decrypting chat:', error);
      return null;
    }
  }

  /**
   * Decrypt all chats for a specific user
   */
  async decryptUserChats(userId: string): Promise<DecryptedChat[]> {
    try {
      const messages = await Message.find({
        $or: [
          { senderId: userId },
          { recipientId: userId }
        ]
      }).sort({ createdAt: -1 });

      // Group messages by chat (based on participants)
      const chatGroups = new Map<string, any[]>();
      
      messages.forEach(message => {
        const participants = [message.senderId.toString(), message.recipientId.toString()].sort();
        const chatKey = participants.join('-');
        
        if (!chatGroups.has(chatKey)) {
          chatGroups.set(chatKey, []);
        }
        chatGroups.get(chatKey)!.push(message);
      });

      const decryptedChats: DecryptedChat[] = [];

      for (const [chatKey, chatMessages] of chatGroups) {
        const decryptedMessages: DecryptedMessage[] = [];
        
        for (const message of chatMessages) {
          const decrypted = await this.decryptMessage((message._id as any).toString());
          if (decrypted) {
            decryptedMessages.push(decrypted);
          }
        }

        const participants = chatKey.split('-');
        
        decryptedChats.push({
          chatId: chatKey,
          participants,
          messages: decryptedMessages,
          totalMessages: chatMessages.length,
        });
      }

      return decryptedChats;

    } catch (error) {
      console.error('Error decrypting user chats:', error);
      return [];
    }
  }

  /**
   * Get decrypted media file for admin panel
   */
  async getDecryptedMedia(mediaId: string): Promise<{
    content: Buffer;
    mimeType: string;
    originalName: string;
  } | null> {
    try {
      const mediaRecord = await Media.findOne({ mediaId });
      if (!mediaRecord) {
        throw new Error('Media not found');
      }

      if (!mediaRecord.storage.adminAccessKey) {
        throw new Error('Admin access not available for this media');
      }

      const decryptionKey = SecureKeyStorage.decryptKeyWithAdminAccess(
        mediaRecord.storage.adminAccessKey,
        this.adminMasterKey
      );

      const encryptedFileBuffer = await fs.readFile(mediaRecord.storage.encryptedPath);
      const decryptedResult = await decryptFile(
        {
          encrypted: encryptedFileBuffer,
          iv: mediaRecord.storage.fileIv,
          tag: mediaRecord.storage.fileTag || ''
        },
        decryptionKey.toString('hex')
      );

      return {
        content: decryptedResult,
        mimeType: mediaRecord.file.mimeType,
        originalName: mediaRecord.file.originalName,
      };

    } catch (error) {
      console.error('Error decrypting media:', error);
      return null;
    }
  }

  /**
   * Validate admin access
   */
  validateAdminAccess(): boolean {
    return this.adminMasterKey !== 'fallback-admin-key';
  }

  /**
   * Get encryption statistics for admin dashboard
   */
  async getEncryptionStats(): Promise<{
    totalMessages: number;
    encryptedMessages: number;
    messagesWithAdminAccess: number;
    totalMedia: number;
    encryptedMedia: number;
    mediaWithAdminAccess: number;
  }> {
    try {
      const totalMessages = await Message.countDocuments();
      const encryptedMessages = await Message.countDocuments({
        'content.encrypted': { $exists: true }
      });
      const messagesWithAdminAccess = await Message.countDocuments({
        'content.adminAccessKey': { $exists: true }
      });

      const totalMedia = await Media.countDocuments();
      const encryptedMedia = await Media.countDocuments({
        'storage.encryptionKey': { $exists: true }
      });
      const mediaWithAdminAccess = await Media.countDocuments({
        'storage.adminAccessKey': { $exists: true }
      });

      return {
        totalMessages,
        encryptedMessages,
        messagesWithAdminAccess,
        totalMedia,
        encryptedMedia,
        mediaWithAdminAccess,
      };

    } catch (error) {
      console.error('Error getting encryption stats:', error);
      return {
        totalMessages: 0,
        encryptedMessages: 0,
        messagesWithAdminAccess: 0,
        totalMedia: 0,
        encryptedMedia: 0,
        mediaWithAdminAccess: 0,
      };
    }
  }
}

export default AdminDecryptionService;
