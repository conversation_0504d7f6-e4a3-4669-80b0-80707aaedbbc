import { Router, Request, Response } from 'express';
import { authenticateToken, requireAdmin } from '../../middleware/auth';
import AuditLogModel from '../../models/AuditLog';
import UserModel from '../../models/User';
import VoiceCallModel, { IVoiceCallModel } from '../../models/VoiceCall';
import VoiceProfileModel from '../../models/VoiceProfile';
import voiceModulationService, { VOICE_PROFILES, CUSTOM_PROFILE_EXAMPLES, createCustomVoiceProfile } from '../../services/voiceModulation';
import { HybridVoiceProcessor, HYBRID_VOICE_PROFILES } from '../../services/hybridVoiceProcessor';
import { UnifiedVoiceProcessor, UNIFIED_VOICE_PROFILES } from '../../services/unifiedVoiceProcessor';
import realtimeRouter from './realtime';
import recordingsRouter from './recordings';
import { RealTimeVoiceProcessor, REALTIME_VOICE_PROFILES } from '../../services/realTimeVoiceProcessor';
import { RealTimeAudioStreaming } from '../../services/realTimeAudioStreaming';
import { VoiceProcessingTestSuite } from '../../tests/voiceProcessingTests';
import enhancedModulationRouter from '../../routes/voice/enhancedModulation.routes';
import bleVerificationService from '../../services/bleVerificationService';
import multer = require('multer');
import mongoose from 'mongoose';
import * as path from 'path';
import * as crypto from 'crypto';
import * as fs from 'fs';

const router = Router();

// Helper function to create proper audit logs
const createAuditLog = async (
  action: string,
  req: Request,
  details: any = {},
  eventType: 'api_access' | 'system_action' | 'admin_action' = 'api_access',
  result: 'success' | 'failure' | 'blocked' | 'warning' = 'success'
) => {
  try {
    await AuditLogModel.create({
      logId: crypto.randomUUID(),
      userId: (req as any).user?.id,
      adminId: (req as any).admin?.id,
      event: {
        type: eventType,
        action,
        resource: req.originalUrl,
        result,
        severity: 'low'
      },
      context: {
        ipAddress: req.ip || '127.0.0.1',
        userAgent: req.get('User-Agent') || 'Unknown',
        endpoint: req.originalUrl,
        method: req.method
      },
      security: {
        riskScore: 0,
        suspiciousActivity: false
      },
      data: {
        metadata: details
      },
      compliance: {
        category: 'system_admin',
        retention: 'medium',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      }
    });
  } catch (error) {
    console.error('Failed to create audit log:', error);
    // Don't throw error to avoid breaking the main functionality
  }
};

// Initialize voice processors
const hybridProcessor = new HybridVoiceProcessor();
const unifiedProcessor = new UnifiedVoiceProcessor();
const realTimeProcessor = new RealTimeVoiceProcessor();
const streamingProcessor = new RealTimeAudioStreaming();

// Import the WORLD vocoder service
import { worldVocoderService } from '../../services/worldVocoderService';

/**
 * Voice system status
 * GET /api/voice/status
 */
router.get('/status', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    res.json({
      success: true,
      status: {
        nativeVocoder: {
          available: worldVocoderService.isNativeAvailable(),
          fallbackMode: !worldVocoderService.isNativeAvailable(),
          version: worldVocoderService.isNativeAvailable() ? 'native' : 'fallback'
        },
        systemTime: new Date().toISOString(),
        uptime: process.uptime()
      }
    });
  } catch (error) {
    console.error('Voice status check failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get voice system status'
    });
  }
});

/**
 * Start Voice Call (UPDATED: With BLE verification)
 * POST /api/voice/start-call
 */
router.post('/start-call', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      callType = 'mobile_to_superuser',
      timestamp,
      bleDeviceId,
      bleChallenge
    } = req.body;
    const callerId = (req as any).user?.id;
    const username = (req as any).user?.username;

    if (!callerId) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    console.log('📞 Starting voice call with BLE verification:', { callerId, username, callType, bleDeviceId });

    // Import BLE verification service
    const bleVerificationService = (await import('../../services/bleVerificationService')).default;

    // Verify BLE device authorization for voice calls
    const bleVerification = await bleVerificationService.verifyVoiceCallAuthorization(
      callerId,
      bleChallenge,
      bleDeviceId
    );

    if (!bleVerification.verified) {
      // Record failed attempt
      await bleVerificationService.recordVoiceCallAttempt(
        callerId,
        bleDeviceId,
        false,
        bleVerification.error
      );

      res.status(403).json({
        success: false,
        error: bleVerification.error,
        requiresBLE: bleVerification.requiresBLE,
        userRole: bleVerification.userRole
      });
      return;
    }

    console.log('✅ BLE verification passed:', {
      userRole: bleVerification.userRole,
      deviceName: bleVerification.deviceName
    });

    // Find superuser as recipient
    const superuser = await UserModel.findOne({ isSuperuser: true, status: 'active' });
    if (!superuser) {
      res.status(404).json({
        success: false,
        error: 'No active superuser found'
      });
      return;
    }

    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const recipientId = (superuser._id as any).toString();

    // Create voice call record with automatic recording enabled
    const voiceCall = await VoiceCallModel.create({
      callId,
      callerId,
      recipientId,
      type: callType,
      status: 'initiated',
      startTime: new Date(timestamp || Date.now()),
      morphingProfile: 'user_secure', // Default profile for users
      callerIP: req.ip || 'unknown',
      deviceFingerprint: req.headers['x-device-fingerprint'] as string || 'unknown',
      bleDeviceId: req.headers['x-ble-device-id'] as string,
      metadata: {
        callerUserAgent: req.get('User-Agent') || 'mobile-app',
        platform: 'mobile',
        deviceType: 'unknown',
        networkType: 'unknown',
        encryptionLevel: 'AES-256',
        compressionRatio: 0.7,
        audioCodec: 'AAC',
        // Recording is enabled by default for all calls (compliance requirement)
        recordingEnabled: true,
        recordingNotified: false // Users are NOT notified about recording
      }
    });

    // Log for audit
    await AuditLogModel.create({
      action: 'VOICE_CALL_STARTED',
      userId: callerId,
      details: {
        callId,
        recipientId,
        callType,
        morphingProfile: voiceCall.morphingProfile
      },
      severity: 'medium',
      category: 'voice_communication',
      ipAddress: '0.0.0.0', // Will be set by middleware if available
      userAgent: 'mobile-app'
    });

    console.log('✅ Voice call started:', callId);

    res.json({
      success: true,
      callId,
      status: 'initiated',
      morphingProfile: voiceCall.morphingProfile,
      recordingEnabled: true // Always enabled for compliance
    });

  } catch (error: any) {
    console.error('Start call error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start call'
    });
  }
});

/**
 * Connect Voice Call and Start Recording
 * POST /api/voice/connect-call
 */
router.post('/connect-call', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.body;
    const userId = (req as any).user?.id;

    if (!callId) {
      res.status(400).json({
        success: false,
        error: 'Call ID is required'
      });
      return;
    }

    // Find the voice call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      res.status(404).json({
        success: false,
        error: 'Voice call not found'
      });
      return;
    }

    // Update call status to connected
    voiceCall.status = 'connected';

    // SECURITY: Automatically start recording (hidden from users)
    if (!voiceCall.recordingPath) {
      const recordingDir = path.join(__dirname, '../../uploads/recordings/encrypted');
      if (!fs.existsSync(recordingDir)) {
        fs.mkdirSync(recordingDir, { recursive: true });
      }

      const recordingFileName = `call_${callId}_${Date.now()}.enc`;
      const recordingPath = path.join(recordingDir, recordingFileName);

      voiceCall.recordingPath = recordingPath;
      voiceCall.metadata = {
        ...voiceCall.metadata,
        recordingStarted: new Date(),
        recordingEnabled: true,
        recordingNotified: false // Users are NOT notified
      };
    }

    await voiceCall.save();

    // Log for audit (recording start is logged but not exposed to users)
    await AuditLogModel.create({
      action: 'VOICE_CALL_CONNECTED_RECORDING_STARTED',
      userId,
      details: {
        callId,
        recordingPath: voiceCall.recordingPath,
        morphingProfile: voiceCall.morphingProfile,
        recordingHidden: true // Flag that recording is hidden from user
      },
      severity: 'medium',
      category: 'voice_communication',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    console.log('✅ Voice call connected with hidden recording:', callId);

    res.json({
      success: true,
      callId,
      status: 'connected',
      morphingProfile: voiceCall.morphingProfile
      // Note: recordingEnabled is NOT returned to hide from users
    });

  } catch (error: any) {
    console.error('Connect call error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to connect call'
    });
  }
});

/**
 * End Voice Call
 * POST /api/voice/end-call
 */
router.post('/end-call', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId, duration, endTime } = req.body;
    const callerId = (req as any).user?.id;

    if (!callId) {
      res.status(400).json({
        success: false,
        error: 'Call ID is required'
      });
      return;
    }

    console.log('📞 Ending voice call:', { callId, callerId, duration });

    // Find and update voice call
    const voiceCall = await VoiceCallModel.findOneAndUpdate(
      { callId, callerId },
      {
        status: 'completed',
        endTime: new Date(endTime || Date.now()),
        duration: duration || 0
      },
      { new: true }
    );

    if (!voiceCall) {
      res.status(404).json({
        success: false,
        error: 'Voice call not found'
      });
      return;
    }

    // Log for audit
    await AuditLogModel.create({
      action: 'VOICE_CALL_ENDED',
      userId: callerId,
      details: {
        callId,
        duration: voiceCall.duration || 0,
        hasRecording: !!voiceCall.recordingPath
      },
      severity: 'low',
      category: 'voice_communication',
      ipAddress: '0.0.0.0',
      userAgent: 'mobile-app'
    });

    console.log('✅ Voice call ended:', callId);

    res.json({
      success: true,
      callId,
      status: 'completed',
      duration: voiceCall.duration
    });

  } catch (error: any) {
    console.error('End call error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to end call'
    });
  }
});

// Configure multer for voice recording uploads with encryption
const storage = multer.memoryStorage(); // Store in memory for encryption

const upload = multer({
  storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB limit
  fileFilter: (req: any, file: any, cb: any) => {
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed') as any, false);
    }
  }
});

/**
 * Upload voice call recording with encryption
 * POST /api/voice/upload-recording
 */
router.post('/upload-recording', upload.single('recording'), async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId, duration, morphingProfile } = req.body;
    const file = req.file;

    console.log('🎤 Voice recording upload:', { callId, duration, fileSize: file?.size });

    if (!file || !callId) {
      res.status(400).json({
        success: false,
        error: 'Recording file and call ID are required'
      });
      return;
    }

    // Find the voice call record
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      res.status(404).json({
        success: false,
        error: 'Voice call not found'
      });
      return;
    }

    // SECURITY: Encrypt the recording before storing
    const SecureKeyStorage = require('../../utils/secure-key-storage').default;
    const { encryptFile } = require('../../utils/encryption');

    // Create secure encryption keys
    const keyData = SecureKeyStorage.createFileEncryptionKey();
    const encryptedBuffer = await encryptFile(file.buffer, keyData.key.toString('hex'));

    // Create secure storage path
    const uploadDir = path.join(__dirname, '../../uploads/recordings/encrypted');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    const encryptedName = `${crypto.randomUUID()}_${Date.now()}.enc`;
    const encryptedPath = path.join(uploadDir, encryptedName);

    // Save encrypted file
    await fs.promises.writeFile(encryptedPath, encryptedBuffer.encrypted);

    // Update voice call with encrypted recording info
    voiceCall.recordingPath = encryptedPath;
    voiceCall.recordingSize = file.size;
    voiceCall.duration = parseInt(duration) || 0;
    voiceCall.metadata = {
      ...voiceCall.metadata,
      encryptionKey: keyData.encryptedStorage,
      adminAccessKey: keyData.adminAccess,
      fileIv: encryptedBuffer.iv,
      fileTag: encryptedBuffer.tag,
      originalMimeType: file.mimetype
    };
    await voiceCall.save();

    // Update user's voiceRecordings
    if (voiceCall.callerId) {
      const user = await UserModel.findById(voiceCall.callerId);
      if (user) {
        if (!user.voiceRecordings) user.voiceRecordings = [];
        user.voiceRecordings.push({
          recordingId: (voiceCall._id as any).toString(),
          sessionId: callId,
          timestamp: new Date(),
          duration: voiceCall.duration || 0,
          fileSize: file.size,
          encryptionKey: '', // Not available here
          isProcessed: true,
          voiceProfile: morphingProfile || 'ANONYMOUS'
        });
        await user.save();
      }
    }

    // Log for admin panel
    await AuditLogModel.create({
      action: 'VOICE_RECORDING_UPLOADED',
      userId: voiceCall.callerId,
      details: {
        callId,
        recordingPath: file.path,
        recordingSize: file.size,
        duration: voiceCall.duration,
        morphingProfile
      },
      severity: 'low',
      category: 'voice_communication',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Recording uploaded successfully',
      callId,
      recordingId: voiceCall._id
    });

  } catch (error) {
    console.error('❌ Voice recording upload failed:', error);
    res.status(500).json({
      success: false,
      error: 'Recording upload failed'
    });
  }
});

/**
 * Save voice call metadata
 * POST /api/voice/save-call-metadata
 */
router.post('/save-call-metadata', async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      callId,
      callerId,
      recipientId,
      startTime,
      endTime,
      duration,
      morphingProfile,
      recordingPath,
      status,
      callerIP,
      deviceFingerprint,
      bleDeviceId,
      callQuality,
      metadata
    } = req.body;

    console.log('🎤 Saving call metadata:', { callId, callerId, recipientId });

    // Create or update voice call record
    const voiceCall = await VoiceCallModel.findOneAndUpdate(
      { callId },
      {
        callId,
        callerId: callerId || 'unknown',
        recipientId: recipientId || 'superuser',
        startTime: new Date(startTime),
        endTime: endTime ? new Date(endTime) : undefined,
        duration: duration || 0,
        status: status || 'ended',
        morphingProfile: morphingProfile || 'agent',
        recordingPath,
        callerIP: callerIP || req.ip,
        deviceFingerprint: deviceFingerprint || 'unknown',
        bleDeviceId,
        callQuality: callQuality || 'good',
        metadata: {
          callerUserAgent: req.get('User-Agent') || 'unknown',
          networkType: metadata?.networkType || 'unknown',
          encryptionLevel: metadata?.encryptionLevel || 'AES-256',
          compressionRatio: metadata?.compressionRatio || 0.7,
          audioCodec: metadata?.audioCodec || 'AAC'
        },
        flaggedForReview: false
      },
      { upsert: true, new: true }
    );

    // Log for admin panel
    await AuditLogModel.create({
      action: 'VOICE_CALL_COMPLETED',
      userId: callerId || 'unknown',
      details: {
        callId,
        recipientId,
        duration,
        morphingProfile,
        recordingAvailable: !!recordingPath,
        callQuality
      },
      severity: 'low',
      category: 'voice_communication',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Call metadata saved successfully',
      callId: voiceCall.callId,
      recordId: voiceCall._id
    });

  } catch (error) {
    console.error('❌ Save call metadata failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to save call metadata'
    });
  }
});

/**
 * Get voice calls for admin panel
 * GET /api/voice/admin/calls
 */
router.get('/admin/calls', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const status = req.query.status as string;
    const flagged = req.query.flagged === 'true';
    const dateFrom = req.query.dateFrom as string;
    const dateTo = req.query.dateTo as string;

    // Build filter
    const filter: any = {};
    if (status) filter.status = status;
    if (flagged !== undefined) filter.flaggedForReview = flagged;
    if (dateFrom || dateTo) {
      filter.startTime = {};
      if (dateFrom) filter.startTime.$gte = new Date(dateFrom);
      if (dateTo) filter.startTime.$lte = new Date(dateTo);
    }

    // Get calls with pagination
    const calls = await (VoiceCallModel as IVoiceCallModel).getCallsForAdmin(filter, page, limit);
    const totalCalls = await VoiceCallModel.countDocuments(filter);

    // Get analytics
    const analytics = await (VoiceCallModel as IVoiceCallModel).getCallAnalytics({
      startDate: dateFrom ? new Date(dateFrom) : undefined,
      endDate: dateTo ? new Date(dateTo) : undefined
    });

    res.json({
      success: true,
      data: {
        calls,
        pagination: {
          page,
          limit,
          total: totalCalls,
          pages: Math.ceil(totalCalls / limit)
        },
        analytics: analytics[0] || {
          totalCalls: 0,
          completedCalls: 0,
          failedCalls: 0,
          averageDuration: 0,
          totalDuration: 0,
          recordingCount: 0,
          flaggedCount: 0
        }
      }
    });

  } catch (error) {
    console.error('❌ Get admin calls failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve call data'
    });
  }
});

/**
 * Get voice call recording for admin playback
 * GET /api/voice/admin/recording/:callId
 */
router.get('/admin/recording/:callId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;

    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall || !voiceCall.recordingPath) {
      res.status(404).json({
        success: false,
        error: 'Recording not found'
      });
      return;
    }

    // Check if file exists
    if (!fs.existsSync(voiceCall.recordingPath)) {
      res.status(404).json({
        success: false,
        error: 'Recording file not found'
      });
      return;
    }

    // Log access for audit
    await AuditLogModel.create({
      action: 'VOICE_RECORDING_ACCESSED',
      userId: (req as any).user?.id || 'admin',
      details: {
        callId,
        recordingPath: voiceCall.recordingPath,
        accessedBy: (req as any).user?.username || 'admin'
      },
      severity: 'medium',
      category: 'admin_access',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Stream the audio file
    const stat = fs.statSync(voiceCall.recordingPath);
    const fileSize = stat.size;
    const range = req.headers.range;

    if (range) {
      // Support range requests for audio streaming
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;
      const file = fs.createReadStream(voiceCall.recordingPath, { start, end });
      const head = {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Length': chunksize,
        'Content-Type': 'audio/mpeg',
      };
      res.writeHead(206, head);
      file.pipe(res);
    } else {
      const head = {
        'Content-Length': fileSize,
        'Content-Type': 'audio/mpeg',
      };
      res.writeHead(200, head);
      fs.createReadStream(voiceCall.recordingPath).pipe(res);
    }

  } catch (error) {
    console.error('❌ Get recording failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve recording'
    });
  }
});

/**
 * Flag call for review
 * POST /api/voice/admin/flag/:callId
 */
router.post('/admin/flag/:callId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;
    const { flagged, adminNotes } = req.body;

    const voiceCall = await VoiceCallModel.findOneAndUpdate(
      { callId },
      {
        flaggedForReview: flagged,
        adminNotes,
        reviewedBy: (req as any).user?.username || 'admin',
        reviewedAt: new Date()
      },
      { new: true }
    );

    if (!voiceCall) {
      res.status(404).json({
        success: false,
        error: 'Call not found'
      });
      return;
    }

    // Log admin action
    await AuditLogModel.create({
      action: flagged ? 'VOICE_CALL_FLAGGED' : 'VOICE_CALL_UNFLAGGED',
      userId: (req as any).user?.id || 'admin',
      details: {
        callId,
        adminNotes,
        reviewedBy: (req as any).user?.username || 'admin'
      },
      severity: 'medium',
      category: 'admin_action',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: `Call ${flagged ? 'flagged' : 'unflagged'} successfully`,
      call: (voiceCall as any).getCallStats()
    });

  } catch (error) {
    console.error('❌ Flag call failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update call flag'
    });
  }
});

/**
 * Get voice modulation settings
 * GET /api/voice/modulation-settings
 */
router.get('/modulation-settings', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;

    // Get global voice modulation settings
    let settings = await VoiceProfileModel.findOne({ type: 'global', userId: null });

    if (!settings) {
      // Create default settings
      settings = await VoiceProfileModel.create({
        type: 'global',
        userId: null,
        profile: getDefaultVoiceSettings(),
        updatedBy: adminId,
        updatedAt: new Date()
      });
    }

    // Log admin access
    await AuditLogModel.create({
      action: 'VOICE_SETTINGS_ACCESSED',
      adminId,
      details: {
        settingsType: 'global'
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        settings: settings.profile,
        lastUpdated: settings.updatedAt,
        updatedBy: settings.updatedBy
      }
    });

  } catch (error: any) {
    console.error('Error fetching voice modulation settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch voice modulation settings',
      details: error.message
    });
  }
});

/**
 * Update voice modulation settings
 * PUT /api/voice/modulation-settings
 */
router.put('/modulation-settings', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { settings } = req.body;

    if (!settings || typeof settings !== 'object') {
      res.status(400).json({
        success: false,
        error: 'Settings object is required'
      });
      return;
    }

    // Validate voice settings
    const validationResult = validateVoiceSettings(settings);
    if (!validationResult.isValid) {
      res.status(400).json({
        success: false,
        error: 'Invalid voice modulation settings',
        details: validationResult.errors
      });
    }

    // Get current settings
    let voiceProfile = await VoiceProfileModel.findOne({ type: 'global', userId: null });
    const oldSettings = voiceProfile ? { ...voiceProfile.profile } : {};

    if (!voiceProfile) {
      // Create new settings
      voiceProfile = new VoiceProfileModel({
        type: 'global',
        userId: null,
        profile: settings,
        updatedBy: adminId,
        updatedAt: new Date()
      });
    } else {
      // Update existing settings
      voiceProfile.profile = { ...voiceProfile.profile, ...settings };
      voiceProfile.updatedBy = adminId;
      voiceProfile.updatedAt = new Date();
    }

    await voiceProfile.save();

    // Log admin action
    await AuditLogModel.create({
      action: 'VOICE_SETTINGS_UPDATED',
      adminId,
      details: {
        settingsType: 'global',
        changes: getSettingsChanges(oldSettings, settings),
        affectedSettings: Object.keys(settings)
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Voice modulation settings updated successfully',
      data: {
        settings: voiceProfile.profile,
        updatedAt: voiceProfile.updatedAt,
        changes: getSettingsChanges(oldSettings, settings)
      }
    });

  } catch (error: any) {
    console.error('Error updating voice modulation settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update voice modulation settings',
      details: error.message
    });
  }
});

/**
 * Get user-specific voice profile
 * GET /api/voice/user/:userId/profile
 */
router.get('/user/:userId/profile', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { userId } = req.params;

    // Validate userId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid userId format'
      });
      return;
    }

    // Verify user exists
    const user = await UserModel.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Get user's voice profile
    let voiceProfile = await VoiceProfileModel.findOne({ type: 'user', userId });

    if (!voiceProfile) {
      // Create default user profile based on global settings
      const globalSettings = await VoiceProfileModel.findOne({ type: 'global', userId: null });
      const defaultProfile = globalSettings ? globalSettings.profile : getDefaultVoiceSettings();

      voiceProfile = await VoiceProfileModel.create({
        type: 'user',
        userId,
        profile: { ...defaultProfile, enabled: user.isSuperuser || false },
        updatedBy: adminId,
        updatedAt: new Date()
      });
    }

    // Log admin access
    await AuditLogModel.create({
      action: 'USER_VOICE_PROFILE_ACCESSED',
      adminId,
      userId,
      details: {
        username: user.username,
        isSuperuser: user.isSuperuser
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          isSuperuser: user.isSuperuser
        },
        profile: voiceProfile.profile,
        lastUpdated: voiceProfile.updatedAt,
        updatedBy: voiceProfile.updatedBy
      }
    });

  } catch (error: any) {
    console.error('Error fetching user voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user voice profile',
      details: error.message
    });
  }
});

/**
 * Update user-specific voice profile
 * PUT /api/voice/user/:userId/profile
 */
router.put('/user/:userId/profile', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { userId } = req.params;
    const { profile } = req.body;

    // Validate userId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid userId format'
      });
      return;
    }

    if (!profile || typeof profile !== 'object') {
      res.status(400).json({
        success: false,
        error: 'Profile object is required'
      });
      return;
    }

    // Verify user exists
    const user = await UserModel.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Validate voice settings
    const validationResult = validateVoiceSettings(profile);
    if (!validationResult.isValid) {
      res.status(400).json({
        success: false,
        error: 'Invalid voice profile settings',
        details: validationResult.errors
      });
    }

    // Get current profile
    let voiceProfile = await VoiceProfileModel.findOne({ type: 'user', userId });
    const oldProfile = voiceProfile ? { ...voiceProfile.profile } : {};

    if (!voiceProfile) {
      // Create new profile
      voiceProfile = new VoiceProfileModel({
        type: 'user',
        userId,
        profile: profile,
        updatedBy: adminId,
        updatedAt: new Date()
      });
    } else {
      // Update existing profile
      voiceProfile.profile = { ...voiceProfile.profile, ...profile };
      voiceProfile.updatedBy = adminId;
      voiceProfile.updatedAt = new Date();
    }

    await voiceProfile.save();

    // Log admin action
    await AuditLogModel.create({
      action: 'USER_VOICE_PROFILE_UPDATED',
      adminId,
      userId,
      details: {
        username: user.username,
        changes: getSettingsChanges(oldProfile, profile),
        affectedSettings: Object.keys(profile)
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'User voice profile updated successfully',
      data: {
        user: {
          id: user._id,
          username: user.username,
          isSuperuser: user.isSuperuser
        },
        profile: voiceProfile.profile,
        updatedAt: voiceProfile.updatedAt,
        changes: getSettingsChanges(oldProfile, profile)
      }
    });

  } catch (error: any) {
    console.error('Error updating user voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user voice profile',
      details: error.message
    });
  }
});

/**
 * Get all voice profiles summary
 * GET /api/voice/profiles
 */
router.get('/profiles', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;

    // Get global settings
    const globalProfile = await VoiceProfileModel.findOne({ type: 'global', userId: null });

    // Get all user profiles
    const userProfiles = await VoiceProfileModel.find({ type: 'user' })
      .populate('userId', 'username email isSuperuser status')
      .sort({ updatedAt: -1 });

    // Get users without custom profiles
    const usersWithProfiles = userProfiles
      .filter(p => p.userId)
      .map(p => p.userId!._id.toString());
    const usersWithoutProfiles = await UserModel.find({
      _id: { $nin: usersWithProfiles }
    }).select('username email isSuperuser status');

    // Log admin access
    await AuditLogModel.create({
      action: 'VOICE_PROFILES_SUMMARY_ACCESSED',
      adminId,
      details: {
        totalUserProfiles: userProfiles.length,
        usersWithoutProfiles: usersWithoutProfiles.length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        globalProfile: globalProfile ? globalProfile.profile : null,
        userProfiles: userProfiles.map(profile => ({
          user: profile.userId,
          profile: profile.profile,
          lastUpdated: profile.updatedAt
        })),
        usersWithoutProfiles: usersWithoutProfiles.map(user => ({
          id: user._id,
          username: user.username,
          email: user.email,
          isSuperuser: user.isSuperuser,
          status: user.status,
          usingGlobalProfile: true
        }))
      }
    });

  } catch (error: any) {
    console.error('Error fetching voice profiles summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch voice profiles summary',
      details: error.message
    });
  }
});

/**
 * Test voice modulation profile
 * POST /api/voice/test
 */
router.post('/test', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;
    const { profile, testType = 'synthetic' } = req.body;

    if (!profile || typeof profile !== 'object') {
      res.status(400).json({
        success: false,
        error: 'Profile object is required'
      });
    }

    // Validate voice settings
    const validationResult = validateVoiceSettings(profile);
    if (!validationResult.isValid) {
      res.status(400).json({
        success: false,
        error: 'Invalid voice profile for testing',
        details: validationResult.errors
      });
    }

    // Generate test results
    const testResults = await generateVoiceTestResults(profile, testType);

    // Log admin action
    await AuditLogModel.create({
      action: 'VOICE_PROFILE_TESTED',
      adminId,
      details: {
        testType,
        profileSettings: profile,
        testResults: testResults.summary
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        testType,
        profile,
        results: testResults
      }
    });

  } catch (error: any) {
    console.error('Error testing voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test voice profile',
      details: error.message
    });
  }
});

// Configure multer for audio file uploads  
const audioUpload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit for audio files
  },
  fileFilter: (req: any, file: any, cb: any) => {
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed') as any, false);
    }
  }
});

/**
 * Test voice modulation with uploaded audio
 * POST /api/voice/test-modulation
 */
router.post('/test-modulation', authenticateToken, upload.single('audio'), async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { profileName } = req.body;

    if (!req.file) {
      res.status(400).json({
        success: false,
        error: 'Audio file is required'
      });
      return;
    }

    if (!profileName || !VOICE_PROFILES[profileName as keyof typeof VOICE_PROFILES]) {
      res.status(400).json({
        success: false,
        error: 'Valid profile name is required',
        availableProfiles: Object.keys(VOICE_PROFILES)
      });
      return;
    }

    console.log('Voice modulation test request:', {
      profileName,
      fileSize: req.file.size,
      mimeType: req.file.mimetype
    });

    // Check if SoX is available, but don't fail if it's not
    const soxAvailable = await voiceModulationService.checkSoxAvailability();
    console.log('SoX availability:', soxAvailable);

    // Get the voice profile
    const profile = VOICE_PROFILES[profileName as keyof typeof VOICE_PROFILES];

    // Apply voice modulation (will use fallback if SoX not available)
    const modulatedAudio = await voiceModulationService.modulateVoice(
      req.file.buffer,
      profile,
      adminId
    );

    // Validate that we got valid audio data
    if (!modulatedAudio || modulatedAudio.length === 0) {
      console.error('Voice modulation returned empty result');
      res.status(500).json({
        success: false,
        error: 'Voice modulation processing failed - empty result'
      });
      return;
    }

    console.log('Voice modulation result:', {
      originalSize: req.file.size,
      modulatedSize: modulatedAudio.length,
      soxUsed: soxAvailable
    });

    // Log the test
    await AuditLogModel.create({
      action: 'VOICE_MODULATION_TEST',
      adminId,
      details: {
        profileName,
        originalSize: req.file.size,
        modulatedSize: modulatedAudio.length,
        filename: req.file.originalname,
        soxAvailable
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Return modulated audio
    res.set({
      'Content-Type': 'audio/wav',
      'Content-Length': modulatedAudio.length.toString(),
      'Content-Disposition': `attachment; filename="modulated_${profileName}_${Date.now()}.wav"`
    });

    res.send(modulatedAudio);

  } catch (error: any) {
    console.error('Voice modulation test failed:', error);
    res.status(500).json({
      success: false,
      error: 'Voice modulation test failed',
      details: error.message
    });
  }
});

/**
 * Check voice modulation service status
 * GET /api/voice/service-status
 */
router.get('/service-status', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const soxAvailable = await voiceModulationService.checkSoxAvailability();

    res.json({
      success: true,
      data: {
        soxAvailable,
        status: soxAvailable ? 'operational' : 'unavailable',
        message: soxAvailable ? 'Voice modulation service is ready' : 'SoX is not installed or not in PATH',
        profiles: Object.keys(VOICE_PROFILES),
        profilesWithSamples: soxAvailable,
        customProfilesSupported: true,
        features: [
          'Non-reversible voice morphing',
          'Clear and understandable output',
          'Real-time processing capability',
          'Multiple security profiles',
          'In-browser profile samples',
          'Custom SoX parameter support',
          'User-specific profile restrictions'
        ]
      }
    });

  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: 'Failed to check service status'
    });
  }
});

/**
 * Voice Neutralization Endpoint
 * POST /api/voice/neutralize
 */
router.post('/neutralize', authenticateToken, upload.single('audio'), async (req: Request, res: Response): Promise<void> => {
  try {
    const { profile = 'REAL_TIME_MEDIUM' } = req.body;
    const file = req.file;

    if (!file) {
      res.status(400).json({
        success: false,
        error: 'Audio file is required'
      });
      return;
    }

    console.log('Voice neutralization request:', {
      profile,
      fileSize: file.size,
      mimeType: file.mimetype
    });

    // Import the neutralizer service
    const lightweightVoiceNeutralizer = require('../../services/lightweightVoiceNeutralizer').default;
    const { NEUTRALIZATION_PROFILES } = require('../../services/lightweightVoiceNeutralizer');

    // Get the neutralization profile
    const profileConfig = NEUTRALIZATION_PROFILES[profile as keyof typeof NEUTRALIZATION_PROFILES];
    if (!profileConfig) {
      res.status(400).json({
        success: false,
        error: 'Invalid neutralization profile',
        availableProfiles: Object.keys(NEUTRALIZATION_PROFILES)
      });
      return;
    }

    // Apply voice neutralization
    const neutralizedAudio = await lightweightVoiceNeutralizer.neutralizeVoice(
      file.buffer,
      profileConfig,
      (req as any).user?.id
    );

    // Validate that we got valid audio data
    if (!neutralizedAudio || neutralizedAudio.length === 0) {
      console.error('Voice neutralization returned empty result');
      res.status(500).json({
        success: false,
        error: 'Voice neutralization processing failed - empty result'
      });
      return;
    }

    console.log('Voice neutralization result:', {
      originalSize: file.size,
      neutralizedSize: neutralizedAudio.length,
      profile
    });

    // Log the neutralization
    await createAuditLog('VOICE_NEUTRALIZATION_TEST', req, {
      profile,
      originalSize: file.size,
      neutralizedSize: neutralizedAudio.length,
      filename: file.originalname
    });

    // Return neutralized audio
    res.set({
      'Content-Type': 'audio/wav',
      'Content-Length': neutralizedAudio.length.toString(),
      'Content-Disposition': `attachment; filename="neutralized_${profile}_${Date.now()}.wav"`
    });

    res.send(neutralizedAudio);

  } catch (error: any) {
    console.error('Voice neutralization failed:', error);
    res.status(500).json({
      success: false,
      error: 'Voice neutralization failed',
      details: error.message
    });
  }
});

/**
 * Check real-time neutralization feasibility
 * GET /api/voice/feasibility?audioLengthMs=...&profile=...
 */
router.get('/feasibility', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const audioLengthMs = parseInt(req.query.audioLengthMs as string, 10);
    const profileName = req.query.profile as string;

    if (!audioLengthMs || !profileName) {
      res.status(400).json({
        success: false,
        error: 'audioLengthMs and profile parameters are required'
      });
      return;
    }

    // Import the neutralizer service
    const { NEUTRALIZATION_PROFILES } = require('../../services/lightweightVoiceNeutralizer');

    // Get the profile config
    const profile = NEUTRALIZATION_PROFILES[profileName as keyof typeof NEUTRALIZATION_PROFILES];
    if (!profile) {
      res.status(400).json({
        success: false,
        error: 'Invalid profile name',
        availableProfiles: Object.keys(NEUTRALIZATION_PROFILES)
      });
      return;
    }

    // Check feasibility based on profile latency target and audio length
    const canProcessRealTime = profile.realTimeMode &&
      profile.latencyTarget < 50 &&
      audioLengthMs <= 5 * 60 * 1000; // 5 minutes max

    const recommendations = [];
    if (!canProcessRealTime) {
      if (!profile.realTimeMode) {
        recommendations.push('This profile is designed for offline processing');
      }
      if (profile.latencyTarget >= 50) {
        recommendations.push('Consider using a profile with lower latency target');
      }
      if (audioLengthMs > 5 * 60 * 1000) {
        recommendations.push('Audio length exceeds recommended limit for real-time processing');
      }
    } else {
      recommendations.push('Real-time processing is feasible');
    }

    res.json({
      success: true,
      data: {
        feasible: canProcessRealTime,
        latencyTarget: profile.latencyTarget,
        audioLengthMs,
        profile: profileName,
        recommendations
      }
    });

  } catch (error: any) {
    console.error('Feasibility check failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check feasibility',
      details: error.message
    });
  }
});

// HYBRID VOICE PROCESSING ENDPOINTS

/**
 * GET /api/voice/hybrid/capabilities
 * Check hybrid processing capabilities and estimated latencies
 */
router.get('/hybrid/capabilities', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const capabilities = await hybridProcessor.checkCapabilities();

    res.json({
      success: true,
      data: {
        ...capabilities,
        profiles: Object.keys(HYBRID_VOICE_PROFILES),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Failed to check hybrid capabilities:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check hybrid processing capabilities'
    });
  }
});

/**
 * GET /api/voice/hybrid/profiles
 * Get available hybrid voice profiles
 */
router.get('/hybrid/profiles', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const profilesObj = hybridProcessor.getAvailableProfiles();

    // Convert profiles object to array with profile names as keys
    const profiles = Object.entries(profilesObj).map(([key, profile]) => ({
      id: key,
      name: profile.name,
      description: profile.description,
      type: profile.type,
      latencyTarget: profile.performance?.latencyTarget || 100,
      qualityMode: profile.performance?.qualityMode || 'balanced',
      antiForensic: profile.security?.antiForensic || false,
      realTimeMode: profile.performance?.realTimeMode || false
    }));

    res.json({
      success: true,
      data: {
        profiles,
        count: profiles.length,
        features: {
          realTimeProcessing: true,
          inputToneRemoval: true,
          soxModulation: true,
          antiForensic: true,
          latencyOptimization: true
        }
      }
    });
  } catch (error) {
    console.error('Failed to get hybrid profiles:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get hybrid voice profiles'
    });
  }
});

/**
 * POST /api/voice/hybrid/process
 * Process audio using hybrid SoX + Neutralization
 */
router.post('/hybrid/process',
  authenticateToken,
  requireAdmin,
  upload.single('audio'),
  async (req: Request, res: Response) => {
    try {
      const { profileName } = req.body;
      const adminId = (req as any).admin?.id;

      // Validate input
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: 'No audio file provided'
        });
      }

      if (!profileName || !HYBRID_VOICE_PROFILES[profileName]) {
        return res.status(400).json({
          success: false,
          error: 'Invalid or missing profile name'
        });
      }

      console.log('Hybrid voice processing request:', {
        profileName,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        adminId
      });

      // Get the hybrid profile
      const profile = HYBRID_VOICE_PROFILES[profileName];

      // Process voice using hybrid approach
      const startTime = Date.now();
      const processedAudio = await hybridProcessor.processVoice(
        req.file.buffer,
        profile,
        adminId
      );
      const processingTime = Date.now() - startTime;

      // Validate result
      if (!processedAudio || processedAudio.length === 0) {
        console.error('Hybrid processing returned empty result');
        return res.status(500).json({
          success: false,
          error: 'Hybrid voice processing failed - empty result'
        });
      }

      // Check if client wants JSON response (for testing)
      const format = (req.query.format as string) || 'audio';

      console.log('Hybrid processing result:', {
        inputSize: req.file.size,
        outputSize: processedAudio.length,
        processingTime,
        latencyTarget: profile.performance.latencyTarget,
        profile: profileName
      });

      if (format === 'json') {
        // Return JSON response for testing
        res.json({
          success: true,
          data: {
            processingTime,
            audioSize: processedAudio.length,
            inputSize: req.file.size,
            profile: profileName,
            latencyTarget: profile.performance.latencyTarget,
            processing: {
              pipeline: ['voice-neutralization', profile.security.antiForensic ? 'anti-forensic' : 'modulation'].filter(Boolean)
            },
            performance: {
              neutralizationTime: Math.floor(processingTime * 0.7), // Approximate
              modulationTime: Math.floor(processingTime * 0.3),
              withinTarget: processingTime <= profile.performance.latencyTarget
            },
            security: {
              inputToneRemoved: profile.security.inputToneRemoval,
              antiForensic: profile.security.antiForensic,
              reversible: profile.security.reversible
            }
          }
        });
      } else {
        // Set response headers for audio
        res.setHeader('Content-Type', 'audio/wav');
        res.setHeader('Content-Length', processedAudio.length);
        res.setHeader('X-Processing-Time', processingTime.toString());
        res.setHeader('X-Profile-Used', profileName);
        res.setHeader('X-Input-Tone-Removed', profile.security.inputToneRemoval.toString());
        res.setHeader('X-Anti-Forensic', profile.security.antiForensic.toString());

        // Send processed audio
        res.send(processedAudio);
      }

    } catch (error: any) {
      console.error('Hybrid voice processing failed:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Hybrid voice processing failed'
      });
    }
  }
);

/**
 * POST /api/voice/hybrid/realtime-test
 * Test real-time hybrid processing performance
 */
router.post('/hybrid/realtime-test',
  authenticateToken,
  requireAdmin,
  upload.single('audio'), // Optional file upload
  async (req: Request, res: Response) => {
    try {
      const adminId = (req as any).admin?.id;

      // Allow testing without file upload for performance benchmarking
      let testBuffer: Buffer;
      if (req.file) {
        testBuffer = req.file.buffer;
      } else {
        // Generate a small test audio buffer (sine wave)
        const sampleRate = 44100;
        const duration = 0.1; // 100ms test audio
        const frequency = 440;
        const samples = Math.floor(sampleRate * duration);
        testBuffer = Buffer.alloc(samples * 2);

        for (let i = 0; i < samples; i++) {
          const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 16383;
          testBuffer.writeInt16LE(Math.round(sample), i * 2);
        }
      }

      console.log('Real-time hybrid test request:', {
        fileSize: req.file?.size || 'generated',
        mimeType: req.file?.mimetype || 'test/audio',
        adminId,
        testType: req.file ? 'uploaded' : 'synthetic'
      });

      // Test all real-time profiles
      const results: any[] = [];
      const realTimeProfiles = Object.entries(HYBRID_VOICE_PROFILES)
        .filter(([_, profile]) => profile.performance.realTimeMode);

      for (const [profileName, profile] of realTimeProfiles) {
        const startTime = Date.now();

        try {
          const processedAudio = await hybridProcessor.processVoice(
            testBuffer,
            profile,
            adminId
          );
          const processingTime = Date.now() - startTime;

          results.push({
            profileName,
            success: true,
            processingTime,
            latencyTarget: profile.performance.latencyTarget,
            meetsTarget: processingTime <= profile.performance.latencyTarget,
            outputSize: processedAudio.length,
            inputToneRemoved: profile.security.inputToneRemoval,
            qualityMode: profile.performance.qualityMode
          });

        } catch (error: any) {
          results.push({
            profileName,
            success: false,
            error: error.message,
            processingTime: Date.now() - startTime
          });
        }
      }

      // Calculate overall metrics
      const successfulTests = results.filter(r => r.success);
      const avgProcessingTime = successfulTests.length > 0
        ? successfulTests.reduce((sum, r) => sum + r.processingTime, 0) / successfulTests.length
        : 0;

      res.json({
        success: true,
        data: {
          testResults: results,
          summary: {
            totalTests: results.length,
            successfulTests: successfulTests.length,
            failedTests: results.length - successfulTests.length,
            averageProcessingTime: Math.round(avgProcessingTime),
            realTimeCapable: successfulTests.every(r => r.meetsTarget),
            inputFile: req.file ? {
              size: req.file.size,
              type: req.file.mimetype
            } : {
              size: testBuffer.length,
              type: 'synthetic/audio'
            }
          },
          recommendations: {
            bestRealTimeProfile: successfulTests
              .filter(r => r.meetsTarget)
              .sort((a, b) => a.processingTime - b.processingTime)[0]?.profileName || null,

            fastestProfile: successfulTests
              .sort((a, b) => a.processingTime - b.processingTime)[0]?.profileName || null
          }
        }
      });

    } catch (error: any) {
      console.error('Real-time hybrid test failed:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Real-time hybrid test failed'
      });
    }
  }
);

// ...existing code...

// Helper functions
function getDefaultVoiceSettings() {
  return {
    enabled: true,
    profiles: {
      robotic: {
        pitch: 0.7,
        speed: 1.0,
        distortion: 0.5,
        resonance: 0.3
      },
      chipmunk: {
        pitch: 1.5,
        speed: 1.2,
        distortion: 0.1,
        resonance: 0.8
      },
      deep: {
        pitch: 0.4,
        speed: 0.9,
        distortion: 0.2,
        resonance: 0.6
      },
      alien: {
        pitch: 0.8,
        speed: 1.1,
        distortion: 0.7,
        resonance: 0.4
      }
    },
    superuserDefaults: {
      enabled: true,
      defaultProfile: 'robotic',
      forceModulation: true
    },
    quality: {
      sampleRate: 44100,
      bitDepth: 16,
      bufferSize: 2048
    },
    processing: {
      noiseReduction: true,
      echoCancellation: true,
      autoGainControl: false
    }
  };
}

function validateVoiceSettings(settings: any) {
  const result = {
    isValid: true,
    errors: [] as string[]
  };

  // Validate profiles
  if (settings.profiles) {
    for (const [profileName, profile] of Object.entries(settings.profiles)) {
      if (typeof profile !== 'object') {
        result.errors.push(`Profile ${profileName} must be an object`);
        result.isValid = false;
        continue;
      }

      const p = profile as any;

      // Validate pitch (0.1 to 3.0)
      if (p.pitch !== undefined && (typeof p.pitch !== 'number' || p.pitch < 0.1 || p.pitch > 3.0)) {
        result.errors.push(`Profile ${profileName}: pitch must be between 0.1 and 3.0`);
        result.isValid = false;
      }

      // Validate speed (0.5 to 2.0)
      if (p.speed !== undefined && (typeof p.speed !== 'number' || p.speed < 0.5 || p.speed > 2.0)) {
        result.errors.push(`Profile ${profileName}: speed must be between 0.5 and 2.0`);
        result.isValid = false;
      }

      // Validate distortion (0.0 to 1.0)
      if (p.distortion !== undefined && (typeof p.distortion !== 'number' || p.distortion < 0.0 || p.distortion > 1.0)) {
        result.errors.push(`Profile ${profileName}: distortion must be between 0.0 and 1.0`);
        result.isValid = false;
      }

      // Validate resonance (0.0 to 1.0)
      if (p.resonance !== undefined && (typeof p.resonance !== 'number' || p.resonance < 0.0 || p.resonance > 1.0)) {
        result.errors.push(`Profile ${profileName}: resonance must be between 0.0 and 1.0`);
        result.isValid = false;
      }
    }
  }

  // Validate quality settings
  if (settings.quality) {
    const q = settings.quality;

    if (q.sampleRate && ![8000, 16000, 22050, 44100, 48000].includes(q.sampleRate)) {
      result.errors.push('Sample rate must be one of: 8000, 16000, 22050, 44100, 48000');
      result.isValid = false;
    }

    if (q.bitDepth && ![8, 16, 24, 32].includes(q.bitDepth)) {
      result.errors.push('Bit depth must be one of: 8, 16, 24, 32');
      result.isValid = false;
    }

    if (q.bufferSize && ![256, 512, 1024, 2048, 4096].includes(q.bufferSize)) {
      result.errors.push('Buffer size must be one of: 256, 512, 1024, 2048, 4096');
      result.isValid = false;
    }
  }

  return result;
}

function getSettingsChanges(oldSettings: any, newSettings: any) {
  const changes: any = {};

  for (const key in newSettings) {
    if (JSON.stringify(oldSettings[key]) !== JSON.stringify(newSettings[key])) {
      changes[key] = {
        old: oldSettings[key],
        new: newSettings[key]
      };
    }
  }

  return changes;
}

async function generateVoiceTestResults(profile: any, testType: string) {
  // Simulate voice modulation testing
  const results = {
    summary: {
      passed: true,
      score: 0,
      issues: [] as string[],
      recommendations: [] as string[]
    },
    details: {
      latency: Math.random() * 50 + 10, // 10-60ms
      quality: Math.random() * 30 + 70, // 70-100%
      recognizability: Math.random() * 40 + 30, // 30-70% (lower is better for anonymity)
      resourceUsage: Math.random() * 20 + 5 // 5-25%
    },
    testType
  };

  // Calculate score based on test results
  let score = 100;

  if (results.details.latency > 40) {
    score -= 15;
    results.summary.issues.push('High latency detected');
    results.summary.recommendations.push('Consider reducing buffer size or simplifying processing');
  }

  if (results.details.quality < 80) {
    score -= 10;
    results.summary.issues.push('Audio quality below recommended level');
    results.summary.recommendations.push('Adjust sample rate or bit depth settings');
  }

  if (results.details.recognizability > 60) {
    score -= 20;
    results.summary.issues.push('Voice still too recognizable');
    results.summary.recommendations.push('Increase distortion or adjust pitch more significantly');
  }

  if (results.details.resourceUsage > 20) {
    score -= 5;
    results.summary.issues.push('High resource usage');
    results.summary.recommendations.push('Optimize processing settings for better performance');
  }

  results.summary.score = Math.max(0, score);
  results.summary.passed = score >= 70;

  return results;
}

/**
 * Get all voice calls for admin panel
 * GET /api/voice/calls
 */
router.get('/calls', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const calls = await VoiceCallModel.find()
      .sort({ startTime: -1 })
      .limit(100);

    res.json({
      success: true,
      calls: calls.map(call => ({
        id: call._id,
        callId: call.callId,
        callerId: call.callerId,
        recipientId: call.recipientId,
        startTime: call.startTime,
        endTime: call.endTime,
        duration: call.duration,
        status: call.status,
        morphingProfile: call.morphingProfile,
        recordingPath: call.recordingPath,
        recordingSize: call.recordingSize,
        callerIP: call.callerIP,
        deviceFingerprint: call.deviceFingerprint,
        callQuality: call.callQuality
      }))
    });
  } catch (error) {
    console.error('Error fetching voice calls:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch voice calls'
    });
  }
});

/**
 * Get user voice settings
 * GET /api/voice/user-settings
 */
router.get('/user-settings', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const UserModel = require('../../models/User').default;
    const users = await UserModel.find({ status: 'active' });

    const settings = users.map((user: any) => ({
      userId: user._id.toString(),
      username: user.username,
      defaultMorphingProfile: user.voiceSettings?.defaultMorphingProfile || 'SECURE_MALE',
      voiceCallsEnabled: user.voiceSettings?.voiceCallsEnabled !== false,
      recordingEnabled: user.voiceSettings?.recordingEnabled !== false
    }));

    res.json({
      success: true,
      settings
    });
  } catch (error) {
    console.error('Error fetching user voice settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user voice settings'
    });
  }
});

/**
 * Update user voice profile
 * POST /api/voice/update-user-profile
 */
router.post('/update-user-profile', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, defaultMorphingProfile } = req.body;

    if (!userId || !defaultMorphingProfile) {
      res.status(400).json({
        success: false,
        error: 'User ID and default morphing profile are required'
      });
      return;
    }

    // Validate profile exists
    const profile = VOICE_PROFILES[defaultMorphingProfile as keyof typeof VOICE_PROFILES];
    if (!profile) {
      res.status(400).json({
        success: false,
        error: 'Invalid voice profile',
        availableProfiles: Object.keys(VOICE_PROFILES)
      });
      return;
    }

    const UserModel = require('../../models/User').default;
    const user = await UserModel.findById(userId);

    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Check profile restrictions
    if (profile.userType === "users_only" && user.role === "superuser") {
      res.status(403).json({
        success: false,
        error: 'This voice profile is not available for superusers',
        details: 'The NORMAL profile is restricted to regular users only'
      });
      return;
    }

    // Update user voice settings
    user.voiceSettings = {
      ...user.voiceSettings,
      defaultMorphingProfile
    };

    await user.save();

    res.json({
      success: true,
      message: 'User voice profile updated successfully'
    });
  } catch (error) {
    console.error('Error updating user voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user voice profile'
    });
  }
});

/**
 * Download voice call recording (decrypted for admin)
 * GET /api/voice/recording/:callId
 */
router.get('/recording/:callId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;

    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall || !voiceCall.recordingPath) {
      res.status(404).json({
        success: false,
        error: 'Recording not found'
      });
      return;
    }

    const fs = require('fs');
    const path = require('path');

    const recordingPath = path.resolve(voiceCall.recordingPath);

    if (!fs.existsSync(recordingPath)) {
      res.status(404).json({
        success: false,
        error: 'Recording file not found on disk'
      });
      return;
    }

    // SECURITY: Decrypt the recording for admin access
    if (voiceCall.metadata?.encryptionKey && voiceCall.metadata?.adminAccessKey) {
      try {
        const SecureKeyStorage = require('../../utils/secure-key-storage').default;
        const { decryptFile } = require('../../utils/encryption');

        // Read encrypted file
        const encryptedBuffer = await fs.promises.readFile(recordingPath);

        // Decrypt using admin access key
        const decryptionKey = SecureKeyStorage.decryptKeyWithAdminAccess(
          voiceCall.metadata.adminAccessKey,
          process.env.ADMIN_MASTER_KEY || 'fallback-admin-key'
        );

        // Decrypt file
        const decryptedBuffer = await decryptFile(
          {
            encrypted: encryptedBuffer,
            iv: voiceCall.metadata.fileIv,
            tag: voiceCall.metadata.fileTag
          },
          decryptionKey.toString('hex')
        );

        // Send decrypted audio
        res.setHeader('Content-Type', voiceCall.metadata.originalMimeType || 'audio/wav');
        res.setHeader('Content-Disposition', `attachment; filename="call_${callId}.wav"`);
        res.send(decryptedBuffer);

      } catch (decryptError) {
        console.error('Failed to decrypt recording:', decryptError);
        res.status(500).json({
          success: false,
          error: 'Failed to decrypt recording'
        });
      }
    } else {
      // Legacy: unencrypted recording
      res.setHeader('Content-Type', 'audio/wav');
      res.setHeader('Content-Disposition', `attachment; filename="call_${callId}.wav"`);

      const fileStream = fs.createReadStream(recordingPath);
      fileStream.pipe(res);
    }
  } catch (error) {
    console.error('Error downloading recording:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download recording'
    });
  }
});

/**
 * Get voice profile sample
 * GET /api/voice/profile-sample/:profileName
 */
router.get('/profile-sample/:profileName', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { profileName } = req.params;

    if (!adminId) {
      res.status(401).json({
        success: false,
        error: 'Admin authentication required'
      });
      return;
    }

    // Check if profile exists
    const profile = VOICE_PROFILES[profileName as keyof typeof VOICE_PROFILES];
    if (!profile) {
      res.status(404).json({
        success: false,
        error: 'Voice profile not found',
        availableProfiles: Object.keys(VOICE_PROFILES)
      });
      return;
    }

    // Check if SoX is available
    const soxAvailable = await voiceModulationService.checkSoxAvailability();
    if (!soxAvailable) {
      res.status(503).json({
        success: false,
        error: 'Voice modulation service unavailable (SoX not found)'
      });
      return;
    }

    // Generate simple test audio (browser will handle actual voice processing)
    const sampleAudio = await voiceModulationService.generateSimpleTestAudio(profileName);

    // Set appropriate headers for audio response
    res.set({
      'Content-Type': 'audio/wav',
      'Content-Length': sampleAudio.length.toString(),
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      'Content-Disposition': `inline; filename="${profileName}_sample.wav"`
    });

    res.send(sampleAudio);

    // Log the sample generation
    await AuditLogModel.create({
      action: 'VOICE_PROFILE_SAMPLE_GENERATED',
      adminId,
      details: {
        profileName,
        sampleSize: sampleAudio.length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

  } catch (error: any) {
    console.error('Error generating voice profile sample:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate voice profile sample',
      details: error.message
    });
  }
});

/**
 * Get enhanced voice profiles with metadata
 * GET /api/voice/profiles-enhanced
 */
router.get('/profiles-enhanced', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;

    if (!adminId) {
      res.status(401).json({
        success: false,
        error: 'Admin authentication required'
      });
      return;
    }

    // Get all standard profiles with enhanced metadata
    const enhancedProfiles = Object.entries(VOICE_PROFILES).map(([name, profile]) => ({
      name,
      ...profile,
      sampleUrl: `/api/voice/profile-sample/${name}`,
      isStandard: true,
      isCustom: false
    }));

    // Add custom profile examples
    const customExamples = Object.entries(CUSTOM_PROFILE_EXAMPLES).map(([profileName, profile]) => ({
      ...profile,
      sampleUrl: null, // Custom profiles don't have pre-generated samples
      isStandard: false,
      isCustom: true
    }));

    res.json({
      success: true,
      data: {
        standardProfiles: enhancedProfiles,
        customExamples: customExamples,
        totalProfiles: enhancedProfiles.length + customExamples.length
      }
    });

  } catch (error: any) {
    console.error('Error fetching enhanced profiles:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch enhanced profiles'
    });
  }
});

/**
 * Create custom voice profile
 * POST /api/voice/custom-profile
 */
router.post('/custom-profile', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { name, customSoxArgs, description } = req.body;

    if (!adminId) {
      res.status(401).json({
        success: false,
        error: 'Admin authentication required'
      });
      return;
    }

    if (!name || !customSoxArgs || !Array.isArray(customSoxArgs)) {
      res.status(400).json({
        success: false,
        error: 'Name and customSoxArgs array are required'
      });
      return;
    }

    // Create custom profile
    const customProfile = createCustomVoiceProfile(name, customSoxArgs, description);

    // Validate SoX arguments by testing them
    try {
      const testSample = await voiceModulationService.generateProfileSample(name, customProfile);

      res.json({
        success: true,
        data: {
          profile: customProfile,
          sampleGenerated: true,
          sampleSize: testSample.length
        }
      });

      // Log custom profile creation
      await AuditLogModel.create({
        action: 'CUSTOM_VOICE_PROFILE_CREATED',
        adminId,
        details: {
          profileName: name,
          customSoxArgs,
          description
        },
        timestamp: new Date(),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });

    } catch (soxError: any) {
      res.status(400).json({
        success: false,
        error: 'Invalid SoX arguments',
        details: soxError.message
      });
    }

  } catch (error: any) {
    console.error('Error creating custom profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create custom profile'
    });
  }
});

/**
 * Create custom WORLD voice profile
 * POST /api/voice/profiles/custom
 */
router.post('/profiles/custom', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;
    const { name, description, parameters, userType } = req.body;

    if (!name || !parameters) {
      res.status(400).json({
        success: false,
        error: 'Profile name and parameters are required'
      });
      return;
    }

    // Validate parameters structure
    const requiredParams = ['pitchScale', 'spectralWarp', 'reverbAmount', 'eqTilt', 'temporalJitter', 'spectralNoise'];
    for (const param of requiredParams) {
      if (typeof parameters[param] !== 'number') {
        res.status(400).json({
          success: false,
          error: `Parameter '${param}' must be a number`
        });
        return;
      }
    }

    // Create the custom profile
    const customProfile = {
      name,
      description: description || `Custom voice profile: ${name}`,
      parameters: {
        pitchScale: Math.max(0.5, Math.min(2.0, parameters.pitchScale)),
        spectralWarp: Math.max(-10.0, Math.min(10.0, parameters.spectralWarp)),
        reverbAmount: Math.max(0.0, Math.min(50.0, parameters.reverbAmount)),
        eqTilt: Math.max(-6.0, Math.min(6.0, parameters.eqTilt)),
        temporalJitter: Math.max(0.0, Math.min(0.1, parameters.temporalJitter)),
        spectralNoise: Math.max(0.0, Math.min(0.3, parameters.spectralNoise)),
      },
      userType: userType || 'all',
      isCustom: true,
      antiForensic: true,
      createdBy: adminId,
      createdAt: new Date()
    };

    // For now, just return success - in a full implementation, you'd save to database
    res.json({
      success: true,
      data: {
        profile: customProfile,
        message: 'Custom profile created successfully'
      }
    });

    // Log custom profile creation
    await AuditLogModel.create({
      action: 'CUSTOM_WORLD_VOICE_PROFILE_CREATED',
      adminId,
      details: {
        profileName: name,
        parameters: customProfile.parameters,
        userType,
        description
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

  } catch (error: any) {
    console.error('Error creating custom WORLD voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create custom profile',
      details: error.message
    });
  }
});

/**
 * Create custom WORLD voice profile
 * POST /api/voice/profiles/custom
 */
router.post('/profiles/custom', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;
    const { name, description, parameters, userType } = req.body;

    if (!name || !parameters) {
      res.status(400).json({
        success: false,
        error: 'Profile name and parameters are required'
      });
      return;
    }

    // Validate parameters structure
    const requiredParams = ['pitchScale', 'spectralWarp', 'reverbAmount', 'eqTilt', 'temporalJitter', 'spectralNoise'];
    for (const param of requiredParams) {
      if (typeof parameters[param] !== 'number') {
        res.status(400).json({
          success: false,
          error: `Parameter '${param}' must be a number`
        });
        return;
      }
    }

    // Create the custom profile
    const customProfile = {
      name,
      description: description || `Custom voice profile: ${name}`,
      parameters: {
        pitchScale: Math.max(0.5, Math.min(2.0, parameters.pitchScale)),
        spectralWarp: Math.max(-10.0, Math.min(10.0, parameters.spectralWarp)),
        reverbAmount: Math.max(0.0, Math.min(50.0, parameters.reverbAmount)),
        eqTilt: Math.max(-6.0, Math.min(6.0, parameters.eqTilt)),
        temporalJitter: Math.max(0.0, Math.min(0.1, parameters.temporalJitter)),
        spectralNoise: Math.max(0.0, Math.min(0.3, parameters.spectralNoise)),
      },
      userType: userType || 'all',
      isCustom: true,
      antiForensic: true,
      createdBy: adminId,
      createdAt: new Date()
    };

    // Return success
    res.json({
      success: true,
      data: {
        profile: customProfile,
        message: 'Custom profile created successfully'
      }
    });

  } catch (error: any) {
    console.error('Error creating custom WORLD voice profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create custom profile',
      details: error.message
    });
  }
});

// UNIFIED VOICE PROCESSING ENDPOINTS

/**
 * GET /api/voice/unified/capabilities
 * Check unified processing capabilities
 */
router.get('/unified/capabilities', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const capabilities = await unifiedProcessor.checkCapabilities();

    res.json({
      success: true,
      data: {
        ...capabilities,
        profiles: Object.keys(UNIFIED_VOICE_PROFILES),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Failed to check unified capabilities:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check unified processing capabilities'
    });
  }
});

/**
 * GET /api/voice/unified/profiles
 * Get available unified voice profiles
 */
router.get('/unified/profiles', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const profilesObj = unifiedProcessor.getAvailableProfiles();

    // Convert profiles object to array with profile names as keys
    const profiles = Object.entries(profilesObj).map(([key, profile]) => ({
      id: key,
      name: profile.name,
      description: profile.description,
      type: profile.type,
      latencyTarget: profile.performance?.latencyTarget || 100,
      qualityMode: profile.performance?.qualityMode || 'balanced',
      inputToneRemoval: profile.security?.inputToneRemoval || false,
      antiForensic: profile.security?.antiForensic || false,
      pipeline: profile.pipeline
    }));

    res.json({
      success: true,
      data: {
        profiles,
        count: profiles.length,
        features: {
          unifiedProcessing: true,
          inputToneRemoval: true,
          soxModulation: true,
          voiceNeutralization: true,
          antiForensic: true,
          realTimeCapable: true
        }
      }
    });
  } catch (error) {
    console.error('Failed to get unified profiles:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get unified voice profiles'
    });
  }
});

/**
 * POST /api/voice/unified/process
 * Process audio using unified pipeline with complete input tone removal
 */
router.post('/unified/process',
  authenticateToken,
  requireAdmin,
  upload.single('audio'),
  async (req: Request, res: Response) => {
    try {
      const { profileName } = req.body;
      const adminId = (req as any).admin?.id;

      // Validate input
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: 'No audio file provided'
        });
      }

      if (!profileName || !UNIFIED_VOICE_PROFILES[profileName]) {
        return res.status(400).json({
          success: false,
          error: 'Invalid or missing profile name',
          availableProfiles: Object.keys(UNIFIED_VOICE_PROFILES)
        });
      }

      console.log('Unified voice processing request:', {
        profileName,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        adminId
      });

      // Get the unified profile
      const profile = UNIFIED_VOICE_PROFILES[profileName];

      // Process voice using unified approach
      const startTime = Date.now();
      const processedAudio = await unifiedProcessor.processVoiceUnified(
        req.file.buffer,
        profile,
        adminId
      );
      const processingTime = Date.now() - startTime;

      // Validate result
      if (!processedAudio || processedAudio.length === 0) {
        console.error('Unified processing returned empty result');
        return res.status(500).json({
          success: false,
          error: 'Unified voice processing failed - empty result'
        });
      }

      // Check if client wants JSON response (for testing)
      const format = (req.query.format as string) || 'audio';

      console.log('Unified processing result:', {
        inputSize: req.file.size,
        outputSize: processedAudio.length,
        processingTime,
        latencyTarget: profile.performance.latencyTarget,
        profile: profileName
      });

      if (format === 'json') {
        // Return JSON response for testing
        res.json({
          success: true,
          data: {
            processingTime,
            audioSize: processedAudio.length,
            inputSize: req.file.size,
            profile: profileName,
            latencyTarget: profile.performance.latencyTarget,
            processing: {
              pipeline: [
                profile.pipeline.neutralizationFirst ? 'voice-neutralization' : null,
                profile.pipeline.soxModulation ? 'sox-modulation' : null,
                profile.pipeline.antiForensic ? 'anti-forensic' : null
              ].filter(Boolean)
            },
            performance: {
              withinTarget: processingTime <= profile.performance.latencyTarget,
              qualityMode: profile.performance.qualityMode
            },
            security: {
              inputToneRemoved: profile.security.inputToneRemoval,
              antiForensic: profile.security.antiForensic,
              reversible: profile.security.reversible
            }
          }
        });
      } else {
        // Set response headers for audio
        res.setHeader('Content-Type', 'audio/wav');
        res.setHeader('Content-Length', processedAudio.length);
        res.setHeader('X-Processing-Time', processingTime.toString());
        res.setHeader('X-Profile-Used', profileName);
        res.setHeader('X-Input-Tone-Removed', profile.security.inputToneRemoval.toString());
        res.setHeader('X-Anti-Forensic', profile.security.antiForensic.toString());
        res.setHeader('X-Pipeline-Steps', JSON.stringify([
          profile.pipeline.neutralizationFirst ? 'neutralization' : null,
          profile.pipeline.soxModulation ? 'modulation' : null,
          profile.pipeline.antiForensic ? 'anti-forensic' : null
        ].filter(Boolean)));

        // Send processed audio
        res.send(processedAudio);
      }

    } catch (error: any) {
      console.error('Unified voice processing failed:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Unified voice processing failed'
      });
    }
  }
);

/**
 * POST /api/voice/unified/performance-test
 * Test unified processing performance across all profiles
 */
router.post('/unified/performance-test',
  authenticateToken,
  requireAdmin,
  upload.single('audio'), // Optional file upload
  async (req: Request, res: Response) => {
    try {
      const adminId = (req as any).admin?.id;

      // Allow testing without file upload for performance benchmarking
      let testBuffer: Buffer;
      if (req.file) {
        testBuffer = req.file.buffer;
      } else {
        // Generate a small test audio buffer (sine wave)
        const sampleRate = 44100;
        const duration = 0.1; // 100ms test audio
        const frequency = 440;
        const samples = Math.floor(sampleRate * duration);
        testBuffer = Buffer.alloc(samples * 2);

        for (let i = 0; i < samples; i++) {
          const sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 16383;
          testBuffer.writeInt16LE(Math.round(sample), i * 2);
        }
      }

      console.log('Unified performance test request:', {
        fileSize: req.file?.size || 'generated',
        mimeType: req.file?.mimetype || 'test/audio',
        adminId,
        testType: req.file ? 'uploaded' : 'synthetic'
      });

      // Test unified processing performance
      const testResults = await unifiedProcessor.testUnifiedPerformance(testBuffer);

      res.json({
        success: true,
        data: {
          ...testResults,
          testInfo: {
            audioSize: testBuffer.length,
            testType: req.file ? 'uploaded' : 'synthetic',
            timestamp: new Date().toISOString()
          },
          recommendations: {
            bestOverallProfile: testResults.results
              .filter((r: any) => r.success && r.withinTarget && r.inputToneRemoved)
              .sort((a: any, b: any) => a.processingTime - b.processingTime)[0]?.profile || null,
            fastestProfile: testResults.results
              .filter((r: any) => r.success)
              .sort((a: any, b: any) => a.processingTime - b.processingTime)[0]?.profile || null
          }
        }
      });

    } catch (error: any) {
      console.error('Unified performance test failed:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Unified performance test failed'
      });
    }
  }
);

// REAL-TIME STREAMING ENDPOINTS

/**
 * POST /api/voice/streaming/session
 * Create a new real-time streaming session
 */
router.post('/streaming/session', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { profileName, config } = req.body;
    const userId = (req as any).user?.id || (req as any).admin?.id;

    // Validate profile
    if (!profileName || !UNIFIED_VOICE_PROFILES[profileName]) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or missing profile name',
        availableProfiles: Object.keys(UNIFIED_VOICE_PROFILES)
      });
    }

    // Create streaming session
    const sessionId = streamingProcessor.createStreamingSession(
      profileName,
      config || {},
      userId
    );

    const session = streamingProcessor.getSession(sessionId);

    res.json({
      success: true,
      data: {
        sessionId,
        profile: session?.profile.name,
        config: session?.config,
        status: session?.status,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Failed to create streaming session:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create streaming session'
    });
  }
});

/**
 * GET /api/voice/streaming/session/:sessionId
 * Get streaming session information
 */
router.get('/streaming/session/:sessionId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const session = streamingProcessor.getSession(sessionId);

    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }

    res.json({
      success: true,
      data: {
        sessionId: session.sessionId,
        userId: session.userId,
        profile: session.profile.name,
        config: session.config,
        status: session.status,
        startTime: session.startTime,
        totalProcessed: session.totalProcessed,
        averageLatency: session.averageLatency,
        performance: {
          withinTarget: session.averageLatency <= session.config.latencyTarget,
          latencyTarget: session.config.latencyTarget
        }
      }
    });

  } catch (error: any) {
    console.error('Failed to get session info:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get session information'
    });
  }
});

/**
 * POST /api/voice/streaming/session/:sessionId/pause
 * Pause a streaming session
 */
router.post('/streaming/session/:sessionId/pause', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const success = streamingProcessor.pauseSession(sessionId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Session not found or cannot be paused'
      });
    }

    res.json({
      success: true,
      data: {
        sessionId,
        status: 'paused',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Failed to pause session:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to pause session'
    });
  }
});

/**
 * POST /api/voice/streaming/session/:sessionId/resume
 * Resume a paused streaming session
 */
router.post('/streaming/session/:sessionId/resume', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const success = streamingProcessor.resumeSession(sessionId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Session not found or cannot be resumed'
      });
    }

    res.json({
      success: true,
      data: {
        sessionId,
        status: 'active',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Failed to resume session:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to resume session'
    });
  }
});

/**
 * DELETE /api/voice/streaming/session/:sessionId
 * Stop and cleanup a streaming session
 */
router.delete('/streaming/session/:sessionId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const success = streamingProcessor.stopSession(sessionId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }

    res.json({
      success: true,
      data: {
        sessionId,
        status: 'stopped',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Failed to stop session:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to stop session'
    });
  }
});

/**
 * GET /api/voice/streaming/stats
 * Get real-time streaming statistics
 */
router.get('/streaming/stats', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const stats = streamingProcessor.getStreamingStats();
    const activeSessions = streamingProcessor.getActiveSessions();

    res.json({
      success: true,
      data: {
        ...stats,
        sessions: activeSessions.map(session => ({
          sessionId: session.sessionId,
          userId: session.userId,
          profile: session.profile.name,
          status: session.status,
          totalProcessed: session.totalProcessed,
          averageLatency: session.averageLatency,
          startTime: session.startTime
        })),
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Failed to get streaming stats:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get streaming statistics'
    });
  }
});

// PERFORMANCE MONITORING ENDPOINTS

/**
 * GET /api/voice/performance/stats
 * Get performance statistics
 */
router.get('/performance/stats', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const timeWindow = req.query.timeWindow ? parseInt(req.query.timeWindow as string) : undefined;
    const stats = unifiedProcessor.getPerformanceStats(timeWindow);

    res.json({
      success: true,
      data: {
        ...stats,
        timeWindow: timeWindow || 'all',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Failed to get performance stats:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get performance statistics'
    });
  }
});

/**
 * GET /api/voice/performance/health
 * Get system health status
 */
router.get('/performance/health', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const health = unifiedProcessor.getSystemHealth();

    res.json({
      success: true,
      data: {
        ...health,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Failed to get system health:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get system health'
    });
  }
});

/**
 * GET /api/voice/performance/trends
 * Get performance trends over time
 */
router.get('/performance/trends', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const hours = req.query.hours ? parseInt(req.query.hours as string) : 24;
    const trends = unifiedProcessor.getPerformanceTrends(hours);

    res.json({
      success: true,
      data: {
        trends,
        timeWindow: `${hours} hours`,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Failed to get performance trends:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get performance trends'
    });
  }
});

/**
 * POST /api/voice/performance/reset
 * Reset performance metrics (for testing)
 */
router.post('/performance/reset', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    unifiedProcessor.resetPerformanceMetrics();

    res.json({
      success: true,
      data: {
        message: 'Performance metrics reset successfully',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Failed to reset performance metrics:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to reset performance metrics'
    });
  }
});

// TESTING AND VALIDATION ENDPOINTS

/**
 * POST /api/voice/test/comprehensive
 * Run comprehensive voice processing tests
 */
router.post('/test/comprehensive', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const testSuite = new VoiceProcessingTestSuite();
    const testResults = await testSuite.runAllTests();

    const format = (req.query.format as 'json' | 'html') || 'json';
    // Use getTestResults instead of exportTestResults
    const exportData = await testSuite.getTestResults();

    if (format === 'html') {
      res.setHeader('Content-Type', 'text/html');
      res.send(exportData);
    } else {
      res.json({
        success: true,
        data: exportData
      });
    }

  } catch (error: any) {
    console.error('Failed to run comprehensive tests:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to run comprehensive tests'
    });
  }
});

/**
 * POST /api/voice/test/latency
 * Run latency-specific tests
 */
router.post('/test/latency', authenticateToken, requireAdmin, async (req: Request, res: Response) => {
  try {
    const testSuite = new VoiceProcessingTestSuite();
    const testResults = await testSuite.runAllTests();

    // Filter for latency tests only
    // Ensure testResults is an array before calling find
    const latencyResults = Array.isArray(testResults) ? testResults.find((suite: any) => suite.suiteName === 'Latency Requirements') : undefined;

    res.json({
      success: true,
      data: {
        latencyResults,
        summary: {
          profilesWithinTarget: latencyResults?.results.filter((r: any) => r.success).length || 0,
          totalProfiles: latencyResults?.results.length || 0,
          averageLatency: (latencyResults?.results ?? []).reduce((sum: number, r: any) => sum + r.duration, 0) / ((latencyResults?.results?.length || 1))
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('Failed to run latency tests:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to run latency tests'
    });
  }
});

// Mount real-time voice call routes
router.use('/realtime', realtimeRouter);

// Mount voice recordings routes
router.use('/recordings', recordingsRouter);

// Mount enhanced voice modulation routes
router.use('/', enhancedModulationRouter);

/**
 * BLE Device Management for Voice Calls
 */

// Get user's BLE devices available for voice calls
router.get('/ble-devices', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    const devices = await bleVerificationService.getUserBleDevicesForVoiceCalls(userId);
    const requiresBLE = await bleVerificationService.requiresBleVerification(userId);

    res.json({
      success: true,
      devices,
      requiresBLE,
      count: devices.length
    });

  } catch (error) {
    console.error('Error fetching user BLE devices:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch BLE devices'
    });
  }
});

// Generate BLE authentication challenge for voice call
router.post('/ble-challenge', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?.id;
    const { deviceId } = req.body;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    if (!deviceId) {
      res.status(400).json({
        success: false,
        error: 'Device ID required'
      });
      return;
    }

    const challenge = await bleVerificationService.generateBleChallenge(userId, deviceId);

    if (!challenge) {
      res.status(404).json({
        success: false,
        error: 'BLE device not found or not authorized for voice calls'
      });
      return;
    }

    res.json({
      success: true,
      challenge: challenge.challenge,
      deviceId: challenge.deviceId,
      expiresAt: challenge.expiresAt
    });

  } catch (error) {
    console.error('Error generating BLE challenge:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate BLE challenge'
    });
  }
});

// Check if user requires BLE verification
router.get('/ble-requirement', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    const requiresBLE = await bleVerificationService.requiresBleVerification(userId);
    const user = await UserModel.findById(userId);

    res.json({
      success: true,
      requiresBLE,
      userRole: user?.isSuperuser ? 'superuser' : 'regular',
      message: requiresBLE
        ? 'BLE device verification required for voice calls'
        : 'Voice calls allowed without BLE verification'
    });

  } catch (error) {
    console.error('Error checking BLE requirement:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check BLE requirement'
    });
  }
});

export default router;
