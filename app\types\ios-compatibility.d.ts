/**
 * iOS Compatibility Type Definitions
 * Addresses nullability type specifier errors in iOS compilation
 */

// Fix React Native iOS compilation issues
declare module "react-native" {
  // Extend existing React Native types with iOS-specific fixes
  export interface NativeModulesStatic {
    [name: string]: any;
  }
  
  export interface PlatformStatic {
    OS: 'ios' | 'android' | 'web' | 'windows' | 'macos';
    Version: string | number;
    isPad?: boolean;
    isTVOS?: boolean;
    isTV?: boolean;
    select<T>(specifics: { ios?: T; android?: T; default?: T }): T;
  }
  
  export interface DimensionsStatic {
    get(dim: 'window' | 'screen'): {
      width: number;
      height: number;
      scale: number;
      fontScale: number;
    };
    addEventListener(type: 'change', handler: Function): void;
    removeEventListener(type: 'change', handler: Function): void;
  }
  
  // Fix AppState types for iOS
  export interface AppStateStatic {
    currentState: 'active' | 'background' | 'inactive' | 'unknown' | 'extension';
    addEventListener(type: 'change', listener: (state: string) => void): void;
    removeEventListener(type: 'change', listener: (state: string) => void): void;
  }
  
  // Fix Alert types
  export interface AlertStatic {
    alert(
      title: string,
      message?: string,
      buttons?: Array<{
        text?: string;
        onPress?: () => void;
        style?: 'default' | 'cancel' | 'destructive';
      }>,
      options?: { cancelable?: boolean; onDismiss?: () => void }
    ): void;
  }
  
  // Fix Linking types
  export interface LinkingStatic {
    openURL(url: string): Promise<any>;
    canOpenURL(url: string): Promise<boolean>;
    getInitialURL(): Promise<string | null>;
    addEventListener(type: string, handler: Function): void;
    removeEventListener(type: string, handler: Function): void;
  }
  
  // Fix Vibration types
  export interface VibrationStatic {
    vibrate(pattern?: number | number[], repeat?: boolean): void;
    cancel(): void;
  }
  
  // Fix BackHandler types
  export interface BackHandlerStatic {
    addEventListener(eventName: 'hardwareBackPress', handler: () => boolean): void;
    removeEventListener(eventName: 'hardwareBackPress', handler: () => boolean): void;
    exitApp(): void;
  }
}

// Fix Expo module types for iOS
declare module "expo-av" {
  export interface Audio {
    Recording: any;
    Sound: any;
    setAudioModeAsync(mode: any): Promise<void>;
    getPermissionsAsync(): Promise<any>;
    requestPermissionsAsync(): Promise<any>;
  }

  export interface Video {
    new (props: any): any;
  }

  export interface AVPlaybackStatus {
    isLoaded: boolean;
    uri?: string;
    progressUpdateIntervalMillis?: number;
    durationMillis?: number;
    positionMillis?: number;
    shouldPlay?: boolean;
    isPlaying?: boolean;
    isBuffering?: boolean;
    rate?: number;
    shouldCorrectPitch?: boolean;
    volume?: number;
    isMuted?: boolean;
    isLooping?: boolean;
    didJustFinish?: boolean;
  }

  export const Audio: Audio;
  export const Video: Video;
  export const ResizeMode: any;
  export const AVPlaybackStatus: any;
}

declare module "expo-file-system" {
  export const documentDirectory: string | null;
  export const cacheDirectory: string | null;
  export const bundleDirectory: string | null;

  export enum EncodingType {
    UTF8 = 'utf8',
    Base64 = 'base64'
  }

  export function readAsStringAsync(fileUri: string, options?: { encoding?: EncodingType }): Promise<string>;
  export function writeAsStringAsync(fileUri: string, contents: string, options?: { encoding?: EncodingType }): Promise<void>;
  export function deleteAsync(fileUri: string, options?: any): Promise<void>;
  export function moveAsync(options: { from: string; to: string }): Promise<void>;
  export function copyAsync(options: { from: string; to: string }): Promise<void>;
  export function makeDirectoryAsync(fileUri: string, options?: any): Promise<void>;
  export function readDirectoryAsync(fileUri: string): Promise<string[]>;
  export function getInfoAsync(fileUri: string, options?: any): Promise<any>;
  export function downloadAsync(uri: string, fileUri: string, options?: any): Promise<any>;
  export function uploadAsync(url: string, fileUri: string, options?: any): Promise<any>;
}

declare module "expo-device" {
  export const deviceName: string | null;
  export const deviceType: number | null;
  export const isDevice: boolean;
  export const brand: string | null;
  export const manufacturer: string | null;
  export const modelName: string | null;
  export const modelId: string | null;
  export const designName: string | null;
  export const productName: string | null;
  export const deviceYearClass: number | null;
  export const totalMemory: number | null;
  export const supportedCpuArchitectures: string[] | null;
  export const osName: string | null;
  export const osVersion: string | null;
  export const osBuildId: string | null;
  export const osInternalBuildId: string | null;
  export const osBuildFingerprint: string | null;
  export const platformApiLevel: number | null;
}

declare module "expo-secure-store" {
  export function setItemAsync(key: string, value: string, options?: any): Promise<void>;
  export function getItemAsync(key: string, options?: any): Promise<string | null>;
  export function deleteItemAsync(key: string, options?: any): Promise<void>;
  export function isAvailableAsync(): Promise<boolean>;
}

declare module "expo-haptics" {
  export enum ImpactFeedbackStyle {
    Light = 'light',
    Medium = 'medium',
    Heavy = 'heavy'
  }
  
  export enum NotificationFeedbackType {
    Success = 'success',
    Warning = 'warning',
    Error = 'error'
  }
  
  export function impactAsync(style?: ImpactFeedbackStyle): Promise<void>;
  export function notificationAsync(type?: NotificationFeedbackType): Promise<void>;
  export function selectionAsync(): Promise<void>;
}

declare module "expo-image-picker" {
  export enum MediaTypeOptions {
    All = 'All',
    Videos = 'Videos',
    Images = 'Images'
  }

  export interface ImagePickerOptions {
    mediaTypes?: MediaTypeOptions | 'Images' | 'Videos' | 'All';
    allowsEditing?: boolean;
    aspect?: [number, number];
    quality?: number;
    allowsMultipleSelection?: boolean;
    selectionLimit?: number;
    videoMaxDuration?: number;
    base64?: boolean;
    exif?: boolean;
    presentationStyle?: string;
  }

  export interface ImagePickerResult {
    canceled: boolean;
    cancelled?: boolean; // Legacy support
    assets?: Array<{
      uri: string;
      width: number;
      height: number;
      type?: 'image' | 'video';
      fileName?: string;
      fileSize?: number;
      duration?: number;
      base64?: string;
    }>;
  }

  export function launchImageLibraryAsync(options?: ImagePickerOptions): Promise<ImagePickerResult>;
  export function launchCameraAsync(options?: ImagePickerOptions): Promise<ImagePickerResult>;
  export function getMediaLibraryPermissionsAsync(): Promise<any>;
  export function requestMediaLibraryPermissionsAsync(): Promise<any>;
  export function getCameraPermissionsAsync(): Promise<any>;
  export function requestCameraPermissionsAsync(): Promise<any>;
}

declare module "expo-document-picker" {
  export interface DocumentPickerOptions {
    type?: string | string[];
    copyToCacheDirectory?: boolean;
    multiple?: boolean;
    presentationStyle?: string;
  }

  export interface DocumentPickerAsset {
    uri: string;
    name: string;
    size?: number;
    mimeType?: string;
    lastModified?: number;
    file?: File;
  }

  export interface DocumentPickerResult {
    type: 'success' | 'cancel';
    uri?: string;
    name?: string;
    size?: number;
    mimeType?: string;
    lastModified?: number;
    file?: File;
    output?: any;
    canceled?: boolean;
    cancelled?: boolean; // Legacy support
    assets?: DocumentPickerAsset[];
  }

  export function getDocumentAsync(options?: DocumentPickerOptions): Promise<DocumentPickerResult>;
}

// Fix React Navigation types for iOS
declare module "@react-navigation/native" {
  export function NavigationContainer(props: any): any;
  export function useNavigation(): any;
  export function useRoute(): any;
  export function useFocusEffect(callback: () => void): void;
}

declare module "@react-navigation/stack" {
  export function createStackNavigator(): any;
}

// Fix React Native Gesture Handler types
declare module "react-native-gesture-handler" {
  export const GestureHandlerRootView: any;
  export const PanGestureHandler: any;
  export const Swipeable: any;
  export const State: any;
}

// Fix React Native Reanimated types
declare module "react-native-reanimated" {
  export const Animated: any;
  export function useSharedValue(initialValue: any): any;
  export function useAnimatedStyle(callback: () => any): any;
  export function withTiming(toValue: any, config?: any): any;
  export function withSpring(toValue: any, config?: any): any;
  export function runOnJS(callback: Function): Function;
}

// Fix React Native Safe Area Context types
declare module "react-native-safe-area-context" {
  export const SafeAreaProvider: any;
  export const SafeAreaView: any;
  export function useSafeAreaInsets(): any;
}

// Fix React Native Screens types
declare module "react-native-screens" {
  export const enableScreens: (enabled?: boolean) => void;
}

// Fix React Native SVG types
declare module "react-native-svg" {
  import { ComponentType } from "react";

  export const Svg: ComponentType<any>;
  export const Circle: ComponentType<any>;
  export const Rect: ComponentType<any>;
  export const Path: ComponentType<any>;
  export const G: ComponentType<any>;
  export const Text: ComponentType<any>;
  export const Defs: ComponentType<any>;
  export const LinearGradient: ComponentType<any>;
  export const Stop: ComponentType<any>;
}

// Fix React Native WebRTC types
declare module "react-native-webrtc" {
  export const RTCPeerConnection: any;
  export const RTCIceCandidate: any;
  export const RTCSessionDescription: any;
  export const mediaDevices: any;
  export const MediaStream: any;
  export const MediaStreamTrack: any;
  export const RTCView: any;
}

// Add theme types for the app
declare global {
  interface Theme {
    colors: {
      systemBlue: string;
      systemIndigo: string;
      systemPurple: string;
      systemTeal: string;
      systemGreen: string;
      systemYellow: string;
      systemOrange: string;
      systemRed: string;
      systemPink: string;
      systemGray: string;
      systemGray2: string;
      systemGray3: string;
      systemGray4: string;
      systemGray5: string;
      systemGray6: string;
      label: string;
      secondaryLabel: string;
      tertiaryLabel: string;
      quaternaryLabel: string;
      systemFill: string;
      secondarySystemFill: string;
      tertiarySystemFill: string;
      quaternarySystemFill: string;
      placeholderText: string;
      systemBackground: string;
      secondarySystemBackground: string;
      tertiarySystemBackground: string;
      systemGroupedBackground: string;
      secondarySystemGroupedBackground: string;
      tertiarySystemGroupedBackground: string;
      separator: string;
      opaqueSeparator: string;
      link: string;
      darkText: string;
      lightText: string;
      glassLight: string;
      glassMedium: string;
      glassStrong: string;
      // Additional theme properties
      border: string;
      text: string;
      textSecondary: string;
      surface: string;
      error: string;
      background: string;
      primary: string;
      secondary: string;
    };
  }
}
