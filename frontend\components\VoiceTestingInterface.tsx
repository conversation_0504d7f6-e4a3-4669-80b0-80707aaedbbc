/**
 * Voice Testing Interface Component
 * Simplified, clean interface for testing voice modulation profiles
 */

import React, { useState, useRef, useEffect } from 'react';

interface VoiceProfile {
  name: string;
  pitch: number;
  tempo: number;
  reverb: number;
  distortion: number;
  formant: number;
  chorus: boolean;
  normalize: boolean;
  description?: string;
  type?: 'standard' | 'custom';
  userType?: string;
}

interface VoiceTestingInterfaceProps {
  selectedProfile: string;
  onProfileChange?: (profile: string) => void;
  userRole?: 'superuser' | 'regular';
  userId?: string;
}

const VOICE_PROFILES: Record<string, VoiceProfile> = {
  NORMAL: {
    name: 'Normal',
    pitch: 1.0,
    tempo: 1.0,
    reverb: 0,
    distortion: 0,
    formant: 1.0,
    chorus: false,
    normalize: true,
    description: 'No modulation applied',
    type: 'standard'
  },
  SECURE_MALE: {
    name: 'Secure Male',
    pitch: 0.85,
    tempo: 0.95,
    reverb: 0.1,
    distortion: 0.05,
    formant: 0.9,
    chorus: false,
    normalize: true,
    description: 'Male voice profile with security enhancements',
    type: 'standard'
  },
  SECURE_FEMALE: {
    name: 'Secure Female',
    pitch: 1.15,
    tempo: 1.05,
    reverb: 0.1,
    distortion: 0.05,
    formant: 1.1,
    chorus: false,
    normalize: true,
    description: 'Female voice profile with security enhancements',
    type: 'standard'
  },
  ROBOTIC: {
    name: 'Robotic',
    pitch: 0.9,
    tempo: 0.9,
    reverb: 0.2,
    distortion: 0.15,
    formant: 0.85,
    chorus: true,
    normalize: true,
    description: 'Robotic voice transformation',
    type: 'standard'
  }
};

const VoiceTestingInterface: React.FC<VoiceTestingInterfaceProps> = ({
  selectedProfile,
  onProfileChange,
  userRole = 'regular',
  userId
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [processedAudio, setProcessedAudio] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [profiles, setProfiles] = useState<Record<string, VoiceProfile>>(VOICE_PROFILES);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Load profiles from backend on component mount
  useEffect(() => {
    const loadProfiles = async () => {
      try {
        setIsLoading(true);
        const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
        const response = await fetch('/api/voice/profiles', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const backendProfiles = await response.json();
          setProfiles(backendProfiles);
        }
      } catch (error) {
        console.error('Failed to load voice profiles:', error);
        // Fall back to default profiles
      } finally {
        setIsLoading(false);
      }
    };

    loadProfiles();
  }, []);

  const startRecording = async () => {
    try {
      setError(null);
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        } 
      });
      
      streamRef.current = stream;
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        setAudioBlob(audioBlob);
        
        // Clean up stream
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }
      };
      
      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);
      
      // Start recording timer
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
    } catch (error) {
      console.error('Error starting recording:', error);
      setError('Failed to start recording. Please check microphone permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
        recordingIntervalRef.current = null;
      }
    }
  };

  const processAudio = async () => {
    if (!audioBlob) return;
    
    try {
      setIsProcessing(true);
      setError(null);
      
      // Convert webm to wav for backend compatibility
      const wavBlob = await convertToWav(audioBlob);
      
      const formData = new FormData();
      formData.append('audio', wavBlob, 'recording.wav');
      formData.append('profileName', selectedProfile);

      const token = localStorage.getItem('ccalc-admin-token') || localStorage.getItem('admin-token');
      const response = await fetch('/api/voice/test-modulation', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Backend processing failed: ${response.statusText}`);
      }

      const processedBlob = await response.blob();
      const audioUrl = URL.createObjectURL(processedBlob);
      setProcessedAudio(audioUrl);
      
    } catch (error) {
      console.error('Error processing audio:', error);
      setError('Failed to process audio. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const convertToWav = async (webmBlob: Blob): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const fileReader = new FileReader();
      
      fileReader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
          
          // Convert to WAV format
          const wavArrayBuffer = audioBufferToWav(audioBuffer);
          const wavBlob = new Blob([wavArrayBuffer], { type: 'audio/wav' });
          resolve(wavBlob);
        } catch (error) {
          reject(error);
        }
      };
      
      fileReader.onerror = () => reject(new Error('Failed to read audio file'));
      fileReader.readAsArrayBuffer(webmBlob);
    });
  };

  const audioBufferToWav = (buffer: AudioBuffer): ArrayBuffer => {
    const length = buffer.length;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);
    const channels = [buffer.getChannelData(0)];
    let pos = 0;

    // WAV header
    const setUint32 = (data: number) => {
      view.setUint32(pos, data, true);
      pos += 4;
    };

    const setUint16 = (data: number) => {
      view.setUint16(pos, data, true);
      pos += 2;
    };

    setUint32(0x46464952); // "RIFF"
    setUint32(36 + length * 2); // file length - 8
    setUint32(0x45564157); // "WAVE"
    setUint32(0x20746d66); // "fmt " chunk
    setUint32(16); // length = 16
    setUint16(1); // PCM (uncompressed)
    setUint16(1); // mono
    setUint32(buffer.sampleRate);
    setUint32(buffer.sampleRate * 2); // avg. bytes/sec
    setUint16(2); // block-align
    setUint16(16); // 16-bit
    setUint32(0x61746164); // "data" - chunk
    setUint32(length * 2); // chunk length

    // Convert float32 to int16
    const floatTo16BitPCM = (output: DataView, offset: number, input: Float32Array) => {
      for (let i = 0; i < input.length; i++, offset += 2) {
        const s = Math.max(-1, Math.min(1, input[i]));
        output.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
      }
    };

    floatTo16BitPCM(view, pos, channels[0]);
    return arrayBuffer;
  };

  const playOriginal = () => {
    if (audioBlob) {
      const audio = new Audio(URL.createObjectURL(audioBlob));
      audio.play();
    }
  };

  const playProcessed = () => {
    if (processedAudio) {
      const audio = new Audio(processedAudio);
      audio.play();
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getAvailableProfiles = () => {
    return Object.entries(profiles).filter(([key, profile]) => {
      if (userRole === 'superuser' && key === 'NORMAL') {
        return false; // Hide NORMAL profile for superusers
      }
      return true;
    });
  };

  const clearRecording = () => {
    setAudioBlob(null);
    setProcessedAudio(null);
    setRecordingTime(0);
    setError(null);
  };

  if (isLoading) {
    return (
      <div className="voice-testing-loading">
        <div className="loading-spinner"></div>
        <p>Loading voice profiles...</p>
      </div>
    );
  }

  return (
    <div className="voice-testing-interface">
      <div className="voice-testing-header">
        <h3>Voice Testing Interface</h3>
        <p>Test voice modulation profiles with real audio</p>
      </div>

      {error && (
        <div className="error-message" style={{ 
          background: '#ffebee', 
          color: '#c62828', 
          padding: '12px', 
          borderRadius: '4px', 
          marginBottom: '16px',
          border: '1px solid #ffcdd2'
        }}>
          {error}
        </div>
      )}

      <div className="profile-selection">
        <label htmlFor="profile-select">Select Voice Profile:</label>
        <select 
          id="profile-select"
          value={selectedProfile} 
          onChange={(e) => onProfileChange?.(e.target.value)}
          style={{
            width: '100%',
            padding: '8px',
            margin: '8px 0',
            borderRadius: '4px',
            border: '1px solid #ccc'
          }}
        >
          {getAvailableProfiles().map(([key, profile]) => (
            <option key={key} value={key}>
              {profile.name} - {profile.description}
            </option>
          ))}
        </select>
      </div>

      <div className="recording-section">
        <div className="recording-controls">
          {!isRecording ? (
            <button 
              onClick={startRecording}
              disabled={isProcessing}
              style={{
                background: '#4caf50',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '16px'
              }}
            >
              🎤 Start Recording
            </button>
          ) : (
            <button 
              onClick={stopRecording}
              style={{
                background: '#f44336',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '16px'
              }}
            >
              ⏹️ Stop Recording
            </button>
          )}
          
          {audioBlob && (
            <button 
              onClick={clearRecording}
              style={{
                background: '#9e9e9e',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '16px',
                marginLeft: '8px'
              }}
            >
              🗑️ Clear
            </button>
          )}
        </div>

        {isRecording && (
          <div className="recording-indicator" style={{ 
            background: '#ffcdd2', 
            padding: '12px', 
            borderRadius: '4px', 
            margin: '16px 0',
            textAlign: 'center'
          }}>
            <span style={{ color: '#f44336', fontWeight: 'bold' }}>● Recording: {formatTime(recordingTime)}</span>
          </div>
        )}
      </div>

      {audioBlob && (
        <div className="audio-playback">
          <h4>Audio Playback</h4>
          <div className="playback-controls" style={{ display: 'flex', gap: '16px', margin: '16px 0' }}>
            <button 
              onClick={playOriginal}
              style={{
                background: '#2196f3',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              ▶️ Play Original
            </button>
            
            {processedAudio && (
              <button 
                onClick={playProcessed}
                style={{
                  background: '#ff9800',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                ▶️ Play Processed
              </button>
            )}
          </div>

          <button 
            onClick={processAudio}
            disabled={isProcessing || !audioBlob}
            style={{
              background: isProcessing ? '#9e9e9e' : '#9c27b0',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '4px',
              cursor: isProcessing ? 'not-allowed' : 'pointer',
              fontSize: '16px',
              width: '100%'
            }}
          >
            {isProcessing ? '🔄 Processing...' : '🎛️ Process with Selected Profile'}
          </button>
        </div>
      )}

      {profiles[selectedProfile] && (
        <div className="profile-details" style={{ 
          background: '#f5f5f5', 
          padding: '16px', 
          borderRadius: '4px', 
          marginTop: '16px' 
        }}>
          <h4>Profile Details: {profiles[selectedProfile].name}</h4>
          <div className="profile-parameters" style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
            <div>Pitch: {profiles[selectedProfile].pitch}x</div>
            <div>Tempo: {profiles[selectedProfile].tempo}x</div>
            <div>Reverb: {(profiles[selectedProfile].reverb * 100).toFixed(0)}%</div>
            <div>Distortion: {(profiles[selectedProfile].distortion * 100).toFixed(0)}%</div>
            <div>Formant: {profiles[selectedProfile].formant}x</div>
            <div>Chorus: {profiles[selectedProfile].chorus ? 'Yes' : 'No'}</div>
          </div>
          <p style={{ marginTop: '12px', fontStyle: 'italic' }}>
            {profiles[selectedProfile].description}
          </p>
        </div>
      )}
    </div>
  );
};

export default VoiceTestingInterface;
