/**
 * Test script to validate media endpoint functionality
 * Run with: node test-media-endpoint.js
 */

const fs = require('fs');
const path = require('path');

async function testMediaEndpoint() {
  console.log('🧪 Testing Media Endpoint Functionality');
  console.log('=====================================');

  // Check if uploads directory exists
  const uploadsDir = path.join(__dirname, 'uploads');
  const mediaDir = path.join(uploadsDir, 'media');
  
  console.log('📂 Checking directories:');
  console.log(`   Uploads: ${fs.existsSync(uploadsDir) ? '✅' : '❌'} ${uploadsDir}`);
  console.log(`   Media: ${fs.existsSync(mediaDir) ? '✅' : '❌'} ${mediaDir}`);
  
  if (fs.existsSync(mediaDir)) {
    const mediaFiles = fs.readdirSync(mediaDir);
    console.log(`   Media files: ${mediaFiles.length}`);
    
    // Show first few media files
    mediaFiles.slice(0, 5).forEach(file => {
      const filePath = path.join(mediaDir, file);
      const stats = fs.statSync(filePath);
      console.log(`   - ${file} (${Math.round(stats.size / 1024)}KB)`);
    });
    
    if (mediaFiles.length > 5) {
      console.log(`   ... and ${mediaFiles.length - 5} more files`);
    }
  }

  // Test database connection
  console.log('\n🔌 Testing Database Connection:');
  try {
    const mongoose = require('mongoose');
    const Media = require('./models/Media').default;
    
    if (mongoose.connection.readyState === 1) {
      console.log('   ✅ Database connected');
      
      // Count media records
      const mediaCount = await Media.countDocuments({ isActive: true });
      console.log(`   📊 Active media records: ${mediaCount}`);
      
      // Show sample media record
      const sampleMedia = await Media.findOne({ isActive: true }).limit(1);
      if (sampleMedia) {
        console.log('   📄 Sample media record:');
        console.log(`      ID: ${sampleMedia.mediaId}`);
        console.log(`      Name: ${sampleMedia.file.originalName}`);
        console.log(`      Type: ${sampleMedia.file.mimeType}`);
        console.log(`      Size: ${Math.round(sampleMedia.file.size / 1024)}KB`);
        console.log(`      Encrypted: ${fs.existsSync(sampleMedia.storage.encryptedPath) ? '✅' : '❌'}`);
      }
    } else {
      console.log('   ❌ Database not connected');
    }
  } catch (error) {
    console.log(`   ❌ Database error: ${error.message}`);
  }

  // Test encryption utilities
  console.log('\n🔐 Testing Encryption Utilities:');
  try {
    const { encryptFile, decryptFile } = require('./utils/encryption');
    
    // Test with sample data
    const testData = Buffer.from('Hello, World!');
    const testKey = 'a'.repeat(64); // 32 bytes in hex
    
    const encrypted = encryptFile(testData, testKey);
    console.log('   ✅ Encryption successful');
    
    const decrypted = decryptFile(encrypted, testKey);
    const isValid = decrypted.toString() === 'Hello, World!';
    console.log(`   ${isValid ? '✅' : '❌'} Decryption ${isValid ? 'successful' : 'failed'}`);
    
  } catch (error) {
    console.log(`   ❌ Encryption test failed: ${error.message}`);
  }

  console.log('\n🎯 Test Complete!');
  console.log('================');
  console.log('If all tests pass, the media endpoint should work correctly.');
  console.log('If tests fail, check the error messages above for troubleshooting.');
}

// Run the test
if (require.main === module) {
  testMediaEndpoint().catch(console.error);
}

module.exports = { testMediaEndpoint };
