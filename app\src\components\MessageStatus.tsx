/**
 * WhatsApp-style Message Status Indicators
 * Vector-based status icons that match <PERSON>sApp's visual design
 * No double checkmarks or read receipts as per requirements
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Svg, Path, Circle } from 'react-native-svg';
import { theme } from '../utils/theme';

export type MessageStatusType = 'sending' | 'sent' | 'delivered' | 'failed' | 'queued';

// Status hierarchy (from lowest to highest):
// 1. 'queued' - Message is waiting to be sent
// 2. 'sending' - Message is being sent
// 3. 'sent' - Message sent to server but not confirmed delivered
// 4. 'delivered' - Message confirmed delivered by server (FINAL STATE)
// 5. 'failed' - Message failed to send (FINAL STATE)

interface MessageStatusProps {
  status: MessageStatusType;
  isConnected: boolean;
  size?: number;
  color?: string;
}

export const MessageStatus: React.FC<MessageStatusProps> = ({
  status,
  isConnected,
  size = 14,
  color = theme.colors.textSecondary,
}) => {
  const getStatusColor = (): string => {
    switch (status) {
      case 'failed':
        return theme.colors.error;
      case 'delivered':
        return theme.colors.primary;
      case 'sending':
      case 'queued':
        return theme.colors.textSecondary;
      default:
        return color;
    }
  };

  const renderStatusIcon = () => {
    const iconColor = getStatusColor();
    const iconSize = size;

    // CRITICAL FIX: Never revert delivered messages to pending status
    // Once a message is delivered, it stays delivered regardless of connection state
    switch (status) {
      case 'sending':
      case 'queued':
        return renderClockIcon(iconSize, iconColor);
      case 'sent':
        // Show clock for 'sent' only if not connected (pending server confirmation)
        // But if disconnected, still show clock since server confirmation is pending
        if (!isConnected) {
          return renderClockIcon(iconSize, iconColor);
        }
        return renderCheckmarkIcon(iconSize, iconColor);
      case 'delivered':
        // ALWAYS show checkmark for delivered messages, regardless of connection state
        return renderCheckmarkIcon(iconSize, iconColor);
      case 'failed':
        return renderWarningIcon(iconSize, iconColor);
      default:
        return null;
    }
  };

  // WhatsApp-style clock icon for pending messages
  const renderClockIcon = (size: number, color: string) => (
    <Svg width={size} height={size} viewBox="0 0 24 24">
      <Circle
        cx="12"
        cy="12"
        r="10"
        fill="none"
        stroke={color}
        strokeWidth="1.5"
        opacity="0.8"
      />
      <Path
        d="M12 6v6l4 2"
        fill="none"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
        opacity="0.8"
      />
    </Svg>
  );

  // WhatsApp-style single checkmark for delivered messages
  const renderCheckmarkIcon = (size: number, color: string) => (
    <Svg width={size} height={size} viewBox="0 0 24 24">
      <Path
        d="M20 6L9 17l-5-5"
        fill="none"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );

  // Warning icon for failed messages
  const renderWarningIcon = (size: number, color: string) => (
    <Svg width={size} height={size} viewBox="0 0 24 24">
      <Path
        d="M12 9v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        fill="none"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );

  const icon = renderStatusIcon();
  if (!icon) return null;

  return (
    <View style={styles.container}>
      {icon}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 4,
    // Ensure proper alignment with text
    minWidth: 16,
    minHeight: 16,
  },
});
