/**
 * Build script for WORLD vocoder native addon
 * Handles compilation and fallback setup
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const ADDON_PATH = path.join(__dirname, '..', 'build', 'Release', 'world_vocoder.node');
const BINDING_GYP_PATH = path.join(__dirname, '..', 'binding.gyp');

console.log('🔧 Building WORLD vocoder native addon...');

// Check if binding.gyp exists
if (!fs.existsSync(BINDING_GYP_PATH)) {
  console.log('📝 Creating binding.gyp configuration...');
  
  const bindingConfig = `{
  "targets": [
    {
      "target_name": "world_vocoder",
      "sources": [
        "src/world_vocoder.cpp",
        "src/world_wrapper.cpp"
      ],
      "include_dirs": [
        "<!(node -e \\"console.log(require('node-addon-api').include)\\")",
        "src/world",
        "src/world/src"
      ],
      "dependencies": [
        "<!(node -e \\"console.log(require('node-addon-api').gyp)\\")"
      ],
      "cflags!": [ "-fno-exceptions" ],
      "cflags_cc!": [ "-fno-exceptions" ],
      "xcode_settings": {
        "GCC_ENABLE_CPP_EXCEPTIONS": "YES",
        "CLANG_CXX_LIBRARY": "libc++",
        "MACOSX_DEPLOYMENT_TARGET": "10.7"
      },
      "msvs_settings": {
        "VCCLCompilerTool": { "ExceptionHandling": 1 }
      },
      "libraries": [
        "-lm"
      ],
      "defines": [ "NAPI_DISABLE_CPP_EXCEPTIONS" ]
    }
  ]
}`;

  fs.writeFileSync(BINDING_GYP_PATH, bindingConfig);
  console.log('✅ binding.gyp created');
}

// Check if WORLD vocoder source exists
const worldSrcPath = path.join(__dirname, '..', 'src', 'world');
if (!fs.existsSync(worldSrcPath)) {
  console.log('📁 Creating WORLD vocoder source directory structure...');
  
  // Create directory structure
  fs.mkdirSync(path.join(__dirname, '..', 'src'), { recursive: true });
  fs.mkdirSync(worldSrcPath, { recursive: true });
  fs.mkdirSync(path.join(worldSrcPath, 'src'), { recursive: true });
  
  // Create placeholder C++ files
  const worldVocoderCpp = `// WORLD Vocoder Native Addon
// This is a placeholder implementation
// Replace with actual WORLD vocoder library integration

#include <napi.h>
#include <vector>
#include <cmath>

// Placeholder WORLD vocoder implementation
class WorldVocoder {
public:
    static Napi::Object Init(Napi::Env env, Napi::Object exports);
    static Napi::Value CreateProcessor(const Napi::CallbackInfo& info);
    static Napi::Value ProcessAudioFrame(const Napi::CallbackInfo& info);
    static Napi::Value CleanupProcessor(const Napi::CallbackInfo& info);
};

Napi::Object WorldVocoder::Init(Napi::Env env, Napi::Object exports) {
    exports.Set(Napi::String::New(env, "createProcessor"), 
                Napi::Function::New(env, CreateProcessor));
    exports.Set(Napi::String::New(env, "processAudioFrame"), 
                Napi::Function::New(env, ProcessAudioFrame));
    exports.Set(Napi::String::New(env, "cleanupProcessor"), 
                Napi::Function::New(env, CleanupProcessor));
    return exports;
}

Napi::Value WorldVocoder::CreateProcessor(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    // Return a placeholder processor ID
    return Napi::Number::New(env, 1);
}

Napi::Value WorldVocoder::ProcessAudioFrame(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 3) {
        Napi::TypeError::New(env, "Expected 3 arguments").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    // Get input audio data
    Napi::TypedArray inputArray = info[1].As<Napi::TypedArray>();
    Napi::ArrayBuffer inputBuffer = inputArray.ArrayBuffer();
    float* inputData = reinterpret_cast<float*>(inputBuffer.Data());
    size_t inputLength = inputArray.ElementLength();
    
    // Create output array
    Napi::ArrayBuffer outputBuffer = Napi::ArrayBuffer::New(env, inputLength * sizeof(float));
    float* outputData = reinterpret_cast<float*>(outputBuffer.Data());
    
    // Simple placeholder processing - apply basic pitch shift
    for (size_t i = 0; i < inputLength; i++) {
        // Simple pitch shifting approximation
        size_t sourceIndex = static_cast<size_t>(i * 0.8); // Lower pitch
        if (sourceIndex < inputLength) {
            outputData[i] = inputData[sourceIndex] * 0.8f; // Reduce amplitude
        } else {
            outputData[i] = 0.0f;
        }
    }
    
    return Napi::Float32Array::New(env, inputLength, outputBuffer, 0);
}

Napi::Value WorldVocoder::CleanupProcessor(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    return env.Undefined();
}

Napi::Object Init(Napi::Env env, Napi::Object exports) {
    return WorldVocoder::Init(env, exports);
}

NODE_API_MODULE(world_vocoder, Init)`;

  const worldWrapperCpp = `// WORLD Vocoder Wrapper
// Additional wrapper functions for the WORLD vocoder

#include "world_vocoder.cpp"

// Additional wrapper functions can be added here`;

  fs.writeFileSync(path.join(__dirname, '..', 'src', 'world_vocoder.cpp'), worldVocoderCpp);
  fs.writeFileSync(path.join(__dirname, '..', 'src', 'world_wrapper.cpp'), worldWrapperCpp);
  
  console.log('✅ Placeholder C++ source files created');
}

// Try to build the native addon
try {
  console.log('🔨 Compiling native addon...');
  execSync('node-gyp rebuild', { 
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit' 
  });
  
  if (fs.existsSync(ADDON_PATH)) {
    console.log('✅ WORLD vocoder native addon built successfully!');
    console.log('📍 Addon location:', ADDON_PATH);
  } else {
    throw new Error('Addon file not found after build');
  }
} catch (error) {
  console.error('❌ Failed to build native addon:', error.message);
  console.log('');
  console.log('🔄 The system will use fallback voice processing instead.');
  console.log('📝 To enable native WORLD vocoder:');
  console.log('   1. Install build tools: npm install -g node-gyp');
  console.log('   2. Install Visual Studio Build Tools (Windows) or Xcode (macOS)');
  console.log('   3. Replace placeholder C++ files with actual WORLD vocoder library');
  console.log('   4. Run: npm run build:world');
  console.log('');
  console.log('✅ Fallback mode is fully functional for development and testing.');
}

console.log('🎉 Build process completed!');
