/**
 * Unified Chat API - Fixes Media Attachment Linking Issues
 * Replaces separate mobile/main chat systems with unified approach
 */

import { Router, Request, Response } from 'express';
import mongoose from 'mongoose';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { authenticateToken } from '../../middleware/auth';
import { Message, Chat } from '../../models/Chat';
import Media from '../../models/Media';
import UserModel from '../../models/User';
import AuditLogModel from '../../models/AuditLog';
import crypto from 'crypto';
import { encryptFile, encryptMessage } from '../../utils/encryption';
import { generateThumbnail } from '../../utils/media-processing';
import { validateFileType, validateFileSize } from '../../utils/file-validation';
import { existsSync, mkdirSync } from 'fs';
import websocketService from '../../services/websocket';
import SecureKeyStorage from '../../utils/secure-key-storage';
import e2eEncryptionService from '../../services/e2e-encryption';

// Interface for message with media record
interface MessageWithMediaRecord extends mongoose.Document {
  [key: string]: any;
  mediaRecord?: any;
}

const router = Router();

// Configure multer for file uploads with memory storage for encryption
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 1,
  },
  fileFilter: (req, file, cb) => {
    const validation = validateFileType(file.mimetype, file.originalname);
    if (!validation.isValid) {
      cb(new Error(validation.error));
      return;
    }
    cb(null, true);
  },
});

// Ensure upload directories exist
const UPLOAD_DIR = path.join(process.cwd(), 'uploads', 'media');
const THUMBNAIL_DIR = path.join(process.cwd(), 'uploads', 'thumbnails');

[UPLOAD_DIR, THUMBNAIL_DIR].forEach(dir => {
  if (!existsSync(dir)) {
    mkdirSync(dir, { recursive: true });
  }
});

/**
 * Send Message with Proper Media Linking
 * POST /api/chat/unified/send
 * 
 * Fixes the critical media attachment linking bug by:
 * 1. Creating media record first with proper encryption
 * 2. Linking media to message during message creation
 * 3. Using unified chat system for consistency
 */
router.post('/send', upload.single('attachment'), authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { message, recipientId } = req.body;
    const file = req.file;
    const userId = (req as any).user?._id || (req as any).user?.id;
    const username = (req as any).user?.username;
    const isSuperuser = (req as any).user?.isSuperuser;
    const deviceFingerprint = req.headers['x-device-fingerprint'] as string || 'unknown';
    const bleUUID = req.headers['x-ble-uuid'] as string || 'unknown';

    console.log('📱 Unified chat send request:', {
      userId,
      username,
      isSuperuser,
      messageLength: message?.length,
      hasAttachment: !!file,
      fileName: file?.originalname,
      recipientId
    });

    // Validate required fields
    if (!message && !file) {
      res.status(400).json({
        success: false,
        error: 'Message text or attachment required'
      });
      return;
    }

    // Get or create recipient (superuser for mobile users)
    let actualRecipientId = recipientId;
    if (!isSuperuser) {
      // Mobile users can only send to superuser
      let superuser = await UserModel.findOne({ isSuperuser: true });
      if (!superuser) {
        console.log('⚠️ No superuser found, creating default superuser...');
        // Create a default superuser if none exists
        superuser = new UserModel({
          username: 'superuser',
          profile: {
            displayName: 'System Administrator'
          },
          expressionHash: 'system-default-hash',
          status: 'active',
          isSuperuser: true,
          deviceFingerprintHash: 'system-default',
          encryption: {
            publicKey: 'default-public-key',
            keyGeneratedAt: new Date()
          }
        });
        await superuser.save();
        console.log('✅ Default superuser created');
      }
      actualRecipientId = (superuser._id as mongoose.Types.ObjectId).toString();
    } else if (!recipientId) {
      // If no recipient specified and user is superuser, this is an error
      res.status(400).json({
        success: false,
        error: 'Recipient ID required for superuser messages'
      });
      return;
    }

    const recipient = await UserModel.findById(actualRecipientId);
    if (!recipient) {
      res.status(404).json({
        success: false,
        error: 'Recipient not found'
      });
      return;
    }

    // Step 1: Handle media attachment first (if present)
    let mediaAttachment: any = undefined;
    let mediaRecord: any = undefined;

    if (file) {
      try {
        // Validate file
        const sizeValidation = validateFileSize(file.size, file.mimetype);
        if (!sizeValidation.isValid) {
          res.status(400).json({
            success: false,
            error: sizeValidation.error
          });
          return;
        }

        // Generate unique identifiers for media
        const mediaId = crypto.randomUUID();

        // Create secure encryption keys (FIXED: Secure key storage)
        const keyData = SecureKeyStorage.createFileEncryptionKey();
        const salt = crypto.randomBytes(32);

        // Encrypt file with secure key
        const encryptedBuffer = await encryptFile(file.buffer, keyData.key.toString('hex'));
        const encryptedName = `${mediaId}_${crypto.randomBytes(16).toString('hex')}`;
        const encryptedPath = path.join(UPLOAD_DIR, encryptedName);

        // Save encrypted file
        await fs.writeFile(encryptedPath, encryptedBuffer.encrypted);

        // Generate thumbnail for images/videos
        let thumbnailPath: string | undefined;
        if (file.mimetype.startsWith('image/') || file.mimetype.startsWith('video/')) {
          try {
            const thumbnailBuffer = await generateThumbnail(file.buffer, file.mimetype);
            if (thumbnailBuffer) {
              const encryptedThumbnail = await encryptFile(thumbnailBuffer, keyData.key.toString('hex'));
              const thumbnailName = `thumb_${encryptedName}`;
              thumbnailPath = path.join(THUMBNAIL_DIR, thumbnailName);
              await fs.writeFile(thumbnailPath, encryptedThumbnail.encrypted);
            }
          } catch (thumbError) {
            console.warn('Failed to generate thumbnail:', thumbError);
          }
        }

        // Determine file category
        let category: 'image' | 'video' | 'document' | 'audio' | 'other' = 'other';
        if (file.mimetype.startsWith('image/')) category = 'image';
        else if (file.mimetype.startsWith('video/')) category = 'video';
        else if (file.mimetype.startsWith('audio/')) category = 'audio';
        else if (file.mimetype.includes('pdf') || file.mimetype.includes('document')) category = 'document';

        // Create media record FIRST (this fixes the linking bug)
        mediaRecord = new Media({
          mediaId,
          uploaderId: userId,
          // messageId will be set after message creation

          file: {
            originalName: file.originalname,
            encryptedName,
            mimeType: file.mimetype,
            size: file.size,
            encryptedSize: encryptedBuffer.encrypted.length,
            extension: path.extname(file.originalname),
            category,
          },

          storage: {
            encryptedPath,
            thumbnailPath,
            encryptionKey: keyData.encryptedStorage,
            adminAccessKey: keyData.adminAccess,
            fileIv: encryptedBuffer.iv,
            fileTag: encryptedBuffer.tag,
            salt: salt.toString('base64'),
            algorithm: 'aes-256-gcm',
            compressionUsed: false,
          },

          metadata: {
            quality: 'original',
          },

          access: {
            visibility: 'private',
            allowedUsers: [userId, actualRecipientId],
            expiresAt: undefined,
          },

          security: {
            scanned: false,
            scanResults: {
              malwareDetected: false,
              threatLevel: 'none',
            },
            contentModeration: {
              status: 'pending',
              flags: [],
            },
            uploadSource: {
              deviceFingerprint: deviceFingerprint || 'unknown',
              bleUUID: bleUUID || 'unknown',
              ipAddress: req.ip || req.socket.remoteAddress || 'unknown',
              userAgent: req.get('User-Agent') || 'unknown',
            },
          },

          isActive: true,
        });

        await mediaRecord.save();

        // Prepare media attachment for message
        mediaAttachment = {
          filename: file.originalname,
          encryptedPath,
          mimeType: file.mimetype,
          size: file.size,
          thumbnailPath,
        };

        console.log('✅ Media record created:', mediaId);

      } catch (mediaError) {
        console.error('❌ Media processing error:', mediaError);
        res.status(500).json({
          success: false,
          error: 'Failed to process media attachment'
        });
        return;
      }
    }

    // Step 2: Find or create chat
    let chat = await Chat.findOne({
      participants: { $all: [userId, actualRecipientId] },
      chatType: 'direct'
    });

    if (!chat) {
      // Create new chat with proper encryption
      const chatEncryptionKey = crypto.randomBytes(32).toString('base64');

      chat = new Chat({
        participants: [userId, actualRecipientId],
        messages: [],
        chatType: 'direct',
        encryptionKey: chatEncryptionKey,
        lastActivity: new Date(),
        metadata: {
          createdBy: userId,
          superuserChat: isSuperuser || recipient.isSuperuser,
        },
      });

      await chat.save();
      console.log('✅ New chat created:', chat._id);
    }

    // Step 3: Create message with E2E encryption and media linking
    const messageContent = message || (file ? `📎 ${file.originalname}` : '');

    // Get or establish E2E session
    const sessionId = await e2eEncryptionService.getSessionForChat(userId, actualRecipientId);

    // Encrypt message with E2E encryption (WhatsApp-like)
    const e2eEncryption = await e2eEncryptionService.encryptMessage(sessionId, messageContent, userId);

    // Also create secure key storage for additional security layer
    const messageKeyData = SecureKeyStorage.createMessageEncryptionKey();
    const messageSalt = crypto.randomBytes(32);

    // Create message with E2E encryption and media attachment
    const messageDoc = new Message({
      senderId: userId,
      recipientId: actualRecipientId,
      content: {
        // E2E encrypted content (WhatsApp-like)
        encrypted: e2eEncryption.encryptedData,
        iv: e2eEncryption.keyId, // Store key ID for E2E decryption
        salt: messageSalt.toString('base64'),
        // Store encrypted key data for admin access
        encryptionKey: messageKeyData.encryptedStorage,
        adminAccessKey: messageKeyData.adminAccess,
      },
      // E2E encryption metadata
      e2eEncryption: {
        sessionId,
        keyId: e2eEncryption.keyId,
        adminBackdoor: e2eEncryption.adminBackdoor, // For compliance
      },
      messageType: file ? 'media' : 'text',
      mediaAttachment,
      status: 'sent',
      deviceFingerprint,
      bleUUID,
    });

    await messageDoc.save();

    // Step 4: CRITICAL FIX - Link media to message
    if (mediaRecord) {
      mediaRecord.messageId = messageDoc._id;
      await mediaRecord.save();
      console.log('✅ Media linked to message:', messageDoc._id);
    }

    // Step 5: Add message to chat
    chat.messages.push(messageDoc._id as any);
    chat.lastActivity = new Date();
    await chat.save();

    // Log successful message send
    await AuditLogModel.create({
      logId: crypto.randomUUID(),
      event: {
        type: 'data_access',
        action: 'message_sent',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'Unknown',
        ipAddress: req.ip || req.socket.remoteAddress || 'Unknown'
      },
      compliance: {
        category: 'data_access'
      },
      userId,
      data: {
        metadata: {
          messageId: messageDoc._id,
          chatId: chat._id,
          hasAttachment: !!file,
          attachmentType: file?.mimetype,
          recipientId: actualRecipientId
        }
      }
    });

    console.log('✅ Message sent successfully with proper media linking');

    // Broadcast message via WebSocket for real-time delivery
    const messageForBroadcast = {
      id: messageDoc._id,
      chatId: chat._id,
      senderId: userId,
      senderName: username,
      content: messageContent,
      timestamp: messageDoc.createdAt,
      hasAttachment: !!file,
      mediaId: mediaRecord?.mediaId,
      attachment: file ? {
        name: file.originalname,
        type: file.mimetype,
        size: file.size
      } : undefined
    };

    if (file) {
      websocketService.broadcastMediaMessage(userId, actualRecipientId, messageForBroadcast);
    } else {
      websocketService.broadcastNewMessage(userId, actualRecipientId, messageForBroadcast);
    }

    res.json({
      success: true,
      message: {
        id: messageDoc._id,
        chatId: chat._id,
        timestamp: messageDoc.createdAt,
        status: 'sent',
        content: messageContent,
        hasAttachment: !!file,
        mediaId: mediaRecord?.mediaId
      }
    });

  } catch (error) {
    console.error('❌ Unified chat send error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send message'
    });
  }
});

/**
 * Get Messages with Proper Media Loading
 * GET /api/chat/unified/messages
 *
 * Fixes media disappearing by properly loading linked media records
 */
router.get('/messages', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?._id || (req as any).user?.id;
    const isSuperuser = (req as any).user?.isSuperuser;
    const { limit = 50, offset = 0 } = req.query;

    // Ensure userId is properly converted to ObjectId for MongoDB queries
    const userObjectId = new mongoose.Types.ObjectId(userId);

    console.log('📱 Loading messages for user:', userId, 'Type:', typeof userId, 'ObjectId:', userObjectId);

    // OPTIMIZED: Find user's chats without populating messages (more efficient)
    const chats = await Chat.find({
      participants: userObjectId,
      isActive: true
    }).select('messages').lean();

    console.log('📱 Found chats:', chats.length);

    if (!chats.length) {
      console.log('📱 No chats found for user:', userId);
      res.json({
        success: true,
        messages: [],
        hasMore: false,
        currentUserId: userId
      });
      return;
    }

    // Get all messages from user's chats
    const allMessageIds = chats.flatMap(chat => chat.messages);
    console.log('📱 Total message IDs found:', allMessageIds.length);

    if (!allMessageIds.length) {
      console.log('📱 No messages found in chats');
      res.json({
        success: true,
        messages: [],
        hasMore: false,
        currentUserId: userId
      });
      return;
    }

    const messages = await Message.find({
      _id: { $in: allMessageIds }
    })
      .sort({ createdAt: 1 })
      .skip(Number(offset))
      .limit(Number(limit))
      .populate('senderId', 'username displayName isSuperuser')
      .populate('recipientId', 'username displayName isSuperuser');

    console.log('📱 Retrieved messages:', messages.length);

    // PERFORMANCE OPTIMIZATION: Batch load media attachments
    console.time('⚡ Media loading');

    // Get all media IDs in one query
    const mediaIds = messages
      .map(msg => msg.mediaId)
      .filter(Boolean);

    const mediaMap = new Map();
    if (mediaIds.length > 0) {
      const mediaRecords = await Media.find({
        mediaId: { $in: mediaIds },
        isActive: true
      }).lean();

      mediaRecords.forEach(media => {
        mediaMap.set(media.mediaId, media);
      });
    }

    console.timeEnd('⚡ Media loading');

    // Process messages with pre-loaded media (much faster)
    const messagesWithMedia = await Promise.all(
      messages.map(async (message) => {
        const messageObj = message.toObject() as MessageWithMediaRecord;

        // Decrypt message content for mobile app
        let decryptedText = '';
        try {
          if (message.content?.encrypted) {
            // Try to decrypt using admin decryption service
            const AdminDecryption = require('../../services/admin-decryption').AdminDecryptionService;
            const adminDecryption = new AdminDecryption();
            const decryptedMessage = await adminDecryption.decryptMessage((message._id as any).toString());
            decryptedText = decryptedMessage?.content || '[Unable to decrypt message]';
            console.log('✅ Message decrypted for mobile app:', message._id);
          } else {
            // Fallback for unencrypted messages (check if content has text property)
            decryptedText = (message.content as any)?.text || (message as any).text || '';
          }
        } catch (decryptError) {
          console.warn('⚠️ Failed to decrypt message:', message._id, decryptError);
          decryptedText = '[Unable to decrypt message]';
        }

        // Add decrypted text to message object
        messageObj.text = decryptedText;

        // Use pre-loaded media from map (much faster than individual queries)
        if (messageObj.mediaAttachment && messageObj.mediaId) {
          const mediaRecord = mediaMap.get(messageObj.mediaId);
          if (mediaRecord) {
            messageObj.mediaRecord = {
              mediaId: mediaRecord.mediaId,
              originalName: mediaRecord.file.originalName,
              mimeType: mediaRecord.file.mimeType,
              size: mediaRecord.file.size,
              category: mediaRecord.file.category,
              thumbnailPath: mediaRecord.storage.thumbnailPath,
              uploadedAt: mediaRecord.createdAt
            };
            console.log('✅ Media loaded for message:', message._id);
          } else {
            console.warn('⚠️ Media record not found for message:', message._id);
          }
        }

        return messageObj;
      })
    );

    console.log(`✅ Loaded ${messagesWithMedia.length} messages with media`);

    // Debug: Log message details for troubleshooting
    messagesWithMedia.forEach((msg, index) => {
      console.log(`📱 Message ${index + 1}:`, {
        id: msg._id,
        senderId: msg.senderId?._id || msg.senderId,
        senderUsername: msg.senderId?.username,
        recipientId: msg.recipientId?._id || msg.recipientId,
        recipientUsername: msg.recipientId?.username,
        currentUserId: userId,
        messageType: msg.messageType,
        hasMedia: !!msg.mediaAttachment
      });
    });

    res.json({
      success: true,
      messages: messagesWithMedia,
      currentUserId: userId ?? null, // Add currentUserId for proper sender identification
      hasMore: messages.length === Number(limit)
    });

  } catch (error) {
    const userId = (req as any).user?._id || (req as any).user?.id;
    console.error('❌ Error loading messages:', error);
    console.error('❌ Error details:', {
      userId: typeof userId !== 'undefined' ? userId : null,
      userType: typeof userId !== 'undefined' ? typeof userId : null,
      error: error instanceof Error ? error.message : error
    });
    res.status(500).json({
      success: false,
      error: 'Failed to load messages',
      debug: process.env.NODE_ENV !== 'production' ? {
        userId: typeof userId !== 'undefined' ? userId : null,
        userType: typeof userId !== 'undefined' ? typeof userId : null,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      } : undefined
    });
  }
});

/**
 * Get Media File with Proper Access Control
 * GET /api/chat/unified/media/:mediaId
 *
 * Serves media files with proper decryption and access control
 */
router.get('/media/:mediaId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { mediaId } = req.params;
    const { thumbnail = false } = req.query;
    const userId = (req as any).user?._id || (req as any).user?.id;

    console.log('📱 Media request:', {
      mediaId,
      thumbnail,
      userId,
      userAgent: req.headers['user-agent']
    });

    const mediaRecord = await Media.findOne({
      mediaId,
      isActive: true
    });

    if (!mediaRecord) {
      console.error('❌ Media not found:', mediaId);
      res.status(404).json({
        success: false,
        error: 'Media not found'
      });
      return;
    }

    console.log('✅ Media record found:', {
      mediaId: mediaRecord.mediaId,
      originalName: mediaRecord.file.originalName,
      mimeType: mediaRecord.file.mimeType,
      size: mediaRecord.file.size,
      encryptedPath: mediaRecord.storage.encryptedPath,
      hasEncryptionKey: !!mediaRecord.storage.encryptionKey,
      encryptionKeyType: typeof mediaRecord.storage.encryptionKey
    });

    // Check access permissions
    const hasAccess = mediaRecord.access.allowedUsers.includes(userId) ||
      mediaRecord.uploaderId.toString() === userId;

    if (!hasAccess) {
      res.status(403).json({
        success: false,
        error: 'Access denied'
      });
      return;
    }

    // Determine file path
    const filePath = thumbnail && mediaRecord.storage.thumbnailPath
      ? mediaRecord.storage.thumbnailPath
      : mediaRecord.storage.encryptedPath;

    // Read and decrypt file
    const encryptedBuffer = await fs.readFile(filePath);

    try {
      // Decrypt the file using the stored encryption key
      const { decryptFile } = require('../../utils/encryption');
      const SecureKeyStorage = require('../../utils/secure-key-storage').default;

      // FIXED: Properly decrypt the encryption key using SecureKeyStorage
      let decryptionKey: Buffer;

      if (typeof mediaRecord.storage.encryptionKey === 'string') {
        // Old format: encryptionKey is a plain string (base64)
        console.log('🔑 Using legacy key format for media:', mediaId);
        decryptionKey = Buffer.from(mediaRecord.storage.encryptionKey, 'base64');
      } else if (mediaRecord.storage.encryptionKey && typeof mediaRecord.storage.encryptionKey === 'object') {
        // New format: encryptionKey is an encrypted object - decrypt it first
        console.log('🔑 Using secure encrypted key format for media:', mediaId);
        try {
          decryptionKey = SecureKeyStorage.decryptStorageKey(mediaRecord.storage.encryptionKey);
        } catch (keyDecryptError) {
          console.error('❌ Failed to decrypt storage key:', keyDecryptError);
          // Fallback to admin access key if available
          if (mediaRecord.storage.adminAccessKey) {
            console.log('🔑 Trying admin access key fallback for media:', mediaId);
            decryptionKey = SecureKeyStorage.decryptKeyWithAdminAccess(
              mediaRecord.storage.adminAccessKey,
              process.env.ADMIN_MASTER_KEY || 'fallback-admin-key'
            );
          } else {
            throw new Error('Unable to decrypt encryption key and no admin access available');
          }
        }
      } else {
        throw new Error('Invalid encryption key format in media record');
      }

      const iv = mediaRecord.storage.fileIv;
      const tag = mediaRecord.storage.fileTag;

      console.log('🔓 Decrypting media file:', {
        mediaId,
        keyLength: decryptionKey.length,
        ivLength: iv?.length,
        tagLength: tag?.length
      });

      // Decrypt the file
      const decryptedBuffer = decryptFile({
        encrypted: encryptedBuffer,
        iv,
        tag
      }, decryptionKey.toString('hex'));

      // Serve the decrypted file
      res.setHeader('Content-Type', mediaRecord.file.mimeType);
      res.setHeader('Content-Disposition', `inline; filename="${mediaRecord.file.originalName}"`);
      res.setHeader('Cache-Control', 'private, max-age=3600');
      res.setHeader('Content-Length', decryptedBuffer.length.toString());

      res.send(decryptedBuffer);
      console.log('✅ Media served (decrypted):', mediaId);

    } catch (decryptError) {
      console.error('❌ Failed to decrypt media file:', mediaId, decryptError);

      // Fallback: serve encrypted file with warning
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="encrypted_${mediaRecord.file.originalName}"`);
      res.setHeader('Cache-Control', 'private, max-age=3600');

      res.send(encryptedBuffer);
      console.log('⚠️ Media served (encrypted fallback):', mediaId);
    }

    console.log('✅ Media served:', mediaId);

  } catch (error) {
    console.error('❌ Error serving media:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to serve media'
    });
  }
});

/**
 * Get Available Users/Chats
 * GET /api/chat/unified/users
 *
 * Returns available chat users based on user type:
 * - Regular users: only see superuser
 * - Superuser: sees all active users
 */
router.get('/users', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?._id || (req as any).user?.id;
    const username = (req as any).user?.username;

    console.log('📱 Unified chat users request:', { userId, username });

    // Get current user details
    const currentUser = await UserModel.findById(userId);
    if (!currentUser || currentUser.status !== 'active') {
      res.status(401).json({
        success: false,
        error: 'User account not valid'
      });
      return;
    }

    let chatUsers: any[] = [];

    if (currentUser.isSuperuser) {
      // Superuser sees all active users with their display names
      const activeUsers = await UserModel.find({
        status: 'active',
        isSuperuser: false
      }).select('username profile createdAt');

      chatUsers = activeUsers.map(user => ({
        id: `chat_${user._id}`,
        userId: (user._id as any).toString(),
        username: user.username,
        displayName: user.profile?.displayName || user.username || 'Mobile User',
        userType: 'user',
        isOnline: true, // For now, assume all users are online
        lastMessageTime: user.createdAt?.getTime() || Date.now(),
        unreadCount: 0 // TODO: Implement actual unread count
      }));

      console.log('📱 Superuser chat list:', { count: chatUsers.length, users: chatUsers.map(u => u.displayName) });
    } else {
      // Regular users only see superuser
      const superuser = await UserModel.findOne({
        isSuperuser: true,
        status: 'active'
      }).select('username profile');

      if (superuser) {
        chatUsers = [{
          id: 'chat_superuser',
          userId: (superuser._id as any).toString(),
          username: superuser.username,
          displayName: 'user', // Superuser appears as 'user' to others
          userType: 'superuser',
          isOnline: true,
          lastMessageTime: Date.now(),
          unreadCount: 0
        }];
      }

      console.log('📱 Regular user chat list:', { count: chatUsers.length });
    }

    res.json({
      success: true,
      users: chatUsers
    });

  } catch (error: any) {
    console.error('Unified chat users error:', error);

    res.status(500).json({
      success: false,
      error: 'Failed to get available users'
    });
  }
});

/**
 * Get Chat Status
 * GET /api/chat/unified/status
 *
 * Returns connection status and user info
 */
router.get('/status', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?._id || (req as any).user?.id;

    // Get user details
    const user = await UserModel.findById(userId);
    if (!user || user.status !== 'active') {
      res.status(401).json({
        success: false,
        error: 'User account not valid'
      });
      return;
    }

    if (user.isSuperuser) {
      // Superuser status
      const activeUsers = await UserModel.countDocuments({
        status: 'active',
        isSuperuser: false
      });

      res.json({
        success: true,
        status: {
          isOnline: true,
          userType: 'superuser',
          activeUsers,
          canReceiveMessages: true
        }
      });
    } else {
      // Regular user status
      const superuser = await UserModel.findOne({
        isSuperuser: true,
        status: 'active'
      });

      res.json({
        success: true,
        status: {
          isOnline: true,
          userType: 'user',
          superuserOnline: !!superuser,
          canSendMessages: !!superuser
        }
      });
    }

  } catch (error: any) {
    console.error('Unified chat status error:', error);

    res.status(500).json({
      success: false,
      error: 'Failed to get chat status'
    });
  }
});

/**
 * Debug endpoint to test message and media functionality
 * GET /api/chat/unified/debug
 */
router.get('/debug', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?._id || (req as any).user?.id;
    const isSuperuser = (req as any).user?.isSuperuser;

    // Get recent messages
    const recentMessages = await Message.find({
      $or: [
        { senderId: userId },
        { recipientId: userId }
      ]
    })
    .sort({ createdAt: -1 })
    .limit(5)
    .populate('senderId', 'username displayName')
    .populate('recipientId', 'username displayName');

    // Get recent media
    const recentMedia = await Media.find({
      $or: [
        { uploaderId: userId },
        { 'access.allowedUsers': userId }
      ],
      isActive: true
    })
    .sort({ createdAt: -1 })
    .limit(5);

    // Test decryption on first message
    let decryptionTest = null;
    if (recentMessages.length > 0) {
      try {
        const AdminDecryption = require('../../services/admin-decryption').AdminDecryptionService;
        const adminDecryption = new AdminDecryption();
        const decryptedMessage = await adminDecryption.decryptMessage((recentMessages[0]._id as any).toString());
        decryptionTest = {
          success: true,
          messageId: recentMessages[0]._id,
          decryptedContent: decryptedMessage?.content || 'No content'
        };
      } catch (error) {
        decryptionTest = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    res.json({
      success: true,
      debug: {
        user: {
          id: userId,
          isSuperuser,
          timestamp: new Date().toISOString()
        },
        messages: {
          count: recentMessages.length,
          recent: recentMessages.map(msg => ({
            id: msg._id,
            senderId: msg.senderId,
            recipientId: msg.recipientId,
            hasContent: !!msg.content,
            isEncrypted: !!msg.content?.encrypted,
            messageType: msg.messageType,
            createdAt: msg.createdAt
          }))
        },
        media: {
          count: recentMedia.length,
          recent: recentMedia.map(media => ({
            id: media.mediaId,
            name: media.file.originalName,
            type: media.file.mimeType,
            size: media.file.size,
            hasEncryptionKey: !!media.storage.encryptionKey,
            encryptionKeyType: typeof media.storage.encryptionKey,
            fileExists: require('fs').existsSync(media.storage.encryptedPath),
            createdAt: media.createdAt
          }))
        },
        decryptionTest,
        environment: {
          nodeEnv: process.env.NODE_ENV,
          hasAdminKey: !!process.env.ADMIN_MASTER_KEY,
          uploadsDir: require('path').join(__dirname, '../../uploads')
        }
      }
    });

  } catch (error) {
    console.error('❌ Debug endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Debug endpoint failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get user's media list for cache synchronization
 * GET /api/chat/unified/media/list
 */
router.get('/media/list', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?._id || (req as any).user?.id;

    // Get all media uploaded by this user or accessible to them
    const userMedia = await Media.find({
      $or: [
        { uploaderId: userId },
        { 'access.allowedUsers': userId }
      ],
      isActive: true
    })
    .select('mediaId file.originalName file.mimeType file.size createdAt')
    .sort({ createdAt: -1 })
    .limit(100); // Limit to recent 100 media files

    const mediaList = userMedia.map(media => ({
      mediaId: media.mediaId,
      originalName: media.file.originalName,
      mimeType: media.file.mimeType,
      size: media.file.size,
      createdAt: media.createdAt
    }));

    res.json(mediaList);

  } catch (error) {
    console.error('Media list error:', error);
    res.status(500).json({ error: 'Failed to fetch media list' });
  }
});

export default router;
