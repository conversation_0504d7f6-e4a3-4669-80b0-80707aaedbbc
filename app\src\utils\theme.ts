/**
 * CCALC iOS Theme System
 * Modern minimalistic design following iOS 17+ principles
 */

import { Dimensions, Platform } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// iOS Design Tokens
export const theme = {
  // Colors - iOS Semantic Colors
  colors: {
    // System Colors
    systemBlue: '#007AFF',
    systemIndigo: '#5856D6',
    systemPurple: '#AF52DE',
    systemTeal: '#5AC8FA',
    systemGreen: '#34C759',
    systemYellow: '#FFCC00',
    systemOrange: '#FF9500',
    systemRed: '#FF3B30',
    systemPink: '#FF2D92',
    
    // Grayscale
    systemGray: '#8E8E93',
    systemGray2: '#AEAEB2',
    systemGray3: '#C7C7CC',
    systemGray4: '#D1D1D6',
    systemGray5: '#E5E5EA',
    systemGray6: '#F2F2F7',
    
    // Dynamic Colors (Light/Dark)
    primary: '#007AFF',
    background: '#FFFFFF',
    secondaryBackground: '#F2F2F7',
    tertiaryBackground: '#FFFFFF',
    
    // Text Colors
    label: '#000000',
    secondaryLabel: '#3C3C43',
    tertiaryLabel: '#3C3C43',
    placeholderText: '#3C3C43',
    text: '#000000',
    textSecondary: '#3C3C43',
    
    // Calculator Specific
    calculatorBackground: '#000000',
    calculatorButton: '#333333',
    calculatorButtonHighlight: '#505050',
    calculatorOperator: '#FF9500',
    calculatorDisplay: '#FFFFFF',
    
    // Chat Colors
    messageBackground: '#007AFF',
    incomingMessage: '#E5E5EA',
    messageBorder: 'transparent',
    border: '#E5E5EA',
    surface: '#F2F2F7',
    error: '#FF3B30',
    
    // Glassmorphism
    glass: 'rgba(255, 255, 255, 0.1)',
    glassStrong: 'rgba(255, 255, 255, 0.2)',
  },
  
  // Typography - SF Pro Font Family
  typography: {
    // Size Scale
    sizes: {
      caption2: 11,
      caption: 12,
      footnote: 13,
      subhead: 15,
      callout: 16,
      body: 17,
      headline: 17,
      title3: 20,
      title2: 22,
      title1: 28,
      largeTitle: 34,
      
      // Calculator specific
      calculatorDisplay: 64,
      calculatorButton: 32,
    },
    
    // Font Weights
    weights: {
      ultraLight: '100',
      thin: '200',
      light: '300',
      regular: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      heavy: '800',
      black: '900',
    } as const,
    
    // Font Families
    families: {
      system: Platform.select({
        ios: 'SF Pro Display',
        android: 'Roboto',
      }),
      systemText: Platform.select({
        ios: 'SF Pro Text',
        android: 'Roboto',
      }),
      monospace: Platform.select({
        ios: 'SF Mono',
        android: 'monospace',
      }),
    },
  },
  
  // Spacing System (Based on 8pt grid)
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
    
    // Semantic spacing
    screen: 20,
    card: 16,
    section: 24,
  },
  
  // Border Radius
  borderRadius: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 24,
    circle: 999,
    
    // iOS specific
    button: 8,
    card: 12,
    modal: 16,
  },
  
  // Shadows (iOS-style)
  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 16,
      elevation: 8,
    },
    
    // Calculator button shadow
    button: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 2,
    },
  },
  
  // Layout
  layout: {
    screenWidth,
    screenHeight,
    headerHeight: 44,
    tabBarHeight: 83,
    statusBarHeight: Platform.select({ ios: 20, android: 24 }),
    
    // Calculator grid
    calculatorButtonSize: (screenWidth - 5 * 16) / 4, // 4 columns with spacing
    calculatorSpacing: 16,
  },
  
  // Animation Durations
  animation: {
    fast: 150,
    normal: 300,
    slow: 500,
    
    // iOS specific timings
    spring: {
      damping: 0.8,
      stiffness: 100,
    },
  },
  
  // Haptic Feedback Types
  haptics: {
    light: 'impactLight',
    medium: 'impactMedium',
    heavy: 'impactHeavy',
    selection: 'selection',
    success: 'notificationSuccess',
    warning: 'notificationWarning',
    error: 'notificationError',
  } as const,
};

// Dark theme overrides
export const darkTheme = {
  ...theme,
  colors: {
    ...theme.colors,
    background: '#000000',
    secondaryBackground: '#1C1C1E',
    tertiaryBackground: '#2C2C2E',
    
    label: '#FFFFFF',
    secondaryLabel: '#EBEBF5',
    tertiaryLabel: '#EBEBF5',
    placeholderText: '#EBEBF5',
    text: '#FFFFFF',
    textSecondary: '#EBEBF5',
    
    systemGray: '#8E8E93',
    systemGray2: '#636366',
    systemGray3: '#48484A',
    systemGray4: '#3A3A3C',
    systemGray5: '#2C2C2E',
    systemGray6: '#1C1C1E',
    
    incomingMessage: '#3A3A3C',
    border: '#3A3A3C',
    surface: '#2C2C2E',
    error: '#FF453A',
    glass: 'rgba(0, 0, 0, 0.1)',
    glassStrong: 'rgba(0, 0, 0, 0.2)',
  },
};

// Theme context type
export type Theme = typeof theme;
export type ColorName = keyof typeof theme.colors;
export type SpacingSize = keyof typeof theme.spacing;
export type FontSize = keyof typeof theme.typography.sizes;
export type FontWeight = keyof typeof theme.typography.weights;

export default theme;
