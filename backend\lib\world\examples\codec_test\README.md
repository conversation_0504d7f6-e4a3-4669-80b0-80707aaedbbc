## Parameter I/O examples including the coded aperiodicity.
You can save three speech parameters (F0, spectral envelope, and aperiodicity) and load them from the files.

- f0analysis.cpp: F0 estimation.
- spanalysis.cpp: spectral envelope estimation.
- apanalysis.cpp: aperiodicity estimation. It can code and write the estimated result.
- readandsynthesis.cpp: load three parameters and generate a waveform from them.

All examples have -h option to display how to set options (e.g. "f0analysis.exe -h")
