/**
 * Test script to check all voice profiles
 * Tests each profile to verify it's working correctly after our fixes
 */

const fs = require('fs');
const path = require('path');
const { worldVocoderService, WORLD_VOICE_PROFILES } = require('./services/worldVocoderService');

// Path to test input file
const TEST_INPUT_FILE = path.join(__dirname, '..', 'test-input.wav');
const OUTPUT_DIR = path.join(__dirname, '..', 'test-neutralization-output');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Test all profiles
async function testAllProfiles() {
  console.log('🔬 Testing all voice profiles');
  console.log('📂 Input file:', TEST_INPUT_FILE);
  console.log('📂 Output directory:', OUTPUT_DIR);
  
  const inputBuffer = fs.readFileSync(TEST_INPUT_FILE);
  
  // Array to track results
  const results = [];
  
  // Test each profile
  for (const [profileName, profile] of Object.entries(WORLD_VOICE_PROFILES)) {
    console.log(`\n🧪 Testing profile: ${profileName}`);
    console.log('🔧 Profile parameters:', JSON.stringify(profile, null, 2));
    
    const outputFile = path.join(OUTPUT_DIR, `output-${profileName}.wav`);
    
    try {
      console.time(`⏱️ ${profileName} processing time`);
      
      // Process the audio with this profile
      const outputBuffer = await worldVocoderService.processAudioWithProfile(inputBuffer, profile);
      
      console.timeEnd(`⏱️ ${profileName} processing time`);
      
      // Save the output file
      fs.writeFileSync(outputFile, outputBuffer);
      console.log(`✅ Saved output to: ${outputFile}`);
      console.log(`📊 Output size: ${outputBuffer.length} bytes`);
      
      // Calculate WAV header size
      const headerSize = 44;  // Standard WAV header size
      const pcmDataSize = outputBuffer.length - headerSize;
      const pcmSampleCount = pcmDataSize / 2;  // 16-bit PCM
      const durationMs = (pcmSampleCount / 48000) * 1000;  // Assuming 48kHz
      
      console.log(`📊 PCM data size: ${pcmDataSize} bytes`);
      console.log(`📊 Duration: ${durationMs.toFixed(2)}ms`);
      
      // Record result
      results.push({
        profile: profileName,
        status: 'success',
        outputSize: outputBuffer.length,
        duration: durationMs
      });
    } catch (error) {
      console.error(`❌ Error processing ${profileName}:`, error);
      results.push({
        profile: profileName,
        status: 'error',
        error: error.message
      });
    }
  }
  
  // Print summary
  console.log('\n📋 Test Results Summary:');
  console.table(results);
}

// Run the tests
testAllProfiles()
  .then(() => {
    console.log('✅ All tests completed');
  })
  .catch(error => {
    console.error('❌ Test error:', error);
  });
