/**
 * Server-side API client for Next.js API routes
 * This client is specifically designed for server-side requests from API routes to the backend
 */
import getBackendUrl from './getBackendUrl';

interface ServerApiClientOptions {
  timeout?: number;
  headers?: Record<string, string>;
}

class ServerApiClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor(options: ServerApiClientOptions = {}) {
    this.baseURL = getBackendUrl();
    this.timeout = options.timeout || 60000; // Increased to 60 seconds for voice processing
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    console.log('ServerApiClient initialized with baseURL:', this.baseURL);
  }
  private async makeRequest(endpoint: string, options: RequestInit) {
    const requestUrl = this.baseURL + endpoint;

    // Merge headers properly
    const requestHeaders = new Headers(this.defaultHeaders);

    // Add any additional headers from options
    if (options.headers) {
      if (options.headers instanceof Headers) {
        options.headers.forEach((value, key) => {
          requestHeaders.set(key, value);
        });
      } else {
        Object.entries(options.headers).forEach(([key, value]) => {
          requestHeaders.set(key, String(value));
        });
      }
    }

    try {
      console.log(`Making ${options.method || 'GET'} request to ${endpoint}`);

      // Log non-auth headers for debugging
      const logHeaders: string[] = [];
      requestHeaders.forEach((value, key) => {
        if (!key.toLowerCase().includes('auth')) {
          logHeaders.push(`${key}: ${value}`);
        } else {
          logHeaders.push(`${key}: [REDACTED]`);
        }
      });
      console.log('Request headers:', logHeaders.join(', '));

      const response = await fetch(requestUrl, {
        ...options,
        headers: requestHeaders,
        credentials: 'include',
      });

      console.log(`Response from ${endpoint}: ${response.status} ${response.statusText}`);
      return response;

    } catch (error) {
      console.error(`Request to ${endpoint} failed:`, error);
      throw error;
    }
  }
  async post(endpoint: string, data: any, options: RequestInit = {}): Promise<Response> {
    return this.makeRequest(endpoint, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
  async get(endpoint: string, options: RequestInit = {}): Promise<Response> {
    return this.makeRequest(endpoint, {
      ...options,
      method: 'GET'
    });
  }

  async put(endpoint: string, data: any, options: RequestInit = {}): Promise<Response> {
    return this.makeRequest(endpoint, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async delete(endpoint: string, options: RequestInit = {}): Promise<Response> {
    return this.makeRequest(endpoint, {
      ...options,
      method: 'DELETE'
    });
  }

  async patch(endpoint: string, data: any, options: RequestInit = {}): Promise<Response> {
    return this.makeRequest(endpoint, {
      ...options,
      method: 'PATCH',
      body: JSON.stringify(data)
    });
  }
}

export default ServerApiClient;
