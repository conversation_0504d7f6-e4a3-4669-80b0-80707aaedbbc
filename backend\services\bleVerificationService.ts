import mongoose from 'mongoose';
import BleDevice from '../models/BleDevice';
import User from '../models/User';
import { createHash } from 'crypto';

/**
 * BLE Device Verification Service for Voice Calls
 * Handles BLE device authentication and authorization for voice call initiation
 */

export interface BleVerificationResult {
  verified: boolean;
  deviceId?: string;
  deviceName?: string;
  error?: string;
  requiresBLE?: boolean;
  userRole?: 'superuser' | 'regular';
}

export interface BleChallenge {
  challenge: string;
  deviceId: string;
  expiresAt: Date;
}

class BleVerificationService {
  /**
   * Verify if user can initiate voice call based on BLE device requirements
   * Regular users MUST have verified BLE device
   * Superuser can make calls without BLE but still requires voice modulation
   */
  async verifyVoiceCallAuthorization(
    userId: string, 
    deviceChallenge?: string,
    deviceId?: string
  ): Promise<BleVerificationResult> {
    try {
      // Get user information
      const user = await User.findById(userId);
      if (!user) {
        return {
          verified: false,
          error: 'User not found'
        };
      }

      const isSuperuser = user.isSuperuser || false;

      // Superuser can make calls without BLE device verification
      if (isSuperuser) {
        return {
          verified: true,
          userRole: 'superuser',
          deviceId: 'superuser-bypass',
          deviceName: 'Superuser (No BLE Required)'
        };
      }

      // Regular users MUST have BLE device verification
      if (!deviceChallenge || !deviceId) {
        return {
          verified: false,
          error: 'BLE device verification required for voice calls',
          requiresBLE: true,
          userRole: 'regular'
        };
      }

      // Verify BLE device challenge
      const verificationResult = await this.verifyBleChallenge(userId, deviceId, deviceChallenge);
      
      return {
        ...verificationResult,
        userRole: 'regular'
      };

    } catch (error) {
      console.error('Error in voice call BLE verification:', error);
      return {
        verified: false,
        error: 'BLE verification service error'
      };
    }
  }

  /**
   * Verify BLE device challenge for authentication
   */
  async verifyBleChallenge(
    userId: string, 
    deviceId: string, 
    challengeResponse: string
  ): Promise<BleVerificationResult> {
    try {
      // Find the BLE device
      const bleDevice = await BleDevice.findOne({
        _id: deviceId,
        userId: userId,
        'status.isActive': true,
        'voiceCallAuth.enabled': true
      });

      if (!bleDevice) {
        return {
          verified: false,
          error: 'BLE device not found or not authorized for voice calls'
        };
      }

      // Check if device has valid challenge
      if (!bleDevice.security.authChallenge || !bleDevice.security.challengeExpiry) {
        return {
          verified: false,
          error: 'No active authentication challenge for this device'
        };
      }

      // Check if challenge has expired
      if (new Date() > bleDevice.security.challengeExpiry) {
        return {
          verified: false,
          error: 'Authentication challenge has expired'
        };
      }

      // Verify challenge response
      const expectedResponse = createHash('sha256')
        .update(`${bleDevice.security.authChallenge}-${bleDevice.deviceId}`)
        .digest('hex');

      if (challengeResponse !== expectedResponse) {
        // Record authentication failure
        bleDevice.voiceCallAuth.authFailures += 1;
        bleDevice.voiceCallAuth.lastAuthFailure = new Date();
        
        // Add security violation
        bleDevice.addSecurityViolation(
          'auth_failure',
          'Invalid BLE challenge response for voice call',
          'medium'
        );
        
        await bleDevice.save();

        return {
          verified: false,
          error: 'Invalid authentication challenge response'
        };
      }

      // Success - record usage and update device
      bleDevice.recordVoiceCallUsage(true);
      bleDevice.updateConnectionStatus();
      
      // Clear the used challenge
      bleDevice.security.authChallenge = undefined;
      bleDevice.security.challengeExpiry = undefined;
      
      await bleDevice.save();

      return {
        verified: true,
        deviceId: (bleDevice._id as mongoose.Types.ObjectId).toString(),
        deviceName: bleDevice.deviceName
      };

    } catch (error) {
      console.error('Error verifying BLE challenge:', error);
      return {
        verified: false,
        error: 'Challenge verification failed'
      };
    }
  }

  /**
   * Generate new authentication challenge for BLE device
   */
  async generateBleChallenge(userId: string, deviceId: string): Promise<BleChallenge | null> {
    try {
      const bleDevice = await BleDevice.findOne({
        _id: deviceId,
        userId: userId,
        'status.isActive': true,
        'voiceCallAuth.enabled': true
      });

      if (!bleDevice) {
        throw new Error('BLE device not found or not authorized');
      }

      // Generate new challenge
      const challenge = createHash('sha256')
        .update(`${bleDevice.deviceId}-${Date.now()}-${Math.random()}`)
        .digest('hex');

      const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

      // Store challenge in device
      bleDevice.security.authChallenge = challenge;
      bleDevice.security.challengeExpiry = expiresAt;
      await bleDevice.save();

      return {
        challenge,
        deviceId: (bleDevice._id as mongoose.Types.ObjectId).toString(),
        expiresAt
      };

    } catch (error) {
      console.error('Error generating BLE challenge:', error);
      return null;
    }
  }

  /**
   * Get user's available BLE devices for voice calls
   */
  async getUserBleDevicesForVoiceCalls(userId: string): Promise<any[]> {
    try {
      const devices = await BleDevice.find({
        userId: userId,
        'status.isActive': true,
        'voiceCallAuth.enabled': true
      }).select('deviceId deviceName deviceType status.lastSeen voiceCallAuth.callCount');

      return devices.map(device => ({
        id: (device._id as mongoose.Types.ObjectId).toString(),
        deviceId: device.deviceId,
        name: device.deviceName,
        type: device.deviceType,
        lastSeen: device.status.lastSeen,
        callCount: device.voiceCallAuth.callCount
      }));

    } catch (error) {
      console.error('Error fetching user BLE devices:', error);
      return [];
    }
  }

  /**
   * Check if user needs BLE verification for voice calls
   */
  async requiresBleVerification(userId: string): Promise<boolean> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return true; // Default to requiring BLE if user not found
      }

      // Superuser doesn't require BLE verification
      return !user.isSuperuser;

    } catch (error) {
      console.error('Error checking BLE requirement:', error);
      return true; // Default to requiring BLE on error
    }
  }

  /**
   * Record voice call attempt for analytics
   */
  async recordVoiceCallAttempt(
    userId: string, 
    deviceId?: string, 
    success: boolean = true,
    failureReason?: string
  ): Promise<void> {
    try {
      if (deviceId && deviceId !== 'superuser-bypass') {
        const bleDevice = await BleDevice.findById(deviceId);
        if (bleDevice) {
          bleDevice.recordVoiceCallUsage(success);
          
          if (!success && failureReason) {
            bleDevice.addSecurityViolation(
              'connection_anomaly',
              `Voice call failed: ${failureReason}`,
              'low'
            );
          }
          
          await bleDevice.save();
        }
      }

      // Could also log to audit system here
      console.log(`Voice call attempt: User ${userId}, Device ${deviceId}, Success: ${success}`);

    } catch (error) {
      console.error('Error recording voice call attempt:', error);
    }
  }
}

export default new BleVerificationService();
