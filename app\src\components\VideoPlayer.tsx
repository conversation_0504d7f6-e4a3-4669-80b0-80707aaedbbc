/**
 * Enhanced Video Player Modal Component
 * Full-screen video player with modern controls and optimized performance
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Modal,
  TouchableOpacity,
  Text,
  StyleSheet,
  Dimensions,
  StatusBar,
  SafeAreaView,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { Video, ResizeMode, AVPlaybackStatus } from 'expo-av';
import { MediaAttachment } from '../services/MediaService';
import { MediaCacheManager } from '../services/MediaCacheManager';
import { theme } from '../utils/theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface VideoPlayerProps {
  visible: boolean;
  videoAttachment: MediaAttachment | null;
  onClose: () => void;
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  visible,
  videoAttachment,
  onClose,
}) => {
  const [status, setStatus] = useState<AVPlaybackStatus>({} as AVPlaybackStatus);
  const [showControls, setShowControls] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [resolvedVideoUri, setResolvedVideoUri] = useState<string | null>(null);
  const videoRef = useRef<any>(null);
  const controlsOpacity = useRef(new Animated.Value(1)).current;
  const hideControlsTimeout = useRef<any>(null);
  const mediaCacheManager = MediaCacheManager.getInstance();

  // Auto-hide controls after 3 seconds
  const scheduleHideControls = () => {
    if (hideControlsTimeout.current) {
      clearTimeout(hideControlsTimeout.current);
    }
    hideControlsTimeout.current = setTimeout(() => {
      hideControls();
    }, 3000);
  };

  const showControlsWithAnimation = () => {
    setShowControls(true);
    Animated.timing(controlsOpacity, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
    scheduleHideControls();
  };

  const hideControls = () => {
    Animated.timing(controlsOpacity, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setShowControls(false);
    });
  };

  const handlePlayPause = async () => {
    try {
      if (videoRef.current) {
        const currentStatus = status as any;
        if (currentStatus.isPlaying) {
          await videoRef.current.pauseAsync();
        } else {
          await videoRef.current.playAsync();
        }
      }
    } catch (err) {
      console.error('❌ Video play/pause error:', err);
      setError('Playback error occurred');
    }
  };

  const handleVideoPress = () => {
    if (showControls) {
      hideControls();
    } else {
      showControlsWithAnimation();
    }
  };

  const handleStatusUpdate = (newStatus: AVPlaybackStatus) => {
    setStatus(newStatus);

    if (newStatus.isLoaded) {
      setIsLoading(false);
      setError(null);
    } else if ('error' in newStatus) {
      setIsLoading(false);
      setError('Failed to load video');
    }
  };

  // Resolve video URI when modal opens - CRITICAL FIX for video persistence
  useEffect(() => {
    const resolveVideoUri = async () => {
      if (visible && videoAttachment) {
        setIsLoading(true);
        setError(null);
        showControlsWithAnimation();

        try {
          let videoUri = videoAttachment.uri;

          // If we have a cached media ID, try to get the best available URI
          if (videoAttachment.id) {
            console.log('🎥 Resolving video URI for:', videoAttachment.id);
            const cachedUri = await mediaCacheManager.getDisplayUri(videoAttachment.id);
            if (cachedUri) {
              videoUri = cachedUri;
              console.log('✅ Using cached video URI:', cachedUri.substring(0, 50) + '...');
            } else {
              console.warn('⚠️ No cached URI found, using original:', videoUri.substring(0, 50) + '...');
            }
          }

          setResolvedVideoUri(videoUri);
        } catch (error) {
          console.error('❌ Failed to resolve video URI:', error);
          setResolvedVideoUri(videoAttachment.uri);
          setError('Failed to load video');
        }
      } else {
        setResolvedVideoUri(null);
        setShowControls(true);
        setIsLoading(true);
        setError(null);
        if (hideControlsTimeout.current) {
          clearTimeout(hideControlsTimeout.current);
        }
      }
    };

    resolveVideoUri();
  }, [visible, videoAttachment]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hideControlsTimeout.current) {
        clearTimeout(hideControlsTimeout.current);
      }
    };
  }, []);

  const formatTime = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!videoAttachment) return null;

  return (
    <Modal
      visible={visible}
      animationType="fade"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <StatusBar hidden />
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.videoContainer}
          onPress={handleVideoPress}
          activeOpacity={1}
        >
          <Video
            ref={videoRef}
            source={{ uri: resolvedVideoUri || '' }}
            style={styles.video}
            resizeMode={ResizeMode.CONTAIN}
            shouldPlay={false}
            isLooping={false}
            onPlaybackStatusUpdate={handleStatusUpdate}
            useNativeControls={false}
          />

          {/* Loading Indicator */}
          {(isLoading || !resolvedVideoUri) && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#FFFFFF" />
              <Text style={styles.loadingText}>
                {!resolvedVideoUri ? 'Resolving video...' : 'Loading video...'}
              </Text>
            </View>
          )}

          {/* Error State */}
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorIcon}>⚠️</Text>
              <Text style={styles.errorText}>{error}</Text>
              <TouchableOpacity style={styles.retryButton} onPress={() => {
                setError(null);
                setIsLoading(true);
              }}>
                <Text style={styles.retryText}>Retry</Text>
              </TouchableOpacity>
            </View>
          )}
        </TouchableOpacity>

        {/* Controls Overlay */}
        {showControls && !isLoading && !error && (
          <Animated.View style={[styles.controlsOverlay, { opacity: controlsOpacity }]}> 
            {/* Top Controls */}
            <SafeAreaView style={styles.topControls}>
              <TouchableOpacity style={styles.modernCloseButton} onPress={onClose}>
                <Text style={styles.closeButtonText}>×</Text>
              </TouchableOpacity>
              <Text style={styles.videoTitle} numberOfLines={1}>
                {videoAttachment?.name || 'Video'}
              </Text>
            </SafeAreaView>

            {/* Center Play/Pause Button */}
            <View style={styles.centerControls}>
              <TouchableOpacity
                style={styles.modernPlayPauseButton}
                onPress={handlePlayPause}
                activeOpacity={0.8}
              >
                <View style={styles.playPauseIconContainer}>
                  <Text style={styles.modernPlayPauseIcon}>
                    {(status as any)?.isPlaying ? '⏸' : '▶'}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* Bottom Controls */}
            <View style={styles.bottomControls}>
              <View style={styles.progressSection}>
                <View style={styles.modernProgressBar}>
                  <View
                    style={[
                      styles.modernProgressFill,
                      {
                        width: (status as any)?.durationMillis
                          ? `${((status as any)?.positionMillis / (status as any)?.durationMillis) * 100}%`
                          : '0%',
                      },
                    ]}
                  />
                </View>
                <View style={styles.timeContainer}>
                  <Text style={styles.modernTimeText}>
                    {(status as any)?.positionMillis ? formatTime((status as any).positionMillis) : '0:00'}
                  </Text>
                  <Text style={styles.modernTimeText}>
                    {(status as any)?.durationMillis ? formatTime((status as any).durationMillis) : '0:00'}
                  </Text>
                </View>
              </View>
            </View>
          </Animated.View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  videoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  video: {
    width: screenWidth,
    height: screenHeight,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginTop: 16,
    fontWeight: '500',
  },
  errorContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    padding: 32,
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  errorText: {
    color: '#FFFFFF',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 24,
    fontWeight: '500',
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 16,
    background: 'linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%)',
  },
  modernCloseButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '300',
    lineHeight: 24,
  },
  videoTitle: {
    flex: 1,
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  centerControls: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernPlayPauseButton: {
    width: 88,
    height: 88,
    borderRadius: 44,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  playPauseIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernPlayPauseIcon: {
    fontSize: 28,
    color: '#000',
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 28,
  },
  bottomControls: {
    paddingHorizontal: 20,
    paddingBottom: 32,
    paddingTop: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  progressSection: {
    width: '100%',
  },
  modernProgressBar: {
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
    marginBottom: 12,
  },
  modernProgressFill: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 3,
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modernTimeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});
