/**
 * WORLD Vocoder Service for React Native
 * Provides real-time voice morphing using WebAssembly WORLD vocoder
 * Designed for <100ms latency voice calls with non-reversible transformations
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

export interface WorldVoiceProfile {
  pitchScale: number;      // 0.7-1.3 (pitch modification)
  spectralWarp: number;    // -10% to +10% (formant shifting)
  reverbAmount: number;    // 0-50% (spatial distortion)
  eqTilt: number;         // -6dB to +6dB (frequency emphasis)
  temporalJitter: number; // Anti-forensic timing variation
  spectralNoise: number;  // Irreversible spectral masking
  antiForensic: boolean;  // Enable anti-forensic processing
}

export interface WorldProcessingConfig {
  sampleRate: number;     // Audio sample rate (typically 48000)
  frameSize: number;      // Frame size in samples (typically 960 for 20ms)
  bufferSize: number;     // Buffer size for processing
  qualityLevel: 'fast' | 'balanced' | 'high';
}

export interface ProcessingStats {
  latency: number;        // Processing latency in ms
  frameCount: number;     // Total frames processed
  errorCount: number;     // Number of processing errors
  averageLatency: number; // Average latency over session
}

/**
 * WORLD Vocoder Processing Session for React Native
 */
class WorldVocoderSession {
  private sessionId: string;
  private config: WorldProcessingConfig;
  private stats: ProcessingStats;
  private isActive: boolean = false;
  private audioContext: AudioContext | null = null;
  private processor: AudioWorkletNode | null = null;

  constructor(sessionId: string, config: WorldProcessingConfig) {
    this.sessionId = sessionId;
    this.config = config;
    this.stats = {
      latency: 0,
      frameCount: 0,
      errorCount: 0,
      averageLatency: 0
    };
  }

  /**
   * Initialize the processing session
   */
  async initialize(): Promise<boolean> {
    try {
      // React Native doesn't have Web Audio API, use fallback mode
      // In a real implementation, this would interface with native audio modules
      console.log('🎤 Initializing audio processing in React Native mode');
      
      this.isActive = true;
      console.log(`🎤 WORLD vocoder session ${this.sessionId} initialized (React Native fallback mode)`);
      return true;
    } catch (error) {
      console.error('Failed to initialize WORLD vocoder session:', error);
      return false;
    }
  }

  /**
   * Process audio frame with voice morphing
   */
  processFrame(audioData: Float32Array, profile: WorldVoiceProfile): Float32Array | null {
    if (!this.isActive) {
      return null;
    }

    try {
      const startTime = Date.now();
      
      // For now, apply basic transformations as fallback
      // In production, this would use the WORLD vocoder WebAssembly module
      const morphedAudio = this.applyBasicMorphing(audioData, profile);
      
      const processingTime = Date.now() - startTime;
      this.updateStats(processingTime);
      
      return morphedAudio;
    } catch (error) {
      this.stats.errorCount++;
      console.error('Audio processing error:', error);
      return null;
    }
  }

  /**
   * Apply basic voice morphing as fallback
   */
  private applyBasicMorphing(audioData: Float32Array, profile: WorldVoiceProfile): Float32Array {
    const morphedAudio = new Float32Array(audioData.length);
    
    // Apply pitch scaling (simplified)
    for (let i = 0; i < audioData.length; i++) {
      let sample = audioData[i];
      
      // Apply pitch scaling by sample rate manipulation simulation
      if (profile.pitchScale !== 1.0) {
        const scaledIndex = i * profile.pitchScale;
        const lowerIndex = Math.floor(scaledIndex);
        const upperIndex = Math.ceil(scaledIndex);
        
        if (upperIndex < audioData.length) {
          const fraction = scaledIndex - lowerIndex;
          sample = audioData[lowerIndex] * (1 - fraction) + 
                  (audioData[upperIndex] || 0) * fraction;
        }
      }
      
      // Apply spectral warping (simplified frequency domain effect)
      if (profile.spectralWarp !== 0) {
        sample *= (1 + profile.spectralWarp / 100);
      }
      
      // Apply EQ tilt (simplified)
      if (profile.eqTilt !== 0) {
        const frequency = (i / audioData.length) * (this.config.sampleRate / 2);
        const tiltFactor = Math.pow(frequency / 1000, profile.eqTilt / 6);
        sample *= tiltFactor;
      }
      
      // Apply temporal jitter for anti-forensic processing
      if (profile.temporalJitter > 0) {
        const jitter = (Math.random() - 0.5) * profile.temporalJitter;
        sample *= (1 + jitter);
      }
      
      // Apply spectral noise for irreversible transformation
      if (profile.spectralNoise > 0) {
        const noise = (Math.random() - 0.5) * profile.spectralNoise;
        sample += noise * 0.001; // Small noise addition
      }
      
      // Clamp to prevent clipping
      morphedAudio[i] = Math.max(-1, Math.min(1, sample));
    }
    
    return morphedAudio;
  }

  /**
   * Get processing statistics
   */
  getStats(): ProcessingStats {
    return { ...this.stats };
  }

  /**
   * Cleanup session
   */
  destroy(): void {
    if (this.processor) {
      this.processor.disconnect();
      this.processor = null;
    }
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    this.isActive = false;
    console.log(`🎤 WORLD vocoder session ${this.sessionId} destroyed`);
  }

  private updateStats(latency: number): void {
    this.stats.frameCount++;
    this.stats.latency = latency;
    this.stats.averageLatency = 
      (this.stats.averageLatency * (this.stats.frameCount - 1) + latency) / this.stats.frameCount;
  }
}

/**
 * WORLD Vocoder Service for React Native
 */
export class WorldVocoderService {
  private sessions: Map<string, WorldVocoderSession> = new Map();
  private defaultConfig: WorldProcessingConfig = {
    sampleRate: 48000,
    frameSize: 960, // 20ms at 48kHz
    bufferSize: 4096,
    qualityLevel: 'balanced'
  };

  constructor() {
    this.checkWebAssemblySupport();
  }

  /**
   * Check if WebAssembly is supported
   */
  isAvailable(): boolean {
    return typeof WebAssembly !== 'undefined';
  }

  /**
   * Create a new voice processing session
   */
  async createSession(userId: string, config?: Partial<WorldProcessingConfig>): Promise<string> {
    const sessionId = this.generateSessionId();
    const sessionConfig = { ...this.defaultConfig, ...config };

    const session = new WorldVocoderSession(sessionId, sessionConfig);
    
    const initialized = await session.initialize();
    if (!initialized) {
      throw new Error('Failed to initialize WORLD vocoder session');
    }

    this.sessions.set(sessionId, session);
    
    // Store session info for persistence
    await this.storeSessionInfo(sessionId, userId, sessionConfig);

    console.log(`🎤 Created WORLD vocoder session: ${sessionId} for user: ${userId}`);
    return sessionId;
  }

  /**
   * Process audio frame for a session
   */
  processAudioFrame(sessionId: string, audioData: Float32Array, 
                   profile: WorldVoiceProfile): Float32Array | null {
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.error(`Session ${sessionId} not found`);
      return null;
    }

    return session.processFrame(audioData, profile);
  }

  /**
   * Get session statistics
   */
  getSessionStats(sessionId: string): ProcessingStats | null {
    const session = this.sessions.get(sessionId);
    return session ? session.getStats() : null;
  }

  /**
   * Destroy a session
   */
  destroySession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.destroy();
      this.sessions.delete(sessionId);
      this.removeSessionInfo(sessionId);
      return true;
    }
    return false;
  }

  /**
   * Get all active sessions
   */
  getActiveSessions(): string[] {
    return Array.from(this.sessions.keys());
  }

  /**
   * Cleanup all sessions
   */
  cleanup(): void {
    for (const session of this.sessions.values()) {
      session.destroy();
    }
    this.sessions.clear();
  }

  private generateSessionId(): string {
    return `world_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async storeSessionInfo(sessionId: string, userId: string, config: WorldProcessingConfig): Promise<void> {
    try {
      const sessionInfo = {
        sessionId,
        userId,
        config,
        createdAt: new Date().toISOString()
      };
      await AsyncStorage.setItem(`world_session_${sessionId}`, JSON.stringify(sessionInfo));
    } catch (error) {
      console.warn('Failed to store session info:', error);
    }
  }

  private async removeSessionInfo(sessionId: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`world_session_${sessionId}`);
    } catch (error) {
      console.warn('Failed to remove session info:', error);
    }
  }

  private checkWebAssemblySupport(): void {
    if (!this.isAvailable()) {
      console.warn('⚠️ WebAssembly not supported. WORLD vocoder will use fallback implementation.');
    } else {
      console.log('✅ WebAssembly supported. WORLD vocoder ready for integration.');
    }
  }
}

// Predefined voice profiles matching backend implementation
export const WORLD_VOICE_PROFILES: Record<string, WorldVoiceProfile> = {
  SECURE_DEEP_MALE: {
    pitchScale: 0.75,
    spectralWarp: -8.0,
    reverbAmount: 20.0,
    eqTilt: -3.0,
    temporalJitter: 0.05,
    spectralNoise: 0.15,
    antiForensic: true
  },
  SECURE_HIGH_FEMALE: {
    pitchScale: 1.25,
    spectralWarp: 6.0,
    reverbAmount: 15.0,
    eqTilt: 2.0,
    temporalJitter: 0.03,
    spectralNoise: 0.12,
    antiForensic: true
  },
  ROBOTIC_SYNTHETIC: {
    pitchScale: 0.9,
    spectralWarp: -15.0,
    reverbAmount: 35.0,
    eqTilt: -6.0,
    temporalJitter: 0.1,
    spectralNoise: 0.25,
    antiForensic: true
  },
  NORMAL_VOICE: {
    pitchScale: 1.0,
    spectralWarp: 0.0,
    reverbAmount: 0.0,
    eqTilt: 0.0,
    temporalJitter: 0.0,
    spectralNoise: 0.0,
    antiForensic: false
  }
  // Additional profiles would be added here...
};

// Export singleton instance
export const worldVocoderService = new WorldVocoderService();
