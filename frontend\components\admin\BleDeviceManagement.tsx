import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  Tooltip,
  Grid
} from '@mui/material';
import {
  Bluetooth,
  BluetoothConnected,
  BluetoothDisabled,
  Edit,
  Delete,
  Refresh,
  Security,
  VolumeUp,
  Info,
  Warning
} from '@mui/icons-material';

interface BleDevice {
  _id: string;
  deviceId: string;
  deviceName: string;
  deviceType: 'earbud' | 'headphone' | 'speaker' | 'generic';
  registrationData: {
    registeredAt: string;
    registeredBy: string;
    scriptVersion?: string;
    rssi?: number;
  };
  status: {
    isVerified: boolean;
    isActive: boolean;
    lastSeen: string;
    connectionCount: number;
    lastRssi?: number;
  };
  voiceCallAuth: {
    enabled: boolean;
    callCount: number;
    authFailures: number;
    authSuccessRate: number;
  };
  security: {
    riskScore: number;
    violations: any[];
  };
  metadata: {
    manufacturer?: string;
    model?: string;
    notes?: string;
    tags?: string[];
  };
}

interface Props {
  userId: string;
  onDeviceUpdate?: () => void;
}

const BleDeviceManagement: React.FC<Props> = ({ userId, onDeviceUpdate }) => {
  const [devices, setDevices] = useState<BleDevice[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editDevice, setEditDevice] = useState<BleDevice | null>(null);
  const [deleteDevice, setDeleteDevice] = useState<BleDevice | null>(null);
  const [includeInactive, setIncludeInactive] = useState(false);

  // Fetch BLE devices for user
  const fetchDevices = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(
        `/api/admin/users/${userId}/ble-devices?includeInactive=${includeInactive}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch BLE devices');
      }

      const data = await response.json();
      setDevices(data.devices || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchDevices();
    }
  }, [userId, includeInactive]);

  // Update device
  const handleUpdateDevice = async (deviceData: Partial<BleDevice>) => {
    if (!editDevice) return;

    try {
      const response = await fetch(`/api/admin/ble-devices/${editDevice._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify(deviceData)
      });

      if (!response.ok) {
        throw new Error('Failed to update device');
      }

      setEditDevice(null);
      fetchDevices();
      onDeviceUpdate?.();
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Delete device
  const handleDeleteDevice = async (permanent: boolean = false) => {
    if (!deleteDevice) return;

    try {
      const response = await fetch(
        `/api/admin/ble-devices/${deleteDevice._id}?permanent=${permanent}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to delete device');
      }

      setDeleteDevice(null);
      fetchDevices();
      onDeviceUpdate?.();
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Generate challenge for device
  const handleGenerateChallenge = async (deviceId: string) => {
    try {
      const response = await fetch(`/api/admin/ble-devices/${deviceId}/challenge`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to generate challenge');
      }

      const data = await response.json();
      alert(`Challenge generated: ${data.challenge}\nExpires at: ${new Date(data.expiresAt).toLocaleString()}`);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const getDeviceTypeIcon = (type: string) => {
    switch (type) {
      case 'earbud':
      case 'headphone':
        return <VolumeUp />;
      default:
        return <Bluetooth />;
    }
  };

  const getStatusColor = (device: BleDevice) => {
    if (!device.status.isActive) return 'default';
    if (device.security.riskScore > 50) return 'error';
    if (device.status.isVerified && device.voiceCallAuth.enabled) return 'success';
    return 'warning';
  };

  const getStatusText = (device: BleDevice) => {
    if (!device.status.isActive) return 'Inactive';
    if (device.security.riskScore > 50) return 'High Risk';
    if (device.status.isVerified && device.voiceCallAuth.enabled) return 'Active';
    return 'Limited';
  };

  return (
    <Box>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              BLE Devices ({devices.length})
            </Typography>
            <Box>
              <FormControlLabel
                control={
                  <Switch
                    checked={includeInactive}
                    onChange={(e) => setIncludeInactive(e.target.checked)}
                  />
                }
                label="Include Inactive"
              />
              <Button
                startIcon={<Refresh />}
                onClick={fetchDevices}
                disabled={loading}
                sx={{ ml: 1 }}
              >
                Refresh
              </Button>
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {devices.length === 0 ? (
            <Box textAlign="center" py={4}>
              <BluetoothDisabled sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="body1" color="text.secondary">
                No BLE devices registered for this user
              </Typography>
              <Typography variant="body2" color="text.secondary" mt={1}>
                Use the BLE registration scripts to add devices
              </Typography>
            </Box>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Device</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Voice Calls</TableCell>
                    <TableCell>Security</TableCell>
                    <TableCell>Last Seen</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {devices.map((device) => (
                    <TableRow key={device._id}>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          {getDeviceTypeIcon(device.deviceType)}
                          <Box ml={1}>
                            <Typography variant="body2" fontWeight="medium">
                              {device.deviceName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {device.deviceId}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={device.deviceType}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatusText(device)}
                          color={getStatusColor(device)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            {device.voiceCallAuth.enabled ? 'Enabled' : 'Disabled'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {device.voiceCallAuth.callCount} calls, {device.voiceCallAuth.authSuccessRate.toFixed(1)}% success
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Typography variant="body2">
                            {device.security.riskScore}/100
                          </Typography>
                          {device.security.violations.length > 0 && (
                            <Tooltip title={`${device.security.violations.length} violations`}>
                              <Warning color="warning" sx={{ ml: 1, fontSize: 16 }} />
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(device.status.lastSeen).toLocaleDateString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(device.status.lastSeen).toLocaleTimeString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => setEditDevice(device)}
                          title="Edit Device"
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleGenerateChallenge(device._id)}
                          title="Generate Auth Challenge"
                        >
                          <Security />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => setDeleteDevice(device)}
                          title="Delete Device"
                          color="error"
                        >
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Edit Device Dialog */}
      <Dialog open={!!editDevice} onClose={() => setEditDevice(null)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit BLE Device</DialogTitle>
        <DialogContent>
          {editDevice && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Device Name"
                  defaultValue={editDevice.deviceName}
                  onChange={(e) => setEditDevice({...editDevice, deviceName: e.target.value})}
                />
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>Device Type</InputLabel>
                  <Select
                    value={editDevice.deviceType}
                    onChange={(e) => setEditDevice({...editDevice, deviceType: e.target.value as any})}
                  >
                    <MenuItem value="earbud">Earbud</MenuItem>
                    <MenuItem value="headphone">Headphone</MenuItem>
                    <MenuItem value="speaker">Speaker</MenuItem>
                    <MenuItem value="generic">Generic</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={editDevice.voiceCallAuth.enabled}
                      onChange={(e) => setEditDevice({
                        ...editDevice,
                        voiceCallAuth: {...editDevice.voiceCallAuth, enabled: e.target.checked}
                      })}
                    />
                  }
                  label="Voice Call Enabled"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  defaultValue={editDevice.metadata.notes || ''}
                  onChange={(e) => setEditDevice({
                    ...editDevice,
                    metadata: {...editDevice.metadata, notes: e.target.value}
                  })}
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDevice(null)}>Cancel</Button>
          <Button onClick={() => handleUpdateDevice(editDevice!)} variant="contained">
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Device Dialog */}
      <Dialog open={!!deleteDevice} onClose={() => setDeleteDevice(null)}>
        <DialogTitle>Delete BLE Device</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{deleteDevice?.deviceName}"?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            This will prevent the device from being used for voice call authentication.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDevice(null)}>Cancel</Button>
          <Button onClick={() => handleDeleteDevice(false)} color="warning">
            Deactivate
          </Button>
          <Button onClick={() => handleDeleteDevice(true)} color="error">
            Permanently Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BleDeviceManagement;
