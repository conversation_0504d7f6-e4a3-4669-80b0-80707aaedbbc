/**
 * @format
 * Test Entry Point - Minimal version for debugging
 */

import {AppRegistry} from 'react-native';
import {name as appName} from './app.json';

// Simple test component
const TestApp = () => {
  const React = require('react');
  const { View, Text, StyleSheet } = require('react-native');
  
  return React.createElement(View, { style: styles.container },
    React.createElement(Text, { style: styles.text }, 'CCALC Test App - Metro Working!')
  );
};

const styles = {
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
  },
  text: {
    color: '#ffffff',
    fontSize: 18,
    textAlign: 'center',
  },
};

// Register the test component
AppRegistry.registerComponent(appName, () => TestApp);
