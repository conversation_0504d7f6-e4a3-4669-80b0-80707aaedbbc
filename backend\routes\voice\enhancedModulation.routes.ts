/**
 * Enhanced Voice Modulation Routes
 * Routes for advanced voice modulation with SoX integration
 */

import express from 'express';
import multer from 'multer';
import {
  getEnhancedProfiles,
  createEnhancedProfile,
  testEnhancedProfile,
  testEnhancedProfileWithUpload,
  getEnhancedProfileByName,
  updateUserEnhancedProfile,
  getSoxCapabilities
} from '../../controllers/voice/enhancedModulation.controller';
import { authenticateToken, requireAdmin } from '../../middleware/auth';

// Configure multer for audio file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept audio files
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed'));
    }
  }
});

const router = express.Router();

// Apply admin authentication to all routes
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * @route GET /api/voice/profiles/enhanced
 * @desc Get all enhanced voice profiles
 * @access Admin
 */
router.get('/profiles/enhanced', getEnhancedProfiles);

/**
 * @route POST /api/voice/profiles/enhanced
 * @desc Create a new enhanced voice profile
 * @access Admin
 */
router.post('/profiles/enhanced', createEnhancedProfile);

/**
 * @route GET /api/voice/profiles/enhanced/:name
 * @desc Get enhanced profile by name
 * @access Admin
 */
router.get('/profiles/enhanced/:name', getEnhancedProfileByName);

/**
 * @route POST /api/voice/test-enhanced-profile
 * @desc Test an enhanced voice profile
 * @access Admin
 */
router.post('/test-enhanced-profile', testEnhancedProfile);

/**
 * @route POST /api/voice/test-enhanced-profile-upload
 * @desc Test an enhanced voice profile with uploaded audio
 * @access Admin
 */
router.post('/test-enhanced-profile-upload', upload.single('audioFile'), testEnhancedProfileWithUpload);

/**
 * @route PUT /api/voice/users/:userId/enhanced-profile
 * @desc Update user's enhanced voice profile
 * @access Admin
 */
router.put('/users/:userId/enhanced-profile', updateUserEnhancedProfile);

/**
 * @route GET /api/voice/sox-capabilities
 * @desc Get SoX capabilities and available effects
 * @access Admin
 */
router.get('/sox-capabilities', getSoxCapabilities);

export default router;
