/**
 * Unified Voice Processing Pipeline
 * Combines neutralization, SoX modulation, and real-time processing
 * Designed for complete input tone removal with <100ms latency
 */

import { RealTimeVoiceProcessor, REALTIME_VOICE_PROFILES, RealTimeVoiceProfile } from './realTimeVoiceProcessor';
import { LightweightVoiceNeutralizer, NEUTRALIZATION_PROFILES, NeutralizationConfig } from './lightweightVoiceNeutralizer';
import { VoiceModulationService, VOICE_PROFILES, VoiceModulationProfile } from './voiceModulation';
import { ADVANCED_VOICE_PROFILES } from './advancedVoiceProfiles';
import { VoicePerformanceMonitor } from './voicePerformanceMonitor';
import * as crypto from 'crypto';

export interface UnifiedVoiceProfile {
    name: string;
    description: string;
    type: 'unified';

    // Processing pipeline configuration
    pipeline: {
        neutralizationFirst: boolean;      // Apply neutralization before modulation
        soxModulation: boolean;            // Apply SoX modulation
        antiForensic: boolean;             // Apply anti-forensic processing
        realTimeOptimized: boolean;        // Optimize for real-time processing
    };

    // Neutralization settings
    neutralization: NeutralizationConfig;

    // SoX modulation settings
    modulation: VoiceModulationProfile;

    // Performance targets
    performance: {
        latencyTarget: number;             // Target latency in ms
        qualityMode: 'speed' | 'balanced' | 'quality';
        parallelProcessing: boolean;
        streamingMode: boolean;
    };

    // Security features
    security: {
        inputToneRemoval: boolean;         // Complete input tone removal
        reversible: boolean;               // Whether processing is reversible
        antiForensic: boolean;             // Anti-forensic capabilities
        encryptionSupport: boolean;        // Support for encrypted processing
    };
}

export const UNIFIED_VOICE_PROFILES: Record<string, UnifiedVoiceProfile> = {
    // Basic unified profiles
    ULTIMATE_SECURE_FAST: {
        name: 'Ultimate Secure (Fast)',
        description: 'Maximum security with complete input tone removal - optimized for <50ms latency',
        type: 'unified',
        pipeline: {
            neutralizationFirst: true,
            soxModulation: true,
            antiForensic: true,
            realTimeOptimized: true
        },
        neutralization: NEUTRALIZATION_PROFILES.REAL_TIME_LIGHT,
        modulation: VOICE_PROFILES.SECURE_MALE,
        performance: {
            latencyTarget: 50,
            qualityMode: 'speed',
            parallelProcessing: true,
            streamingMode: true
        },
        security: {
            inputToneRemoval: true,
            reversible: false,
            antiForensic: true,
            encryptionSupport: true
        }
    },

    ULTIMATE_SECURE_BALANCED: {
        name: 'Ultimate Secure (Balanced)',
        description: 'Balanced security and quality with complete input tone removal - <80ms latency',
        type: 'unified',
        pipeline: {
            neutralizationFirst: true,
            soxModulation: true,
            antiForensic: true,
            realTimeOptimized: true
        },
        neutralization: NEUTRALIZATION_PROFILES.REAL_TIME_MEDIUM,
        modulation: VOICE_PROFILES.DEEP_SECURE,
        performance: {
            latencyTarget: 80,
            qualityMode: 'balanced',
            parallelProcessing: true,
            streamingMode: true
        },
        security: {
            inputToneRemoval: true,
            reversible: false,
            antiForensic: true,
            encryptionSupport: true
        }
    },

    ULTIMATE_SECURE_QUALITY: {
        name: 'Ultimate Secure (Quality)',
        description: 'Maximum quality with complete input tone removal - <100ms latency',
        type: 'unified',
        pipeline: {
            neutralizationFirst: true,
            soxModulation: true,
            antiForensic: true,
            realTimeOptimized: false
        },
        neutralization: NEUTRALIZATION_PROFILES.OFFLINE_HEAVY,
        modulation: VOICE_PROFILES.ANONYMOUS,
        performance: {
            latencyTarget: 100,
            qualityMode: 'quality',
            parallelProcessing: true,
            streamingMode: false
        },
        security: {
            inputToneRemoval: true,
            reversible: false,
            antiForensic: true,
            encryptionSupport: true
        }
    },

    // Advanced profiles with specialized capabilities
    ...ADVANCED_VOICE_PROFILES
};

export class UnifiedVoiceProcessor {
    private realTimeProcessor: RealTimeVoiceProcessor;
    private neutralizer: LightweightVoiceNeutralizer;
    private modulator: VoiceModulationService;
    private performanceMonitor: VoicePerformanceMonitor;

    constructor() {
        this.realTimeProcessor = new RealTimeVoiceProcessor();
        this.neutralizer = new LightweightVoiceNeutralizer();
        this.modulator = new VoiceModulationService();
        this.performanceMonitor = new VoicePerformanceMonitor();

        console.log('Unified Voice Processor initialized with complete input tone removal capabilities and performance monitoring');
    }

    /**
     * Process voice using the unified pipeline for complete input tone removal
     */
    async processVoiceUnified(
        audioBuffer: Buffer,
        profile: UnifiedVoiceProfile,
        userId?: string
    ): Promise<Buffer> {
        const startTime = Date.now();
        const sessionId = crypto.randomUUID();

        try {
            console.log(`🎯 Starting unified voice processing (${profile.name})`);

            let processedAudio = audioBuffer;
            const processingSteps: string[] = [];

            // Stage 1: Voice Neutralization (Input tone removal)
            if (profile.pipeline.neutralizationFirst) {
                console.log('Stage 1: Applying voice neutralization for input tone removal');
                processedAudio = await this.neutralizer.neutralizeVoiceEnhanced(
                    processedAudio,
                    profile.neutralization,
                    true, // Use SoX enhancement
                    userId
                );
                processingSteps.push('neutralization');
            }

            // Stage 2: SoX Modulation (Voice transformation)
            if (profile.pipeline.soxModulation) {
                console.log('Stage 2: Applying SoX modulation for voice transformation');
                processedAudio = await this.modulator.modulateVoice(
                    processedAudio,
                    profile.modulation,
                    userId
                );
                processingSteps.push('sox-modulation');
            }

            // Stage 3: Anti-forensic processing
            if (profile.pipeline.antiForensic) {
                console.log('Stage 3: Applying anti-forensic processing');
                processedAudio = await this.applyAntiForensicProcessing(
                    processedAudio,
                    profile
                );
                processingSteps.push('anti-forensic');
            }

            const processingTime = Date.now() - startTime;

            // Record performance metrics
            await this.performanceMonitor.recordMetric({
                sessionId,
                userId,
                profileName: profile.name,
                processingTime,
                latencyTarget: profile.performance.latencyTarget,
                inputSize: audioBuffer.length,
                outputSize: processedAudio.length,
                processingSteps
            });

            console.log(`✅ Unified voice processing completed:`, {
                sessionId,
                processingTime,
                latencyTarget: profile.performance.latencyTarget,
                withinTarget: processingTime <= profile.performance.latencyTarget,
                inputSize: audioBuffer.length,
                outputSize: processedAudio.length,
                processingSteps,
                inputToneRemoved: profile.security.inputToneRemoval,
                antiForensic: profile.security.antiForensic
            });

            return processedAudio;

        } catch (error: any) {
            console.error('Unified voice processing failed:', error);
            throw new Error(`Unified voice processing failed: ${error.message}`);
        }
    }

    /**
     * Apply anti-forensic processing to prevent voice analysis
     */
    private async applyAntiForensicProcessing(
        audioBuffer: Buffer,
        profile: UnifiedVoiceProfile
    ): Promise<Buffer> {
        // Use the real-time processor's anti-forensic capabilities
        const realTimeProfile: RealTimeVoiceProfile = {
            name: 'Anti-Forensic',
            description: 'Anti-forensic processing',
            neutralization: {
                enabled: false,
                f0Neutralization: false,
                formantNormalization: false,
                spectralSmoothing: 0,
                temporalJitter: 0,
                noiseLevel: 0,
                preserveClarity: true
            },
            modulation: {
                enabled: false,
                pitch: 0,
                tempo: 1.0,
                reverb: 0,
                distortion: 0,
                formant: 0,
                chorus: false,
                normalize: true
            },
            performance: {
                realTimeMode: profile.performance.streamingMode,
                latencyTarget: profile.performance.latencyTarget,
                bufferSize: 1024,
                sampleRate: 44100,
                bitDepth: 16,
                parallelProcessing: profile.performance.parallelProcessing,
                streamingMode: profile.performance.streamingMode
            },
            security: {
                inputToneRemoval: false,
                antiForensic: true,
                reversible: false,
                outputVariation: true
            }
        };

        return await this.realTimeProcessor.processVoiceRealTime(audioBuffer, realTimeProfile, 'anti-forensic');
    }

    /**
     * Get available unified profiles
     */
    getAvailableProfiles(): Record<string, UnifiedVoiceProfile> {
        return UNIFIED_VOICE_PROFILES;
    }

    /**
     * Check system capabilities for unified processing
     */
    async checkCapabilities(): Promise<any> {
        const realTimeCapabilities = await this.realTimeProcessor.checkCapabilities();

        return {
            ...realTimeCapabilities,
            unifiedProcessing: true,
            inputToneRemoval: true,
            soxIntegration: true,
            neutralizationIntegration: true,
            antiForensicCapabilities: true,
            estimatedLatency: {
                fast: 50,
                balanced: 80,
                quality: 100
            },
            features: {
                completeInputToneRemoval: true,
                nonReversibleProcessing: true,
                realTimeCapable: true,
                antiForensicProcessing: true,
                encryptionSupport: true,
                streamingMode: true,
                parallelProcessing: true
            }
        };
    }

    /**
     * Test unified processing performance
     */
    async testUnifiedPerformance(audioBuffer: Buffer): Promise<any> {
        const results: any[] = [];

        for (const [profileName, profile] of Object.entries(UNIFIED_VOICE_PROFILES)) {
            const startTime = Date.now();

            try {
                await this.processVoiceUnified(audioBuffer, profile);
                const processingTime = Date.now() - startTime;

                results.push({
                    profile: profileName,
                    success: true,
                    processingTime,
                    latencyTarget: profile.performance.latencyTarget,
                    withinTarget: processingTime <= profile.performance.latencyTarget,
                    inputToneRemoved: profile.security.inputToneRemoval,
                    antiForensic: profile.security.antiForensic,
                    qualityMode: profile.performance.qualityMode
                });
            } catch (error: any) {
                results.push({
                    profile: profileName,
                    success: false,
                    error: error.message,
                    processingTime: Date.now() - startTime
                });
            }
        }

        return {
            results,
            summary: {
                totalTests: results.length,
                successfulTests: results.filter(r => r.success).length,
                averageLatency: results.filter(r => r.success).reduce((sum, r) => sum + r.processingTime, 0) / results.filter(r => r.success).length || 0,
                inputToneRemovalTests: results.filter(r => r.success && r.inputToneRemoved).length,
                antiForensicTests: results.filter(r => r.success && r.antiForensic).length
            }
        };
    }

    /**
     * Get performance statistics
     */
    getPerformanceStats(timeWindow?: number): any {
        return this.performanceMonitor.getPerformanceStats(timeWindow);
    }

    /**
     * Get system health status
     */
    getSystemHealth(): any {
        return this.performanceMonitor.getSystemHealth();
    }

    /**
     * Get performance trends
     */
    getPerformanceTrends(hours: number = 24): any {
        return this.performanceMonitor.getPerformanceTrends(hours);
    }

    /**
     * Export performance data
     */
    async exportPerformanceData(format: 'json' | 'csv' = 'json'): Promise<string> {
        return await this.performanceMonitor.exportPerformanceData(format);
    }

    /**
     * Reset performance metrics (for testing)
     */
    resetPerformanceMetrics(): void {
        this.performanceMonitor.reset();
    }
}
