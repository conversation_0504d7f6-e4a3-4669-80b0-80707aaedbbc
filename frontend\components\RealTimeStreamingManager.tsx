/**
 * Real-Time Streaming Manager Component
 * Interface for managing real-time voice processing sessions
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Speed as SpeedIcon,
  Timeline as TimelineIcon,
  Person as PersonIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';

interface StreamingSession {
  sessionId: string;
  userId?: string;
  profile: string;
  status: 'active' | 'paused' | 'stopped';
  startTime: string;
  totalProcessed: number;
  averageLatency: number;
  performance: {
    withinTarget: boolean;
    latencyTarget: number;
  };
}

interface StreamingStats {
  activeSessions: number;
  totalProcessed: number;
  averageLatency: number;
  sessionsByStatus: {
    active: number;
    paused: number;
  };
  sessionsByProfile: Record<string, number>;
}

export const RealTimeStreamingManager: React.FC = () => {
  const [sessions, setSessions] = useState<StreamingSession[]>([]);
  const [stats, setStats] = useState<StreamingStats | null>(null);
  const [profiles, setProfiles] = useState<string[]>([]);
  const [selectedProfile, setSelectedProfile] = useState<string>('');
  const [isCreatingSession, setIsCreatingSession] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProfiles();
    loadStats();
    const interval = setInterval(loadStats, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const loadProfiles = async () => {
    try {
      const response = await fetch('/api/voice/unified/profiles', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        const profileNames = data.data.profiles.map((p: any) => p.id);
        setProfiles(profileNames);
        if (profileNames.length > 0) {
          setSelectedProfile(profileNames[0]);
        }
      }
    } catch (error) {
      console.error('Failed to load profiles:', error);
    }
  };

  const loadStats = async () => {
    try {
      const response = await fetch('/api/voice/streaming/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
        setSessions(data.data.sessions || []);
      }
    } catch (error) {
      console.error('Failed to load streaming stats:', error);
    }
  };

  const createSession = async () => {
    if (!selectedProfile) {
      setError('Please select a profile');
      return;
    }

    setIsCreatingSession(true);
    setError(null);

    try {
      const response = await fetch('/api/voice/streaming/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({
          profileName: selectedProfile,
          config: {
            latencyTarget: 50,
            processingMode: 'realtime'
          }
        })
      });

      if (response.ok) {
        await loadStats(); // Refresh the stats and sessions
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to create session');
      }
    } catch (error) {
      console.error('Failed to create session:', error);
      setError('Failed to create session');
    } finally {
      setIsCreatingSession(false);
    }
  };

  const pauseSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/voice/streaming/session/${sessionId}/pause`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (response.ok) {
        await loadStats();
      }
    } catch (error) {
      console.error('Failed to pause session:', error);
    }
  };

  const resumeSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/voice/streaming/session/${sessionId}/resume`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (response.ok) {
        await loadStats();
      }
    } catch (error) {
      console.error('Failed to resume session:', error);
    }
  };

  const stopSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/voice/streaming/session/${sessionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (response.ok) {
        await loadStats();
      }
    } catch (error) {
      console.error('Failed to stop session:', error);
    }
  };

  const getLatencyStatus = (latency: number, target: number) => {
    if (latency <= target) {
      return { color: 'text-green-600', icon: CheckIcon };
    } else if (latency <= target * 1.5) {
      return { color: 'text-yellow-600', icon: WarningIcon };
    } else {
      return { color: 'text-red-600', icon: WarningIcon };
    }
  };

  const formatDuration = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diff = now.getTime() - start.getTime();
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-lg p-6 text-white">
        <div className="flex items-center space-x-3">
          <TimelineIcon className="h-8 w-8" />
          <div>
            <h2 className="text-2xl font-bold">Real-Time Streaming Manager</h2>
            <p className="text-green-100">Manage real-time voice processing sessions</p>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <PlayIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-gray-500">Active Sessions</div>
                <div className="text-2xl font-bold text-gray-900">{stats.activeSessions}</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <SpeedIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-gray-500">Avg Latency</div>
                <div className="text-2xl font-bold text-gray-900">{Math.round(stats.averageLatency)}ms</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <SecurityIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-gray-500">Total Processed</div>
                <div className="text-2xl font-bold text-gray-900">{(stats.totalProcessed / 1024 / 1024).toFixed(1)}MB</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <PersonIcon className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-gray-500">Status</div>
                <div className="text-sm text-gray-900">
                  {stats.sessionsByStatus.active} active, {stats.sessionsByStatus.paused} paused
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create New Session */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Session</h3>
        
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Voice Profile
            </label>
            <select
              value={selectedProfile}
              onChange={(e) => setSelectedProfile(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {profiles.map((profile) => (
                <option key={profile} value={profile}>
                  {profile.replace(/_/g, ' ')}
                </option>
              ))}
            </select>
          </div>
          
          <div className="pt-6">
            <button
              onClick={createSession}
              disabled={!selectedProfile || isCreatingSession}
              className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                !selectedProfile || isCreatingSession
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isCreatingSession ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <AddIcon className="h-5 w-5 mr-2" />
                  Create Session
                </>
              )}
            </button>
          </div>
        </div>

        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <WarningIcon className="h-5 w-5 text-red-600 mr-2" />
              <span className="text-red-800">{error}</span>
            </div>
          </div>
        )}
      </div>

      {/* Active Sessions */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Active Sessions</h3>
          <button
            onClick={loadStats}
            className="flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <RefreshIcon className="h-4 w-4 mr-1" />
            Refresh
          </button>
        </div>

        <div className="divide-y divide-gray-200">
          {sessions.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              No active sessions
            </div>
          ) : (
            sessions.map((session) => (
              <motion.div
                key={session.sessionId}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="px-6 py-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="font-medium text-gray-900">
                        {session.profile.replace(/_/g, ' ')}
                      </h4>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        session.status === 'active' 
                          ? 'bg-green-100 text-green-800'
                          : session.status === 'paused'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {session.status}
                      </span>
                      {session.userId && (
                        <span className="text-sm text-gray-500">
                          User: {session.userId}
                        </span>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-500">Duration:</span>
                        <div className="text-gray-900">{formatDuration(session.startTime)}</div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-500">Processed:</span>
                        <div className="text-gray-900">{(session.totalProcessed / 1024).toFixed(1)}KB</div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-500">Avg Latency:</span>
                        <div className={`font-medium ${getLatencyStatus(session.averageLatency, session.performance.latencyTarget).color}`}>
                          {Math.round(session.averageLatency)}ms
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-500">Performance:</span>
                        <div className={`flex items-center ${getLatencyStatus(session.averageLatency, session.performance.latencyTarget).color}`}>
                          {React.createElement(getLatencyStatus(session.averageLatency, session.performance.latencyTarget).icon, { className: "h-4 w-4 mr-1" })}
                          {session.performance.withinTarget ? 'Good' : 'Slow'}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    {session.status === 'active' ? (
                      <button
                        onClick={() => pauseSession(session.sessionId)}
                        className="p-2 text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors"
                        title="Pause Session"
                      >
                        <PauseIcon className="h-5 w-5" />
                      </button>
                    ) : session.status === 'paused' ? (
                      <button
                        onClick={() => resumeSession(session.sessionId)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                        title="Resume Session"
                      >
                        <PlayIcon className="h-5 w-5" />
                      </button>
                    ) : null}
                    
                    <button
                      onClick={() => stopSession(session.sessionId)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Stop Session"
                    >
                      <StopIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default RealTimeStreamingManager;
