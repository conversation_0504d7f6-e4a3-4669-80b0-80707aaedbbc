/**
 * Voice System Setup Script
 * Automatically sets up the WORLD vocoder system with fallback support
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🎤 Setting up CCALC Voice System...');
console.log('=====================================');

// Check Node.js version
const nodeVersion = process.version;
console.log(`📋 Node.js version: ${nodeVersion}`);

// Check if we're in the correct directory
const packageJsonPath = path.join(__dirname, '..', 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the backend directory.');
  process.exit(1);
}

// Install dependencies if needed
console.log('📦 Checking dependencies...');
try {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const nodeModulesPath = path.join(__dirname, '..', 'node_modules');
  
  if (!fs.existsSync(nodeModulesPath)) {
    console.log('📥 Installing dependencies...');
    execSync('npm install', { 
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit' 
    });
  } else {
    console.log('✅ Dependencies already installed');
  }
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

// Check for build tools
console.log('🔧 Checking build tools...');
let hasBuildTools = false;
try {
  execSync('node-gyp --version', { stdio: 'pipe' });
  hasBuildTools = true;
  console.log('✅ node-gyp found');
} catch (error) {
  console.log('⚠️  node-gyp not found globally');
  
  // Try to install node-gyp locally
  try {
    console.log('📥 Installing node-gyp locally...');
    execSync('npm install node-gyp', { 
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit' 
    });
    hasBuildTools = true;
    console.log('✅ node-gyp installed locally');
  } catch (installError) {
    console.log('⚠️  Could not install node-gyp locally');
  }
}

// Try to build WORLD vocoder
console.log('🎯 Setting up WORLD vocoder...');
try {
  execSync('npm run build:world', { 
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit' 
  });
  console.log('✅ WORLD vocoder setup completed');
} catch (error) {
  console.log('⚠️  WORLD vocoder native build failed, but fallback mode is available');
}

// Verify voice system components
console.log('🔍 Verifying voice system components...');

const requiredFiles = [
  'services/worldVocoderService.ts',
  'services/voiceSecurityService.ts',
  'services/realTimeVoiceStreaming.ts',
  'services/voiceCallRecording.ts',
  'services/performanceMonitor.ts',
  'api/voice/realtime.ts',
  'api/voice/recordings.ts'
];

let allFilesExist = true;
for (const file of requiredFiles) {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
}

if (!allFilesExist) {
  console.error('❌ Some required voice system files are missing!');
  process.exit(1);
}

// Create voice profiles configuration
console.log('📝 Setting up voice profiles...');
const voiceProfilesPath = path.join(__dirname, '..', 'config', 'voice-profiles.json');
const configDir = path.dirname(voiceProfilesPath);

if (!fs.existsSync(configDir)) {
  fs.mkdirSync(configDir, { recursive: true });
}

const voiceProfiles = {
  "profiles": [
    {
      "name": "SECURE_DEEP_MALE",
      "description": "Deep male voice with security features",
      "pitchScale": 0.8,
      "spectralWarp": -5.0,
      "reverbAmount": 15.0,
      "eqTilt": -2.0,
      "temporalJitter": 0.05,
      "spectralNoise": 0.15
    },
    {
      "name": "SECURE_HIGH_FEMALE",
      "description": "High female voice with security features",
      "pitchScale": 1.3,
      "spectralWarp": 8.0,
      "reverbAmount": 20.0,
      "eqTilt": 3.0,
      "temporalJitter": 0.05,
      "spectralNoise": 0.15
    },
    {
      "name": "SECURE_ROBOTIC",
      "description": "Robotic voice with heavy processing",
      "pitchScale": 1.0,
      "spectralWarp": 0.0,
      "reverbAmount": 30.0,
      "eqTilt": 0.0,
      "temporalJitter": 0.08,
      "spectralNoise": 0.25
    },
    {
      "name": "SECURE_WHISPER",
      "description": "Whisper-like voice with security",
      "pitchScale": 1.1,
      "spectralWarp": 3.0,
      "reverbAmount": 10.0,
      "eqTilt": -1.0,
      "temporalJitter": 0.03,
      "spectralNoise": 0.12
    },
    {
      "name": "SECURE_ELDERLY",
      "description": "Elderly voice simulation with security",
      "pitchScale": 0.9,
      "spectralWarp": -3.0,
      "reverbAmount": 25.0,
      "eqTilt": -1.5,
      "temporalJitter": 0.06,
      "spectralNoise": 0.18
    },
    {
      "name": "NORMAL_VOICE",
      "description": "Natural voice (regular users only)",
      "pitchScale": 1.0,
      "spectralWarp": 0.0,
      "reverbAmount": 0.0,
      "eqTilt": 0.0,
      "temporalJitter": 0.0,
      "spectralNoise": 0.0
    }
  ],
  "defaultProfile": "SECURE_DEEP_MALE",
  "superuserProfiles": [
    "SECURE_DEEP_MALE",
    "SECURE_HIGH_FEMALE",
    "SECURE_ROBOTIC",
    "SECURE_WHISPER",
    "SECURE_ELDERLY"
  ],
  "regularUserProfiles": [
    "NORMAL_VOICE"
  ]
};

fs.writeFileSync(voiceProfilesPath, JSON.stringify(voiceProfiles, null, 2));
console.log('✅ Voice profiles configuration created');

// Create recordings directory
const recordingsDir = path.join(__dirname, '..', 'recordings');
if (!fs.existsSync(recordingsDir)) {
  fs.mkdirSync(recordingsDir, { recursive: true });
  console.log('✅ Voice recordings directory created');
} else {
  console.log('✅ Voice recordings directory exists');
}

// Final system check
console.log('');
console.log('🎉 CCALC Voice System Setup Complete!');
console.log('=====================================');
console.log('');

// Check if native addon is available
const addonPath = path.join(__dirname, '..', 'build', 'Release', 'world_vocoder.node');
if (fs.existsSync(addonPath)) {
  console.log('✅ Native WORLD vocoder: AVAILABLE');
  console.log('   - High-quality voice morphing enabled');
  console.log('   - Optimal performance and latency');
} else {
  console.log('🔄 Native WORLD vocoder: FALLBACK MODE');
  console.log('   - JavaScript-based voice processing');
  console.log('   - Fully functional for development');
  console.log('   - Run "npm run build:world" to enable native mode');
}

console.log('');
console.log('🔒 Security Features:');
console.log('   ✅ Non-reversible voice transformation');
console.log('   ✅ Anti-forensic protection');
console.log('   ✅ Real-time processing (<100ms)');
console.log('   ✅ Call recording system');
console.log('   ✅ Admin panel integration');
console.log('');
console.log('🚀 Ready to start the server with: npm run dev');
console.log('');
