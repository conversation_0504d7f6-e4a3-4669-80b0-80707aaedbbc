import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import Button from '../../components/admin/Button';
import { 
  Mic as MicrophoneIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Check as CheckIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { apiClient } from '../../utils/axiosClient';

interface VoiceCall {
  id: string;
  callId: string;
  callerId: string;
  recipientId: string;
  startTime: string;
  endTime?: string;
  duration: number;
  status: string;
  morphingProfile: string;
  recordingPath?: string;
  recordingSize?: number;
  callerIP: string;
  deviceFingerprint: string;
  callQuality: string;
  metadata?: {
    recordingEnabled?: boolean;
    recordingNotified?: boolean;
    recordingStarted?: string;
  };
}

const VoiceRecordingsPage: React.FC = () => {
  const [voiceCalls, setVoiceCalls] = useState<VoiceCall[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCall, setSelectedCall] = useState<VoiceCall | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    fetchVoiceCalls();
  }, []);

  const fetchVoiceCalls = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.backend.get('/api/voice/calls');
      setVoiceCalls(response.data.calls || []);
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to fetch voice calls');
    } finally {
      setIsLoading(false);
    }
  };

  const downloadRecording = async (callId: string, filename: string) => {
    try {
      const response = await apiClient.backend.get(`/api/voice/recording/${callId}`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename || `recording_${callId}.wav`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to download recording');
    }
  };

  const filteredCalls = voiceCalls.filter(call => {
    if (filterStatus === 'all') return true;
    return call.status === filterStatus;
  });

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'N/A';
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(2)} MB`;
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <Head>
          <title>Voice Recordings | CCALC Admin Panel</title>
        </Head>
        <div className="container">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <RefreshIcon className="animate-spin text-4xl mb-4 mx-auto" />
              <p>Loading voice recordings...</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Voice Recordings | CCALC Admin Panel</title>
      </Head>
      <div className="container mx-auto px-6 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Voice Call Recordings</h1>
          <p className="text-gray-600">
            Monitor and manage all voice call recordings. All calls are automatically recorded for compliance.
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center text-red-800">
              <WarningIcon className="mr-3" />
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Filters and Stats */}
        <div className="bg-white shadow-lg rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium text-gray-700">Filter by Status:</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="all">All Calls</option>
                <option value="ended">Completed</option>
                <option value="connected">Active</option>
                <option value="failed">Failed</option>
              </select>
            </div>
            <Button onClick={fetchVoiceCalls} variant="outline">
              <RefreshIcon className="mr-2" />
              Refresh
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{voiceCalls.length}</div>
              <div className="text-sm text-blue-800">Total Calls</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {voiceCalls.filter(c => c.status === 'ended').length}
              </div>
              <div className="text-sm text-green-800">Completed</div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {voiceCalls.filter(c => c.recordingPath).length}
              </div>
              <div className="text-sm text-yellow-800">With Recordings</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {voiceCalls.filter(c => c.metadata?.recordingNotified === false).length}
              </div>
              <div className="text-sm text-purple-800">Hidden Recordings</div>
            </div>
          </div>
        </div>

        {/* Voice Calls Table */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Call Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Participants
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Voice Profile
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Recording
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCalls.map((call) => (
                  <tr key={call.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {call.callId.substring(0, 12)}...
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(call.startTime).toLocaleString()}
                        </div>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          call.status === 'ended' 
                            ? 'bg-green-100 text-green-800'
                            : call.status === 'connected'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {call.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>
                        <div>From: {call.callerId}</div>
                        <div>To: {call.recipientId}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {call.duration > 0 ? formatDuration(call.duration) : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className="px-2 py-1 bg-gray-100 rounded text-xs">
                        {call.morphingProfile}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {call.recordingPath ? (
                        <div>
                          <div className="flex items-center">
                            <CheckIcon className="text-green-500 mr-1" />
                            Available
                          </div>
                          <div className="text-xs text-gray-400">
                            {formatFileSize(call.recordingSize)}
                          </div>
                          {call.metadata?.recordingNotified === false && (
                            <div className="text-xs text-purple-600">
                              🔒 Hidden from user
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <CloseIcon className="text-red-500 mr-1" />
                          Not available
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        {call.recordingPath && (
                          <button
                            onClick={() => downloadRecording(call.callId, `call_${call.callId}.wav`)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Download
                          </button>
                        )}
                        <button
                          onClick={() => setSelectedCall(call)}
                          className="text-gray-600 hover:text-gray-900"
                        >
                          Details
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {filteredCalls.length === 0 && (
          <div className="text-center py-12">
            <MicrophoneIcon className="text-gray-400 text-6xl mb-4 mx-auto" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No voice calls found</h3>
            <p className="text-gray-500">Voice calls will appear here once users start making calls.</p>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(VoiceRecordingsPage);
