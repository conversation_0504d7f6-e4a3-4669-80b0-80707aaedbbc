import { Request, Response, NextFunction } from 'express';
import * as jwt from 'jsonwebtoken';
import Admin, { IAdmin } from '../models/Admin';
import User, { IUser } from '../models/User';
import TokenBlacklist from '../models/TokenBlacklist';
import DeviceModel from '../models/Device';
import AuditLogModel from '../models/AuditLog';
import * as crypto from 'crypto';
import { generateChallenge } from '../utils/ppk';
import * as winston from 'winston';

// Create dedicated logger for auth middleware
const authLogger = winston.createLogger({
  level: 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return `${timestamp} [${level.toUpperCase()}] 🔐 ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: './logs/auth.log' })
  ]
});

// Extend Request interface to include user/admin data and PPK challenge
declare global {
  namespace Express {
    interface Request {
      user?: IUser;
      admin?: IAdmin;
      ppkChallenge?: string;
      device?: any; // Device information from fingerprinting
    }
  }
}

// PPK authentication middleware for admin routes
export async function ppkAuth(req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    const admin = req.admin;

    // Check if PPK auth is required
    if (!admin || !admin.ppkEnabled || admin.authMethod === 'password') {
      next();
      return;
    }

    const { signature } = req.body;
    const challenge = req.headers['x-ppk-challenge'];

    if (!signature || !challenge) {
      res.status(401).json({ 
        error: 'PPK authentication required',
        challenge: generateChallenge() // Generate new challenge for the client
      });
      return;
    }

    // Verify the PPK signature
    if (!admin.verifyPPKSignature(challenge as string, signature)) {
      res.status(401).json({ 
        error: 'Invalid PPK signature',
        challenge: generateChallenge() // Generate new challenge for retry
      });
      return;
    }

    next();
  } catch (error) {
    res.status(500).json({ error: 'PPK authentication error' });
  }
}

// Crypto helper for PPK validation
async function validatePPK(req: Request, encryptedData: string, adminPPKHash: string): Promise<boolean> {
  try {
    const deviceId = req.headers['x-device-id'] as string;
    
    if (!deviceId) {
      return false;
    }

    // Implement PPK validation logic here
    // This is a placeholder - actual implementation would depend on your PPK requirements
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Enhanced token authentication with device verification
 */
export async function authenticateToken(req: Request, res: Response, next: NextFunction): Promise<void> {
  // Add basic console.log to ensure this runs even if Winston fails
  console.log('🔐 AUTH MIDDLEWARE ENTRY POINT - This should always appear');
  
  authLogger.info('Starting authentication for request', { 
    method: req.method, 
    path: req.path,
    userAgent: req.headers['user-agent']
  });
  
  try {
    // Try to get token from Authorization header or admin-token cookie
    let token: string | undefined = undefined;
    const authHeader = req.headers['authorization'];
    authLogger.debug('Checking authorization header', { authHeader });
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
      authLogger.info('Found Bearer token in Authorization header');
    } else if (req.cookies && req.cookies['admin-token']) {
      token = req.cookies['admin-token'];
      authLogger.info('Found admin-token cookie');
    }          if (!token) {
        authLogger.warn('No token found in request');
        res.status(401).json({
          success: false,
          error: 'Access token required - UPDATED TEST',
          code: 'NO_TOKEN'
        });
        return;
      }

    // Check if token is blacklisted
    const blacklistedToken = await TokenBlacklist.findOne({ token });
    if (blacklistedToken) {      authLogger.warn('Blacklisted token used', { tokenPrefix: token.substring(0, 10) + '...' });
      await AuditLogModel.create({
        logId: `blacklist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        event: {
          type: 'security_violation',
          action: 'blacklisted_token_used',
          result: 'blocked',
          severity: 'high'
        },
        context: {
          ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.path,
          method: req.method
        },
        compliance: {
          category: 'security',
          retention: 'long',
          piiIncluded: false,
          sensitiveData: true,
          exportable: false
        },
        data: {
          metadata: { 
            token: token.substring(0, 10) + '...', 
            endpoint: req.path 
          }
        }
      });

      res.status(401).json({
        success: false,
        error: 'Token has been revoked',
        code: 'TOKEN_BLACKLISTED'
      });
      return;
    }    
    
    // Verify token
    authLogger.debug('Verifying JWT token...');
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    authLogger.info('Token verified successfully', { 
      decoded: {
        type: decoded.type,
        adminId: decoded.adminId,
        username: decoded.username,
        role: decoded.role,
        exp: decoded.exp,
        iat: decoded.iat
      }
    });      // Check if user or admin
    if (decoded.type === 'admin') {
      // Get admin ID from the token - support multiple field names for compatibility
      const adminId = decoded.adminId || decoded.id || decoded._id;
      
      // Debug token contents
      authLogger.info('Processing admin token', { adminId, username: decoded.username });      // Try to find admin by ID
      authLogger.info('Looking for admin in database', { adminId, adminIdType: typeof adminId });
      const admin = await Admin.findById(adminId).select('-privateKey');
      authLogger.info('Admin lookup result', { admin: admin ? 'found' : 'not found', adminId });
      
      if (!admin) {
        authLogger.error('Admin not found in database', { adminId });
        // Let's also try to see all admins to debug
        const allAdmins = await Admin.find({}, { username: 1, _id: 1, isActive: 1 });
        authLogger.error('All admins in database', { allAdmins });
        
        res.status(401).json({
          success: false,
          error: 'Admin account not found or inactive',
          code: 'ADMIN_INACTIVE',
          debug: process.env.NODE_ENV === 'development' ? { adminId, tokenData: decoded, allAdmins } : undefined
        });
        return;
      }
        if (!admin.isActive) {
        authLogger.warn('Admin account is inactive', { adminId, username: admin.username });
        res.status(401).json({
          success: false,
          error: 'Admin account not found or inactive',
          code: 'ADMIN_INACTIVE',
          debug: process.env.NODE_ENV === 'development' ? { adminId, isActive: admin.isActive, username: admin.username } : undefined
        });
        return;
      }
      
      authLogger.info('Admin authentication successful', { adminId, username: admin.username });
      req.admin = admin;    } else {
      // Get user ID from token - support multiple field names for compatibility
      const userId = decoded.userId || decoded.id || decoded._id;
      
      authLogger.info('Processing user token', { userId, username: decoded.username });
      
      const user = await User.findById(userId);
      if (!user || user.status !== 'active') {
        authLogger.warn('User not found or inactive', { userId, userStatus: user?.status });
        res.status(401).json({
          success: false,
          error: 'User account not found or inactive',
          code: 'USER_INACTIVE'
        });
        return;
      }
      req.user = user;

      // Update last activity
      await User.findByIdAndUpdate(userId, {
        lastActivity: new Date()
      });
    }    authLogger.info('Authentication successful, calling next middleware');
    next();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    authLogger.error('Authentication error', { error: errorMessage, stack: errorStack });
    if ((error as any).name === 'TokenExpiredError') {
      res.status(401).json({
        success: false,
        error: 'Token has expired',
        code: 'TOKEN_EXPIRED'
      });
    } else if ((error as any).name === 'JsonWebTokenError') {
      res.status(401).json({
        success: false,
        error: 'Invalid token',
        code: 'INVALID_TOKEN'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Authentication error',
        code: 'AUTH_ERROR'
      });
    }
  }
}

/**
 * Device verification middleware for sensitive operations
 */
export async function verifyDevice(req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    const { deviceId } = req.body;
    const userId = req.user?.id;

    if (!deviceId || !userId) {
      res.status(400).json({
        success: false,
        error: 'Device ID required for this operation',
        code: 'DEVICE_REQUIRED'
      });
      return;
    }

    const device = await DeviceModel.findOne({
      _id: deviceId,
      userId,
      isVerified: true,
      status: 'active'
    });    if (!device) {
      await AuditLogModel.create({
        logId: `device_unverified_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        event: {
          type: 'security_violation',
          action: 'unverified_device_access',
          result: 'blocked',
          severity: 'medium'
        },
        context: {
          ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.path,
          method: req.method
        },
        compliance: {
          category: 'security',
          retention: 'medium',
          piiIncluded: false,
          sensitiveData: false,
          exportable: true
        },
        data: {
          metadata: { 
            deviceId,
            endpoint: req.path
          }
        }
      });

      res.status(403).json({
        success: false,
        error: 'Device not verified or inactive',
        code: 'DEVICE_NOT_VERIFIED'
      });
      return;
    }

    // Update device last seen
    await DeviceModel.findByIdAndUpdate(deviceId, {
      lastSeen: new Date()
    });

    req.device = device;
    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Device verification error',
      code: 'DEVICE_VERIFICATION_ERROR'
    });
  }
}

// Admin authorization middleware
export function requireAdmin(req: Request, res: Response, next: NextFunction): void {
  if (req.user) {
    res.status(403).json({ error: 'User access not allowed for this endpoint' });
    return;
  }
  
  if (!req.admin) {
    res.status(401).json({ error: 'Admin authentication required' });
    return;
  }
  
  next();
}

// Middleware to ensure superadmin access
export function superAdminOnly(req: Request, res: Response, next: NextFunction): void {
  if (!req.admin || req.admin.role !== 'superadmin') {
    res.status(403).json({ error: 'Superadmin access required' });
    return;
  }
  next();
}

// Middleware to check PPK requirement
export function ppkRequired(req: Request, res: Response, next: NextFunction): void {
  const admin = req.admin;
  if (!admin || !admin.ppkEnabled) {
    res.status(403).json({ error: 'PPK authentication required for this resource' });
    return;
  }
  next();
}

// Alias for authenticateToken for backward compatibility
export const auth = authenticateToken;

// Middleware to require user authentication (non-admin)
export function requireUser(req: Request, res: Response, next: NextFunction): void {
  if (req.admin) {
    res.status(403).json({ error: 'Admin access not allowed for this endpoint' });
    return;
  }
  
  if (!req.user) {
    res.status(401).json({ error: 'User authentication required' });
    return;
  }
  
  next();
}
