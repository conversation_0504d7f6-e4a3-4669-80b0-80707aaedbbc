#!/usr/bin/env node

/**
 * Debug Metro Resolution Script
 * Helps identify Metro bundler resolution issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Metro Resolution Debug Information');
console.log('=====================================');

// Check current working directory
console.log('📁 Current Directory:', process.cwd());
console.log('📱 App Directory:', __dirname);

// Check if we're in the right directory
const expectedFiles = ['index.js', 'package.json', 'app.config.js', 'src/App.tsx'];
console.log('\n📋 Expected Files Check:');
expectedFiles.forEach(file => {
  const fullPath = path.join(__dirname, file);
  const exists = fs.existsSync(fullPath);
  console.log(`  ${exists ? '✅' : '❌'} ${file} - ${exists ? 'EXISTS' : 'MISSING'}`);
  if (exists && file === 'index.js') {
    console.log(`     Content preview: ${fs.readFileSync(fullPath, 'utf8').substring(0, 100)}...`);
  }
});

// Check package.json main field
console.log('\n📦 Package.json Main Field:');
const packageJsonPath = path.join(__dirname, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  console.log(`  Main: ${packageJson.main || 'NOT SET'}`);
  console.log(`  Name: ${packageJson.name || 'NOT SET'}`);
} else {
  console.log('  ❌ package.json not found');
}

// Check app.json
console.log('\n📱 App.json Check:');
const appJsonPath = path.join(__dirname, 'app.json');
if (fs.existsSync(appJsonPath)) {
  const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
  console.log(`  Name: ${appJson.name || 'NOT SET'}`);
  console.log(`  Display Name: ${appJson.displayName || 'NOT SET'}`);
} else {
  console.log('  ❌ app.json not found');
}

// Check metro.config.js
console.log('\n🚇 Metro Config Check:');
const metroConfigPath = path.join(__dirname, 'metro.config.js');
if (fs.existsSync(metroConfigPath)) {
  console.log('  ✅ metro.config.js exists');
  console.log('     Preview:', fs.readFileSync(metroConfigPath, 'utf8').substring(0, 200) + '...');
} else {
  console.log('  ❌ metro.config.js not found');
}

// Check node_modules
console.log('\n📚 Node Modules Check:');
const nodeModulesPath = path.join(__dirname, 'node_modules');
console.log(`  ${fs.existsSync(nodeModulesPath) ? '✅' : '❌'} node_modules exists`);

// Check if running from workspace root by mistake
console.log('\n🏗️  Workspace Root Check:');
const workspaceRootMarkers = ['../backend', '../frontend', '../package.json'];
workspaceRootMarkers.forEach(marker => {
  const exists = fs.existsSync(path.join(__dirname, marker));
  console.log(`  ${exists ? '✅' : '❌'} ${marker} - ${exists ? 'WORKSPACE DETECTED' : 'NOT FOUND'}`);
});

console.log('\n💡 Recommended Actions:');
console.log('  1. Ensure you run expo start from the app/ directory');
console.log('  2. Clear Metro cache: npx expo start --clear');
console.log('  3. Delete node_modules and reinstall: rm -rf node_modules && npm install');
console.log('  4. Check that index.js imports are correct');

console.log('\n🚀 To start properly, run:');
console.log(`  cd ${__dirname}`);
console.log('  npm run start:clear');
