# Metro Resolution Error Fix Guide

## Problem
Error: `Unable to resolve module ./app/index from D:\Development\Freelance\HT\CCALC\app\..:`

## Root Cause
Metro bundler is trying to resolve `./app/index` instead of `./index.js`. This happens when:
1. Metro is running from the wrong directory (workspace root instead of app directory)
2. Entry point configuration is incorrect
3. Metro cache contains stale resolution data

## Quick Fix Steps

### Step 1: Ensure Correct Directory
```bash
# Make sure you're in the app directory
cd D:\Development\Freelance\HT\CCALC\app
pwd  # Should show the app directory path
```

### Step 2: Clear All Caches
```bash
# Clear Metro cache
npx expo start --clear

# Or clear React Native cache
npx react-native start --reset-cache

# Clear Expo cache
rm -rf .expo
rm -rf node_modules/.cache
```

### Step 3: Use Correct Start Command
```bash
# From the app directory, run:
npm run start:clear

# Or directly:
npx expo start --clear --port 8082
```

### Step 4: Verify Configuration
Check these files:

**package.json** - Should have:
```json
{
  "main": "index.js"
}
```

**app.config.js** - Should have:
```javascript
{
  expo: {
    entryPoint: "./index.js"
  }
}
```

**index.js** - Should exist and contain:
```javascript
import {AppRegistry} from 'react-native';
import App from './src/App.tsx';
import {name as appName} from './app.json';

AppRegistry.registerComponent(appName, () => App);
```

## Advanced Troubleshooting

### Option 1: Use Test Entry Point
Temporarily replace `package.json` main field:
```json
{
  "main": "index.test.js"
}
```

Then test with the minimal entry point to isolate the issue.

### Option 2: Manual Metro Start
```bash
# Force Metro to use correct project root
npx expo start --clear --port 8082 --project-root ./
```

### Option 3: Complete Reset
```bash
# Delete all caches and reinstall
rm -rf node_modules
rm -rf .expo
rm -rf ~/.expo  # Clear global Expo cache
npm install
npx expo start --clear
```

### Option 4: Debug Metro Configuration
Run the debug script:
```bash
npm run start:debug
```

## Platform-Specific Notes

### iOS (Expo Go)
- Make sure Expo Go app is updated
- Try switching between LAN and Tunnel connection
- Clear Expo Go cache in the app settings

### Android
- Check that Metro bundler is accessible on port 8082
- Verify network connectivity between device and development machine

## Scripts Available

- `npm run start` - Start with automatic directory detection
- `npm run start:clear` - Start with cleared cache
- `npm run start:debug` - Run debug diagnostics
- `npm run fix:metro` - Run automated fix script

## Expected Working State

When fixed, you should see:
1. Metro bundler starts without errors
2. Bundle builds successfully
3. App loads in Expo Go without resolution errors
4. No "Unable to resolve module" messages

## If Nothing Works

1. Create a fresh Expo project and compare configurations
2. Check if the issue persists with `expo init` template
3. Verify that Expo CLI and related tools are up to date:
   ```bash
   npm install -g @expo/cli@latest
   ```

## Prevention

Always run Expo commands from the correct directory:
```bash
# Good ✅
cd D:\Development\Freelance\HT\CCALC\app
npm start

# Bad ❌ 
cd D:\Development\Freelance\HT\CCALC
npx expo start app  # This might cause resolution issues
```
