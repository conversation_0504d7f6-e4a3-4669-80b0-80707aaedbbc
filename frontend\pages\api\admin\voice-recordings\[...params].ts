// Next.js API route to proxy admin voice recording requests to backend
import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';
  
  // Reconstruct the full path from params
  const { params } = req.query;
  const pathSegments = Array.isArray(params) ? params : [params];
  const fullPath = pathSegments.join('/');
  
  const url = `${backendUrl}/api/admin/voice-recordings/${fullPath}`;

  try {
    const response = await axios({
      method: req.method,
      url,
      headers: {
        ...req.headers,
        host: undefined, // Remove host header for backend
      },
      data: req.body,
      params: req.query.params ? undefined : req.query, // Avoid double params
      validateStatus: () => true,
      responseType: req.method === 'GET' && fullPath.includes('/download') ? 'stream' : 'json',
    });
    
    if (req.method === 'GET' && fullPath.includes('/download')) {
      // Handle file download
      res.setHeader('Content-Type', response.headers['content-type'] || 'audio/wav');
      res.setHeader('Content-Disposition', response.headers['content-disposition'] || 'attachment');
      response.data.pipe(res);
    } else {
      res.status(response.status).json(response.data);
    }
  } catch (error: any) {
    console.error('Admin voice recording proxy error:', error.message);
    res.status(500).json({ 
      error: 'Proxy error', 
      details: error.message,
      url: url.replace(process.env.NEXT_PUBLIC_BACKEND_URL || '', '[BACKEND_URL]')
    });
  }
}
