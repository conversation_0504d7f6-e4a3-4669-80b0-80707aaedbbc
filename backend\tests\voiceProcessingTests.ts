/**
 * Voice Processing Test Suite
 * Comprehensive tests for voice processing pipeline with WORLD vocoder
 */

import { worldVocoderService, WORLD_VOICE_PROFILES } from '../services/worldVocoderService';
import { VoiceModulationService } from '../services/voiceModulation';
import { realTimeVoiceStreamingService } from '../services/realTimeVoiceStreaming';
import { voiceSecurityService } from '../services/voiceSecurityService';
import { performanceMonitor } from '../services/performanceMonitor';



export class VoiceProcessingTestSuite {
    private testResults: any[] = [];

    async runAllTests(): Promise<boolean> {
        console.log('🧪 Starting Comprehensive Voice Processing Test Suite...');

        const tests = [
            this.testWorldVocoderInitialization,
            this.testVoiceProfileProcessing,
            this.testLatencyRequirements,
            this.testAudioQuality,
            this.testSecurityFeatures,
            this.testRealTimeStreaming
        ];

        let allPassed = true;

        for (const test of tests) {
            try {
                const result = await test.call(this);
                this.testResults.push(result);
                if (!result.passed) {
                    allPassed = false;
                }
                console.log(`${result.passed ? '✅' : '❌'} ${result.testName}`);
            } catch (error) {
                console.error(`Test failed with error:`, error);
                this.testResults.push({
                    testName: test.name,
                    passed: false,
                    error: (error as Error).message
                });
                allPassed = false;
            }
        }

        console.log(`🧪 Test Suite Complete. All tests passed: ${allPassed}`);
        return allPassed;
    }

    private async testWorldVocoderInitialization(): Promise<any> {
        const sessionId = await worldVocoderService.createSession('test-user', {
            sampleRate: 48000,
            frameSize: 960,
            qualityLevel: 'balanced'
        });

        const session = worldVocoderService.getSessionStats(sessionId);
        const passed = session !== null;

        if (sessionId) {
            worldVocoderService.destroySession(sessionId);
        }

        return {
            testName: 'WORLD Vocoder Initialization',
            passed,
            details: { sessionId, sessionExists: !!session }
        };
    }

    private async testVoiceProfileProcessing(): Promise<any> {
        const testAudio = new Float32Array(960);
        for (let i = 0; i < testAudio.length; i++) {
            testAudio[i] = Math.sin(2 * Math.PI * 440 * i / 48000) * 0.5;
        }

        const sessionId = await worldVocoderService.createSession('test-user', {
            sampleRate: 48000,
            frameSize: 960,
            qualityLevel: 'balanced'
        });

        let passed = false;
        let processedAudio: Float32Array | null = null;

        if (sessionId) {
            const session = worldVocoderService.getSession(sessionId);
            if (session) {
                processedAudio = await session.processFrame(testAudio, WORLD_VOICE_PROFILES.SECURE_DEEP_MALE);
                passed = processedAudio !== null && processedAudio.length === testAudio.length;
            }
            worldVocoderService.destroySession(sessionId);
        }

        return {
            testName: 'Voice Profile Processing',
            passed,
            details: {
                inputLength: testAudio.length,
                outputLength: processedAudio?.length || 0,
                profileUsed: 'SECURE_DEEP_MALE'
            }
        };
    }

    private async testLatencyRequirements(): Promise<any> {
        const testAudio = new Float32Array(960);
        for (let i = 0; i < testAudio.length; i++) {
            testAudio[i] = Math.sin(2 * Math.PI * 440 * i / 48000) * 0.5;
        }

        const sessionId = await worldVocoderService.createSession('test-user', {
            sampleRate: 48000,
            frameSize: 960,
            qualityLevel: 'balanced'
        });

        let passed = false;
        let latency = 0;

        if (sessionId) {
            const session = worldVocoderService.getSession(sessionId);
            if (session) {
                const startTime = Date.now();
                await session.processFrame(testAudio, WORLD_VOICE_PROFILES.SECURE_DEEP_MALE);
                latency = Date.now() - startTime;

                passed = latency < 50;
            }
            worldVocoderService.destroySession(sessionId);
        }

        return {
            testName: 'Latency Requirements',
            passed,
            details: {
                latency,
                requirement: '< 50ms',
                frameSize: 960
            }
        };
    }

    private async testAudioQuality(): Promise<any> {
        const testAudio = new Float32Array(960);
        for (let i = 0; i < testAudio.length; i++) {
            testAudio[i] =
                Math.sin(2 * Math.PI * 220 * i / 48000) * 0.3 +
                Math.sin(2 * Math.PI * 440 * i / 48000) * 0.3 +
                Math.sin(2 * Math.PI * 880 * i / 48000) * 0.2;
        }

        const sessionId = await worldVocoderService.createSession('test-user', {
            sampleRate: 48000,
            frameSize: 960,
            qualityLevel: 'high'
        });

        let passed = false;
        let qualityScore = 0;

        if (sessionId) {
            const session = worldVocoderService.getSession(sessionId);
            if (session) {
                const processedAudio = await session.processFrame(testAudio, WORLD_VOICE_PROFILES.SECURE_DEEP_MALE);

                if (processedAudio) {
                    const maxAmplitude = Math.max(...Array.from(processedAudio as Float32Array).map((x: number) => Math.abs(x)));
                    const avgAmplitude = Array.from(processedAudio as Float32Array).reduce((sum: number, val: number) => sum + Math.abs(val), 0) / processedAudio.length;

                    qualityScore = (maxAmplitude > 0.1 && maxAmplitude < 0.95 && avgAmplitude > 0.01) ? 85 : 30;
                    passed = qualityScore > 70;
                }
            }
            worldVocoderService.destroySession(sessionId);
        }

        return {
            testName: 'Audio Quality',
            passed,
            details: {
                qualityScore,
                requirement: '> 70',
                testType: 'amplitude_analysis'
            }
        };
    }

    private async testSecurityFeatures(): Promise<any> {
        const testAudio = new Float32Array(960);
        for (let i = 0; i < testAudio.length; i++) {
            testAudio[i] = Math.sin(2 * Math.PI * 440 * i / 48000) * 0.5;
        }

        const securitySessionId = await voiceSecurityService.createSecuritySession('test-user', {
            antiForensicLevel: 'high',
            glottalDestruction: true,
            formantScrambling: true,
            originalWaveformPurge: true
        });

        let passed = false;
        let securityMetrics = null;

        if (securitySessionId) {
            const securityResult = await voiceSecurityService.applySecurityTransformations(
                securitySessionId,
                testAudio
            );

            if (securityResult) {
                securityMetrics = voiceSecurityService.getSecurityMetrics(securitySessionId);
                const verificationResult = await voiceSecurityService.verifyAudioSecurity(
                    securitySessionId,
                    securityResult.securedAudio,
                    securityResult.securityMetadata
                );

                passed = (securityMetrics?.forensicResistance || 0) > 80 &&
                    verificationResult?.isSecure &&
                    (verificationResult?.reversibilityRisk || 1) < 0.1;
            }

            voiceSecurityService.destroySecuritySession(securitySessionId);
        }

        return {
            testName: 'Security Features',
            passed,
            details: {
                forensicResistance: securityMetrics?.forensicResistance || 0,
                securityLevel: securityMetrics?.securityLevel || 'unknown'
            }
        };
    }

    private async testRealTimeStreaming(): Promise<any> {
        let passed = false;
        let callId = '';

        try {
            callId = await realTimeVoiceStreamingService.initiateCall(
                'test-user-1',
                'test-user-2',
                'SECURE_DEEP_MALE',
                'SECURE_HIGH_FEMALE'
            );

            // No getCallSession method, just check callId exists
            passed = !!callId;

            if (callId) {
                await realTimeVoiceStreamingService.endCall(callId, 'test-user-1');
            }
        } catch (error) {
            console.error('Real-time streaming test error:', error);
            passed = false;
        }

        return {
            testName: 'Real-Time Streaming',
            passed,
            details: {
                callId,
                testType: 'call_initiation'
            }
        };
    }

    getTestResults(): any[] {
        return [...this.testResults];
    }

    generateTestReport(): string {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(result => result.passed).length;

        let report = `\n🧪 COMPREHENSIVE WORLD VOCODER TEST REPORT\n`;
        report += `=============================================\n`;
        report += `Total Tests: ${totalTests}\n`;
        report += `Passed: ${passedTests}\n`;
        report += `Failed: ${totalTests - passedTests}\n`;
        report += `Success Rate: ${Math.round((passedTests / totalTests) * 100)}%\n\n`;

        this.testResults.forEach(result => {
            report += `${result.passed ? '✅' : '❌'} ${result.testName}\n`;
            if (result.details) {
                Object.entries(result.details).forEach(([key, value]) => {
                    report += `   ${key}: ${value}\n`;
                });
            }
            if (result.error) {
                report += `   Error: ${result.error}\n`;
            }
            report += '\n';
        });

        return report;
    }
}
