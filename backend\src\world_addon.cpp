#include <napi.h>
#include "world_processor.h"
#include "world_wrapper.h"

/**
 * WORLD Vocoder Node.js Native Addon
 * Real-time voice transformation with non-reversible morphing
 * Designed for <100ms latency voice calls
 */

namespace WorldVocoder
{

    // Initialize the WORLD processor
    Napi::Value InitializeProcessor(const Napi::CallbackInfo &info)
    {
        Napi::Env env = info.Env();

        if (info.Length() < 1 || !info[0].IsObject())
        {
            Napi::TypeError::New(env, "Expected configuration object").ThrowAsJavaScriptException();
            return env.Null();
        }

        Napi::Object config = info[0].As<Napi::Object>();

        // Extract configuration parameters
        int sampleRate = config.Get("sampleRate").As<Napi::Number>().Int32Value();
        int frameSize = config.Get("frameSize").As<Napi::Number>().Int32Value();
        bool realTimeMode = config.Get("realTimeMode").As<Napi::Boolean>().Value();

        try
        {
            WorldProcessor *processor = new WorldProcessor(sampleRate, frameSize, realTimeMode);

            // Return processor instance wrapped in external
            return Napi::External<WorldProcessor>::New(env, processor);
        }
        catch (const std::exception &e)
        {
            Napi::Error::New(env, std::string("Failed to initialize WORLD processor: ") + e.what())
                .ThrowAsJavaScriptException();
            return env.Null();
        }
    }

    // Process audio frame with voice morphing
    Napi::Value ProcessAudioFrame(const Napi::CallbackInfo &info)
    {
        Napi::Env env = info.Env();

        if (info.Length() < 3)
        {
            Napi::TypeError::New(env, "Expected processor, audio buffer, and morph profile")
                .ThrowAsJavaScriptException();
            return env.Null();
        }

        // Get processor instance
        WorldProcessor *processor = info[0].As<Napi::External<WorldProcessor>>().Data();

        // Get audio buffer
        Napi::Float32Array audioBuffer = info[1].As<Napi::Float32Array>();
        float *audioData = audioBuffer.Data();
        size_t audioLength = audioBuffer.ElementLength();

        // Get morph profile
        Napi::Object morphProfile = info[2].As<Napi::Object>();

        VoiceMorphProfile profile;
        profile.pitchScale = morphProfile.Get("pitchScale").As<Napi::Number>().FloatValue();
        profile.spectralWarp = morphProfile.Get("spectralWarp").As<Napi::Number>().FloatValue();
        profile.reverbAmount = morphProfile.Get("reverbAmount").As<Napi::Number>().FloatValue();
        profile.eqTilt = morphProfile.Get("eqTilt").As<Napi::Number>().FloatValue();
        profile.temporalJitter = morphProfile.Get("temporalJitter").As<Napi::Number>().FloatValue();
        profile.spectralNoise = morphProfile.Get("spectralNoise").As<Napi::Number>().FloatValue();
        profile.antiForensic = morphProfile.Get("antiForensic").As<Napi::Boolean>().Value();

        try
        {
            // Process audio frame
            std::vector<float> outputAudio = processor->ProcessFrame(audioData, audioLength, profile);

            // Create output buffer
            Napi::Float32Array result = Napi::Float32Array::New(env, outputAudio.size());
            float *resultData = result.Data();

            // Copy processed audio
            std::copy(outputAudio.begin(), outputAudio.end(), resultData);

            return result;
        }
        catch (const std::exception &e)
        {
            Napi::Error::New(env, std::string("Audio processing failed: ") + e.what())
                .ThrowAsJavaScriptException();
            return env.Null();
        }
    }

    // Extract WORLD features (F0, spectral envelope, aperiodicity)
    Napi::Value ExtractWorldFeatures(const Napi::CallbackInfo &info)
    {
        Napi::Env env = info.Env();

        if (info.Length() < 2)
        {
            Napi::TypeError::New(env, "Expected processor and audio buffer")
                .ThrowAsJavaScriptException();
            return env.Null();
        }

        WorldProcessor *processor = info[0].As<Napi::External<WorldProcessor>>().Data();
        Napi::Float32Array audioBuffer = info[1].As<Napi::Float32Array>();

        try
        {
            WorldFeatures features = processor->ExtractFeatures(
                audioBuffer.Data(),
                audioBuffer.ElementLength());

            // Create result object
            Napi::Object result = Napi::Object::New(env);

            // F0 array
            Napi::Float64Array f0Array = Napi::Float64Array::New(env, features.f0.size());
            double *f0Data = f0Array.Data();
            std::copy(features.f0.begin(), features.f0.end(), f0Data);
            result.Set("f0", f0Array);

            // Spectral envelope (2D array flattened)
            size_t spectralSize = features.spectralEnvelope.size() * features.spectralEnvelope[0].size();
            Napi::Float64Array spectralArray = Napi::Float64Array::New(env, spectralSize);
            double *spectralData = spectralArray.Data();

            size_t idx = 0;
            for (const auto &frame : features.spectralEnvelope)
            {
                std::copy(frame.begin(), frame.end(), spectralData + idx);
                idx += frame.size();
            }
            result.Set("spectralEnvelope", spectralArray);
            result.Set("spectralFrames", Napi::Number::New(env, features.spectralEnvelope.size()));
            result.Set("spectralBins", Napi::Number::New(env, features.spectralEnvelope[0].size()));

            // Aperiodicity (2D array flattened)
            size_t aperiodicitySize = features.aperiodicity.size() * features.aperiodicity[0].size();
            Napi::Float64Array aperiodicityArray = Napi::Float64Array::New(env, aperiodicitySize);
            double *aperiodicityData = aperiodicityArray.Data();

            idx = 0;
            for (const auto &frame : features.aperiodicity)
            {
                std::copy(frame.begin(), frame.end(), aperiodicityData + idx);
                idx += frame.size();
            }
            result.Set("aperiodicity", aperiodicityArray);

            return result;
        }
        catch (const std::exception &e)
        {
            Napi::Error::New(env, std::string("Feature extraction failed: ") + e.what())
                .ThrowAsJavaScriptException();
            return env.Null();
        }
    }

    // Synthesize audio from WORLD features
    Napi::Value SynthesizeAudio(const Napi::CallbackInfo &info)
    {
        Napi::Env env = info.Env();

        if (info.Length() < 2)
        {
            Napi::TypeError::New(env, "Expected processor and features object")
                .ThrowAsJavaScriptException();
            return env.Null();
        }

        WorldProcessor *processor = info[0].As<Napi::External<WorldProcessor>>().Data();
        Napi::Object features = info[1].As<Napi::Object>();

        try
        {
            // Extract features from JavaScript object
            WorldFeatures worldFeatures;

            // F0
            Napi::Float64Array f0Array = features.Get("f0").As<Napi::Float64Array>();
            worldFeatures.f0.assign(f0Array.Data(), f0Array.Data() + f0Array.ElementLength());

            // Spectral envelope
            Napi::Float64Array spectralArray = features.Get("spectralEnvelope").As<Napi::Float64Array>();
            int spectralFrames = features.Get("spectralFrames").As<Napi::Number>().Int32Value();
            int spectralBins = features.Get("spectralBins").As<Napi::Number>().Int32Value();

            worldFeatures.spectralEnvelope.resize(spectralFrames);
            for (int i = 0; i < spectralFrames; i++)
            {
                worldFeatures.spectralEnvelope[i].assign(
                    spectralArray.Data() + i * spectralBins,
                    spectralArray.Data() + (i + 1) * spectralBins);
            }

            // Aperiodicity
            Napi::Float64Array aperiodicityArray = features.Get("aperiodicity").As<Napi::Float64Array>();
            worldFeatures.aperiodicity.resize(spectralFrames);
            for (int i = 0; i < spectralFrames; i++)
            {
                worldFeatures.aperiodicity[i].assign(
                    aperiodicityArray.Data() + i * spectralBins,
                    aperiodicityArray.Data() + (i + 1) * spectralBins);
            }

            // Synthesize audio
            std::vector<float> synthesizedAudio = processor->SynthesizeAudio(worldFeatures);

            // Create output buffer
            Napi::Float32Array result = Napi::Float32Array::New(env, synthesizedAudio.size());
            float *resultData = result.Data();
            std::copy(synthesizedAudio.begin(), synthesizedAudio.end(), resultData);

            return result;
        }
        catch (const std::exception &e)
        {
            Napi::Error::New(env, std::string("Audio synthesis failed: ") + e.what())
                .ThrowAsJavaScriptException();
            return env.Null();
        }
    }

    // Cleanup processor
    Napi::Value CleanupProcessor(const Napi::CallbackInfo &info)
    {
        Napi::Env env = info.Env();

        if (info.Length() < 1)
        {
            Napi::TypeError::New(env, "Expected processor instance").ThrowAsJavaScriptException();
            return env.Null();
        }

        WorldProcessor *processor = info[0].As<Napi::External<WorldProcessor>>().Data();
        delete processor;

        return env.Null();
    }

    // Module initialization
    Napi::Object Init(Napi::Env env, Napi::Object exports)
    {
        exports.Set(Napi::String::New(env, "initializeProcessor"),
                    Napi::Function::New(env, InitializeProcessor));
        exports.Set(Napi::String::New(env, "processAudioFrame"),
                    Napi::Function::New(env, ProcessAudioFrame));
        exports.Set(Napi::String::New(env, "extractWorldFeatures"),
                    Napi::Function::New(env, ExtractWorldFeatures));
        exports.Set(Napi::String::New(env, "synthesizeAudio"),
                    Napi::Function::New(env, SynthesizeAudio));
        exports.Set(Napi::String::New(env, "cleanupProcessor"),
                    Napi::Function::New(env, CleanupProcessor));

        return exports;
    }

} // namespace WorldVocoder

Napi::Object Init(Napi::Env env, Napi::Object exports)
{
    return WorldVocoder::Init(env, exports);
}

NODE_API_MODULE(world_vocoder, Init)
