// Test script to verify voice modulation API
const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');

async function testVoiceModulation() {
  try {
    console.log('Testing voice modulation API...');

    // Create a simple test audio file (silent WAV)
    const testAudioBuffer = createSilentWavBuffer(1000); // 1 second of silence
    
    const form = new FormData();
    form.append('audio', testAudioBuffer, {
      filename: 'test.wav',
      contentType: 'audio/wav'
    });
    form.append('profileName', 'SECURE_MALE');

    const response = await axios.post('http://localhost:3000/api/voice/test-modulation', form, {
      headers: {
        ...form.getHeaders(),
        'Authorization': 'Bearer test-token' // Replace with actual admin token
      },
      responseType: 'arraybuffer'
    });

    console.log('Response status:', response.status);
    console.log('Response size:', response.data.length);
    console.log('Content-Type:', response.headers['content-type']);

    // Save the result for inspection
    fs.writeFileSync('./test-output.wav', response.data);
    console.log('Saved output to test-output.wav');

  } catch (error) {
    console.error('Test failed:', error.response?.data || error.message);
  }
}

function createSilentWavBuffer(durationMs) {
  const sampleRate = 44100;
  const samples = Math.floor(sampleRate * durationMs / 1000);
  const buffer = Buffer.alloc(44 + samples * 2);
  
  // WAV header
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(36 + samples * 2, 4);
  buffer.write('WAVE', 8);
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16);
  buffer.writeUInt16LE(1, 20);
  buffer.writeUInt16LE(1, 22);
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(sampleRate * 2, 28);
  buffer.writeUInt16LE(2, 32);
  buffer.writeUInt16LE(16, 34);
  buffer.write('data', 36);
  buffer.writeUInt32LE(samples * 2, 40);
  
  // Silent audio data (all zeros)
  buffer.fill(0, 44);
  
  return buffer;
}

testVoiceModulation();
