/**
 * iOS-only Chat UI Styles
 * WhatsApp-like instant media preview with iOS-native design
 * Optimized for iPhone experience with swipe-to-reply and upload progress
 */

import { StyleSheet } from 'react-native';

// Modern, minimal color palette - Enhanced for premium feel
export const chatColors = {
  // Backgrounds
  chatBackground: '#FAFBFC', // Softer, more premium background
  headerBackground: '#FFFFFF',
  inputBackground: '#FFFFFF',

  // Message bubbles - Enhanced with gradients
  userBubble: '#007AFF', // iOS blue
  userBubbleGradient: ['#007AFF', '#0056CC'], // Subtle gradient for depth
  receivedBubble: '#FFFFFF',

  // Text - Improved contrast and readability
  primaryText: '#1C1C1E', // Slightly softer than pure black
  secondaryText: 'rgba(28, 28, 30, 0.6)', // Better contrast
  userText: '#FFFFFF',
  timestamp: 'rgba(28, 28, 30, 0.5)',
  userTimestamp: 'rgba(255, 255, 255, 0.7)',

  // Borders and separators - More refined
  border: 'rgba(0, 0, 0, 0.06)',
  lightBorder: 'rgba(0, 0, 0, 0.03)',

  // Interactive elements
  accent: '#007AFF',
  accentLight: 'rgba(0, 122, 255, 0.08)',
  danger: '#FF3B30',

  // Status colors
  success: '#34C759',
  warning: '#FF9500',
};

// Typography scale following iOS Human Interface Guidelines (iOS-only app)
export const chatTypography = {
  // Font families - iOS system fonts
  systemFont: '-apple-system',
  
  // Font sizes - iOS HIG compliant
  title: 17,
  body: 16,
  caption: 14,
  small: 12,
  tiny: 11,
  
  // Font weights - iOS specific
  regular: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  
  // Line heights - iOS optimized
  titleLineHeight: 22,
  bodyLineHeight: 20,
  captionLineHeight: 18,
};

// Spacing system (multiples of 4)
export const chatSpacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

// Border radius system
export const chatRadius = {
  small: 6,
  medium: 12,
  large: 20,
  full: 50, // For circular elements
};

export const chatStyles = StyleSheet.create({
  // Layout
  container: {
    flex: 1,
    backgroundColor: chatColors.chatBackground,
  },
  
  // Reply Bar - WhatsApp-like reply preview
  replyBar: {
    backgroundColor: chatColors.inputBackground,
    paddingHorizontal: chatSpacing.lg,
    paddingVertical: chatSpacing.sm,
    borderTopWidth: 0.33,
    borderTopColor: chatColors.border,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  replyPreview: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  replyIndicator: {
    width: 3,
    height: 24,
    backgroundColor: chatColors.accent,
    borderRadius: 2,
    marginRight: chatSpacing.sm,
  },
  replyContent: {
    flex: 1,
  },
  replyAuthor: {
    fontSize: chatTypography.small,
    fontWeight: chatTypography.semibold,
    color: chatColors.accent,
    marginBottom: 2,
  },
  replyText: {
    fontSize: chatTypography.caption,
    color: chatColors.secondaryText,
  },
  replyCloseButton: {
    padding: chatSpacing.sm,
    marginLeft: chatSpacing.sm,
  },
  replyCloseText: {
    fontSize: 16,
    color: chatColors.secondaryText,
    fontWeight: chatTypography.medium,
  },
  
  // Delivery Status
  deliveryStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  deliveryIcon: {
    fontSize: 12,
    color: chatColors.userTimestamp,
    marginLeft: 4,
  },
  
  // Header - Enhanced modern style
  headerSafeArea: {
    backgroundColor: chatColors.headerBackground,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: chatSpacing.lg,
    paddingVertical: 16, // More generous padding
    borderBottomWidth: 0.5,
    borderBottomColor: chatColors.border,
    backgroundColor: chatColors.headerBackground,
    height: 64, // Slightly taller for better proportions
  },
  headerLeft: {
    flex: 1,
  },
  headerCenter: {
    flex: 2,
    alignItems: 'center',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  headerTitle: {
    fontSize: chatTypography.title,
    fontWeight: chatTypography.semibold,
    color: chatColors.primaryText,
    fontFamily: chatTypography.systemFont,
    letterSpacing: -0.24,
  },
  exitButton: {
    paddingVertical: chatSpacing.xs,
    paddingHorizontal: chatSpacing.xs,
  },
  exitText: {
    fontSize: chatTypography.body,
    color: chatColors.accent,
    fontFamily: chatTypography.systemFont,
    fontWeight: chatTypography.medium,
  },
  callButton: {
    padding: chatSpacing.xs,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: chatColors.accentLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  callButtonText: {
    fontSize: 18,
  },
  
  // Chat area
  chatContainer: {
    flex: 1,
    backgroundColor: chatColors.chatBackground,
  },
  messagesList: {
    flex: 1,
    backgroundColor: chatColors.chatBackground,
  },
  messagesContent: {
    paddingVertical: chatSpacing.lg,
    paddingBottom: chatSpacing.xxxl,
  },
  
  // Messages
  messageContainer: {
    paddingHorizontal: chatSpacing.lg,
    marginVertical: 1,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  receivedMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '78%', // Slightly wider for better content display
    paddingHorizontal: chatSpacing.lg,
    paddingVertical: 12, // More breathing room
    borderRadius: 20, // Smoother, more modern radius
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08, // Slightly more pronounced shadow
    shadowRadius: 4,
    elevation: 2,
  },
  userMessage: {
    backgroundColor: chatColors.userBubble,
    borderBottomRightRadius: 6, // Consistent with modern messaging apps
    shadowColor: chatColors.userBubble,
    shadowOpacity: 0.15,
  },
  receivedMessage: {
    backgroundColor: chatColors.receivedBubble,
    borderBottomLeftRadius: 6,
    borderWidth: 0.5, // Slightly more visible border
    borderColor: chatColors.border,
    shadowColor: '#000',
    shadowOpacity: 0.05,
  },
  messageText: {
    fontSize: chatTypography.body,
    lineHeight: chatTypography.bodyLineHeight,
    fontFamily: chatTypography.systemFont,
    fontWeight: chatTypography.regular,
  },
  userMessageText: {
    color: chatColors.userText,
  },
  receivedMessageText: {
    color: chatColors.primaryText,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: chatSpacing.xs,
  },
  timestamp: {
    fontSize: chatTypography.tiny,
    fontFamily: chatTypography.systemFont,
    fontWeight: chatTypography.regular,
  },
  userTimestamp: {
    color: chatColors.userTimestamp,
  },
  receivedTimestamp: {
    color: chatColors.timestamp,
  },
  
  // Input area
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: chatSpacing.lg,
    paddingVertical: 14, // More generous padding
    backgroundColor: chatColors.inputBackground,
    borderTopWidth: 0.5,
    borderTopColor: chatColors.border,
    minHeight: 72, // Slightly taller for better proportions
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 4,
  },
  inputSafeArea: {
    backgroundColor: chatColors.inputBackground,
  },
  textInputContainer: {
    flex: 1,
    backgroundColor: '#F7F8FA', // Subtle background differentiation
    borderRadius: 22, // More rounded for modern look
    paddingHorizontal: 18,
    paddingVertical: 12,
    marginRight: chatSpacing.md,
    minHeight: 44, // Larger touch target
    maxHeight: 120,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: chatColors.lightBorder,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.02,
    shadowRadius: 2,
    elevation: 1,
  },
  textInput: {
    fontSize: chatTypography.body,
    color: chatColors.primaryText,
    fontFamily: chatTypography.systemFont,
    textAlignVertical: 'center',
    // iOS-optimized padding (no Android adjustments needed)
    paddingTop: 0,
    paddingBottom: 0,
    lineHeight: chatTypography.bodyLineHeight,
    maxHeight: 120,
  },
  sendButton: {
    width: 44, // Larger touch target
    height: 44,
    borderRadius: 22, // Perfect circle
    backgroundColor: chatColors.accent,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: chatColors.accent,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25, // More pronounced shadow for depth
    shadowRadius: 4,
    elevation: 3,
    // Add subtle scale animation feel
    transform: [{ scale: 1 }],
  },
  sendButtonDisabled: {
    backgroundColor: 'rgba(0, 122, 255, 0.25)',
    shadowOpacity: 0.1,
    transform: [{ scale: 0.95 }], // Slightly smaller when disabled
  },
  sendButtonText: {
    fontSize: chatTypography.body,
    color: chatColors.userText,
    fontWeight: chatTypography.semibold,
  },
  attachmentButton: {
    width: 44, // Match send button size
    height: 44,
    borderRadius: 22, // Perfect circle
    backgroundColor: '#F7F8FA', // Subtle background
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: chatSpacing.md,
    borderWidth: 1,
    borderColor: chatColors.lightBorder,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 1,
  },
  attachmentButtonText: {
    fontSize: 20, // Proper icon size
    color: chatColors.accent,
    fontWeight: chatTypography.medium,
  },
  
  // Image messages - Enhanced modern style
  imageMessageBubble: {
    maxWidth: '78%', // Slightly wider for better content display
    borderRadius: 16, // More modern radius
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.12, // More pronounced shadow for depth
    shadowRadius: 6,
    elevation: 3,
    marginVertical: 3,
    // Remove padding for edge-to-edge image display
  },
  userImageMessage: {
    borderBottomRightRadius: chatRadius.small,
  },
  receivedImageMessage: {
    borderBottomLeftRadius: chatRadius.small,
  },
  imageAttachment: {
    position: 'relative',
    borderRadius: chatRadius.medium,
    overflow: 'hidden',
  },
  attachmentImage: {
    width: 240,
    height: 180,
    borderRadius: chatRadius.medium,
    // Remove any padding/margin for edge-to-edge display
  },
  imageMessageFooter: {
    position: 'absolute',
    bottom: 6,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.65)',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 50,
    alignItems: 'flex-end',
  },
  imageTimestamp: {
    fontSize: chatTypography.tiny,
    color: '#FFFFFF',
    fontFamily: chatTypography.systemFont,
    fontWeight: chatTypography.medium,
    textAlign: 'right',
  },
  
  // Failed message status indicator
  failedStatus: {
    fontSize: chatTypography.tiny,
    color: '#FF3B30',
    fontFamily: chatTypography.systemFont,
    fontWeight: chatTypography.medium,
    marginLeft: 4,
  },
  
  // Video attachments (instant preview like WhatsApp)
  videoContainer: {
    position: 'relative',
    width: 240,
    height: 180,
    borderRadius: chatRadius.medium,
    overflow: 'hidden',
  },
  videoPlayButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playIcon: {
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 2, // Slightly offset to center the triangle
  },
  
  // Upload status indicators (WhatsApp-style)
  uploadStatusOverlay: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 3,
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 40,
  },
  uploadStatusIcon: {
    fontSize: 12,
    marginRight: 4,
  },
  uploadStatusText: {
    fontSize: chatTypography.tiny,
    color: '#FFFFFF',
    fontFamily: chatTypography.systemFont,
    fontWeight: chatTypography.medium,
  },
  uploadProgressIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#FFFFFF',
    marginRight: 4,
  },
  retryButton: {
    backgroundColor: 'rgba(255, 59, 48, 0.9)',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 4,
  },
  retryButtonText: {
    fontSize: chatTypography.tiny,
    color: '#FFFFFF',
    fontWeight: chatTypography.medium,
  },
  
  // Swipe-to-reply functionality (WhatsApp-style)
  swipeableContainer: {
    position: 'relative',
  },
  replyIcon: {
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -15 }],
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: chatColors.accent,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  replyIconLeft: {
    left: -40,
  },
  replyIconRight: {
    right: -40,
  },
  replyIconText: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  
  // File attachments
  fileAttachment: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
    borderRadius: chatRadius.medium,
    padding: chatSpacing.md,
    minHeight: 54,
  },
  attachmentIcon: {
    fontSize: 18,
    marginRight: 10,
  },
  attachmentInfo: {
    flex: 1,
  },
  attachmentName: {
    fontSize: chatTypography.caption,
    fontWeight: chatTypography.medium,
    marginBottom: 2,
    fontFamily: chatTypography.systemFont,
  },
  attachmentSize: {
    fontSize: chatTypography.small,
    opacity: 0.6,
    fontFamily: chatTypography.systemFont,
  },
  userAttachmentText: {
    color: chatColors.userText,
  },
  receivedAttachmentText: {
    color: chatColors.primaryText,
  },
  
  // Attachment preview
  attachmentPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: chatColors.chatBackground,
    borderRadius: chatRadius.medium,
    padding: chatSpacing.md,
    marginHorizontal: chatSpacing.lg,
    marginTop: chatSpacing.sm,
    borderWidth: 1,
    borderColor: chatColors.border,
  },
  attachmentPreviewInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  attachmentPreviewIcon: {
    fontSize: 18,
    marginRight: 10,
  },
  attachmentPreviewImage: {
    width: 36,
    height: 36,
    borderRadius: chatSpacing.sm,
    marginRight: 10,
  },
  attachmentPreviewImageContainer: {
    position: 'relative',
    width: 36,
    height: 36,
    borderRadius: chatSpacing.sm,
    marginRight: 10,
    overflow: 'hidden',
  },
  attachmentPreviewPlayButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -8 }, { translateY: -8 }],
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  attachmentPreviewPlayIcon: {
    fontSize: 8,
    color: '#FFFFFF',
    marginLeft: 1, // Slightly offset to center the triangle
  },
  attachmentPreviewDetails: {
    flex: 1,
  },
  attachmentPreviewName: {
    fontSize: chatTypography.caption,
    fontWeight: chatTypography.medium,
    color: chatColors.primaryText,
    marginBottom: 2,
    fontFamily: chatTypography.systemFont,
  },
  attachmentPreviewSize: {
    fontSize: chatTypography.small,
    color: chatColors.secondaryText,
    fontFamily: chatTypography.systemFont,
  },
  removeAttachmentButton: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: chatColors.danger,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeAttachmentText: {
    color: chatColors.userText,
    fontSize: chatTypography.small,
    fontWeight: chatTypography.semibold,
  },
  
  // File attachment styles
  fileIcon: {
    fontSize: 16,
    marginRight: chatSpacing.sm,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: chatTypography.caption,
    color: chatColors.primaryText,
    fontWeight: chatTypography.medium,
    marginBottom: 2,
  },
  fileSize: {
    fontSize: chatTypography.small,
    color: chatColors.secondaryText,
  },
  
  // Typing indicator
  typingContainer: {
    paddingHorizontal: chatSpacing.lg,
    paddingVertical: chatSpacing.xs,
  },
  typingText: {
    fontSize: chatTypography.caption,
    color: chatColors.secondaryText,
    fontStyle: 'italic',
    fontFamily: chatTypography.systemFont,
    fontWeight: chatTypography.regular,
  },

  // Upload progress overlay (WhatsApp-like persistent upload experience)
  uploadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: chatRadius.large,
  },
  uploadProgressContainer: {
    alignItems: 'center',
  },
  uploadProgressBar: {
    width: 60,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    marginBottom: 8,
  },
  uploadProgressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },
  uploadProgressText: {
    fontSize: chatTypography.small,
    color: '#FFFFFF',
    fontWeight: chatTypography.medium,
    textAlign: 'center',
  },
  uploadRetryButton: {
    marginTop: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: chatRadius.small,
  },
  uploadRetryButtonText: {
    fontSize: chatTypography.caption,
    color: chatColors.danger,
    fontWeight: chatTypography.semibold,
  },

  // Swipe-to-reply functionality (WhatsApp-like gesture)
  swipeToReplyContainer: {
    flex: 1,
  },
  replyMessagePreview: {
    backgroundColor: chatColors.accentLight,
    borderLeftWidth: 3,
    borderLeftColor: chatColors.accent,
    padding: chatSpacing.sm,
    marginBottom: chatSpacing.xs,
    borderRadius: chatRadius.small,
  },
  replyToText: {
    fontSize: chatTypography.caption,
    color: chatColors.accent,
    fontWeight: chatTypography.semibold,
    marginBottom: 2,
  },
  replyMessageText: {
    fontSize: chatTypography.caption,
    color: chatColors.secondaryText,
  },
  
  // Media error fallback styles
  mediaErrorPlaceholder: {
    backgroundColor: chatColors.lightBorder,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: chatRadius.medium,
    width: 240, // Enforce fixed width
    height: 180, // Enforce fixed height
    minHeight: 120,
    minWidth: 120,
    borderWidth: 1,
    borderColor: chatColors.border,
    shadowColor: '#000',
    shadowOpacity: 0.04,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 2 },
  },
  mediaErrorIcon: {
    fontSize: 36,
    color: chatColors.secondaryText,
    marginBottom: 4,
    textAlign: 'center',
  },
  mediaErrorText: {
    fontSize: chatTypography.caption,
    color: chatColors.secondaryText,
    textAlign: 'center',
    fontWeight: chatTypography.medium,
  },
});
