/**
 * Real-Time Audio Streaming Processor
 * Handles streaming voice processing for real-time voice calls
 * Optimized for <50ms latency with complete input tone removal
 */

import { Transform, Readable, Writable } from 'stream';
import { UnifiedVoiceProcessor, UnifiedVoiceProfile, UNIFIED_VOICE_PROFILES } from './unifiedVoiceProcessor';
import { EventEmitter } from 'events';
import * as crypto from 'crypto';

export interface StreamingConfig {
    chunkSize: number;              // Audio chunk size in bytes
    sampleRate: number;             // Audio sample rate
    bitDepth: number;               // Audio bit depth
    channels: number;               // Number of audio channels
    latencyTarget: number;          // Target latency in ms
    bufferSize: number;             // Internal buffer size
    processingMode: 'realtime' | 'buffered';
}

export interface StreamingSession {
    sessionId: string;
    userId?: string;
    profile: UnifiedVoiceProfile;
    config: StreamingConfig;
    startTime: Date;
    totalProcessed: number;
    averageLatency: number;
    status: 'active' | 'paused' | 'stopped';
}

export const DEFAULT_STREAMING_CONFIG: StreamingConfig = {
    chunkSize: 1024,                // 1KB chunks for low latency
    sampleRate: 44100,              // Standard sample rate
    bitDepth: 16,                   // 16-bit audio
    channels: 1,                    // Mono audio for voice
    latencyTarget: 50,              // 50ms target latency
    bufferSize: 4096,               // 4KB buffer
    processingMode: 'realtime'
};

export class RealTimeAudioStreaming extends EventEmitter {
    private unifiedProcessor: UnifiedVoiceProcessor;
    private activeSessions: Map<string, StreamingSession>;
    private processingQueue: Map<string, Buffer[]>;
    
    constructor() {
        super();
        this.unifiedProcessor = new UnifiedVoiceProcessor();
        this.activeSessions = new Map();
        this.processingQueue = new Map();
        
        console.log('Real-time audio streaming processor initialized');
    }
    
    /**
     * Create a new streaming session for real-time voice processing
     */
    createStreamingSession(
        profileName: string,
        config: Partial<StreamingConfig> = {},
        userId?: string
    ): string {
        const sessionId = crypto.randomUUID();
        const profile = UNIFIED_VOICE_PROFILES[profileName];
        
        if (!profile) {
            throw new Error(`Invalid profile: ${profileName}`);
        }
        
        const sessionConfig: StreamingConfig = {
            ...DEFAULT_STREAMING_CONFIG,
            ...config,
            latencyTarget: profile.performance.latencyTarget
        };
        
        const session: StreamingSession = {
            sessionId,
            userId,
            profile,
            config: sessionConfig,
            startTime: new Date(),
            totalProcessed: 0,
            averageLatency: 0,
            status: 'active'
        };
        
        this.activeSessions.set(sessionId, session);
        this.processingQueue.set(sessionId, []);
        
        console.log('Created streaming session:', {
            sessionId,
            profileName,
            userId,
            latencyTarget: sessionConfig.latencyTarget
        });
        
        this.emit('sessionCreated', session);
        return sessionId;
    }
    
    /**
     * Create a transform stream for real-time voice processing
     */
    createProcessingStream(sessionId: string): Transform {
        const session = this.activeSessions.get(sessionId);
        if (!session) {
            throw new Error(`Session not found: ${sessionId}`);
        }
        
        return new Transform({
            objectMode: false,
            transform: async (chunk: Buffer, encoding, callback) => {
                try {
                    const startTime = Date.now();
                    
                    // Process audio chunk through unified pipeline
                    const processedChunk = await this.unifiedProcessor.processVoiceUnified(
                        chunk,
                        session.profile,
                        session.userId
                    );
                    
                    const processingTime = Date.now() - startTime;
                    
                    // Update session statistics
                    session.totalProcessed += chunk.length;
                    session.averageLatency = (session.averageLatency + processingTime) / 2;
                    
                    // Emit processing metrics
                    this.emit('chunkProcessed', {
                        sessionId,
                        inputSize: chunk.length,
                        outputSize: processedChunk.length,
                        processingTime,
                        withinTarget: processingTime <= session.config.latencyTarget
                    });
                    
                    // Check latency target
                    if (processingTime > session.config.latencyTarget) {
                        this.emit('latencyWarning', {
                            sessionId,
                            processingTime,
                            target: session.config.latencyTarget
                        });
                    }
                    
                    callback(null, processedChunk);
                    
                } catch (error) {
                    console.error('Streaming processing error:', error);
                    this.emit('processingError', { sessionId, error });
                    if (error instanceof Error || error === null || error === undefined) {
                        callback(error);
                    } else {
                        callback(new Error(String(error)));
                    }
                }
            }
        });
    }
    
    /**
     * Create a buffered processing stream for better quality
     */
    createBufferedProcessingStream(sessionId: string): Transform {
        const session = this.activeSessions.get(sessionId);
        if (!session) {
            throw new Error(`Session not found: ${sessionId}`);
        }
        
        let buffer = Buffer.alloc(0);
        const targetBufferSize = session.config.bufferSize;
        
        return new Transform({
            objectMode: false,
            transform: async (chunk: Buffer, encoding, callback) => {
                try {
                    // Add chunk to buffer
                    buffer = Buffer.concat([buffer, chunk]);
                    
                    // Process when buffer reaches target size
                    if (buffer.length >= targetBufferSize) {
                        const startTime = Date.now();
                        
                        // Process buffered audio
                        const processedBuffer = await this.unifiedProcessor.processVoiceUnified(
                            buffer,
                            session.profile,
                            session.userId
                        );
                        
                        const processingTime = Date.now() - startTime;
                        
                        // Update session statistics
                        session.totalProcessed += buffer.length;
                        session.averageLatency = (session.averageLatency + processingTime) / 2;
                        
                        // Emit processing metrics
                        this.emit('bufferProcessed', {
                            sessionId,
                            inputSize: buffer.length,
                            outputSize: processedBuffer.length,
                            processingTime,
                            withinTarget: processingTime <= session.config.latencyTarget
                        });
                        
                        // Reset buffer and send processed audio
                        buffer = Buffer.alloc(0);
                        callback(null, processedBuffer);
                    } else {
                        // Buffer not full yet, continue accumulating
                        callback();
                    }
                    
                } catch (error) {
                    console.error('Buffered processing error:', error);
                    this.emit('processingError', { sessionId, error });
                    if (error instanceof Error || error === null || error === undefined) {
                        callback(error);
                    } else {
                        callback(new Error(String(error)));
                    }
                }
            },
            flush: async (callback) => {
                // Process any remaining buffer on stream end
                if (buffer.length > 0) {
                    try {
                        const processedBuffer = await this.unifiedProcessor.processVoiceUnified(
                            buffer,
                            session.profile,
                            session.userId
                        );
                        callback(null, processedBuffer);
                    } catch (error) {
                        if (error instanceof Error || error === null || error === undefined) {
                            callback(error);
                        } else {
                            callback(new Error(String(error)));
                        }
                    }
                } else {
                    callback();
                }
            }
        });
    }
    
    /**
     * Get session information
     */
    getSession(sessionId: string): StreamingSession | undefined {
        return this.activeSessions.get(sessionId);
    }
    
    /**
     * Get all active sessions
     */
    getActiveSessions(): StreamingSession[] {
        return Array.from(this.activeSessions.values());
    }
    
    /**
     * Pause a streaming session
     */
    pauseSession(sessionId: string): boolean {
        const session = this.activeSessions.get(sessionId);
        if (session) {
            session.status = 'paused';
            this.emit('sessionPaused', session);
            return true;
        }
        return false;
    }
    
    /**
     * Resume a paused streaming session
     */
    resumeSession(sessionId: string): boolean {
        const session = this.activeSessions.get(sessionId);
        if (session && session.status === 'paused') {
            session.status = 'active';
            this.emit('sessionResumed', session);
            return true;
        }
        return false;
    }
    
    /**
     * Stop and cleanup a streaming session
     */
    stopSession(sessionId: string): boolean {
        const session = this.activeSessions.get(sessionId);
        if (session) {
            session.status = 'stopped';
            this.activeSessions.delete(sessionId);
            this.processingQueue.delete(sessionId);
            
            this.emit('sessionStopped', session);
            console.log('Stopped streaming session:', {
                sessionId,
                totalProcessed: session.totalProcessed,
                averageLatency: session.averageLatency,
                duration: Date.now() - session.startTime.getTime()
            });
            
            return true;
        }
        return false;
    }
    
    /**
     * Get streaming statistics
     */
    getStreamingStats(): any {
        const sessions = Array.from(this.activeSessions.values());
        
        return {
            activeSessions: sessions.length,
            totalProcessed: sessions.reduce((sum, s) => sum + s.totalProcessed, 0),
            averageLatency: sessions.length > 0 
                ? sessions.reduce((sum, s) => sum + s.averageLatency, 0) / sessions.length 
                : 0,
            sessionsByStatus: {
                active: sessions.filter(s => s.status === 'active').length,
                paused: sessions.filter(s => s.status === 'paused').length
            },
            sessionsByProfile: sessions.reduce((acc, s) => {
                acc[s.profile.name] = (acc[s.profile.name] || 0) + 1;
                return acc;
            }, {} as Record<string, number>)
        };
    }
    
    /**
     * Cleanup all sessions
     */
    cleanup(): void {
        const sessionIds = Array.from(this.activeSessions.keys());
        sessionIds.forEach(sessionId => this.stopSession(sessionId));
        
        console.log('Real-time audio streaming processor cleaned up');
    }
}
