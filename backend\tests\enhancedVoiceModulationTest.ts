/**
 * Enhanced Voice Modulation Test
 * Tests the new SoX-based voice modulation with tone changes
 */

import { VoiceModulationService, VOICE_PROFILES } from '../services/voiceModulation';
import * as fs from 'fs/promises';
import * as path from 'path';

async function testEnhancedVoiceModulation() {
  console.log('🎵 Testing Enhanced Voice Modulation with SoX Integration');
  console.log('='.repeat(60));

  const voiceService = new VoiceModulationService();

  // Test SoX availability
  console.log('\n1. Testing SoX Availability...');
  const soxAvailable = await voiceService.checkSoxAvailability();
  console.log(`   SoX Available: ${soxAvailable ? '✅ YES' : '❌ NO'}`);

  if (!soxAvailable) {
    console.log('   ⚠️  SoX not available - enhanced features will use fallback');
    return;
  }

  // Test enhanced profiles
  console.log('\n2. Testing Enhanced Voice Profiles...');
  const profiles = Object.entries(VOICE_PROFILES);
  
  for (const [profileName, profile] of profiles) {
    console.log(`\n   Testing Profile: ${profileName}`);
    console.log(`   - Pitch: ${profile.pitch} semitones`);
    console.log(`   - Bass Gain: ${profile.bassGain || 0} dB`);
    console.log(`   - Treble Gain: ${profile.trebleGain || 0} dB`);
    console.log(`   - Spectral Tilt: ${profile.spectralTilt || 0} dB/oct`);
    console.log(`   - Harmonic Distortion: ${profile.harmonicDistortion || 0}%`);
    console.log(`   - EQ Bands: ${profile.eqBands?.length || 0} bands`);
    
    if (profile.eqBands && profile.eqBands.length > 0) {
      profile.eqBands.forEach((band, i) => {
        console.log(`     Band ${i + 1}: ${band.frequency}Hz, ${band.gain > 0 ? '+' : ''}${band.gain}dB, Q=${band.width}`);
      });
    }
  }

  // Test sample generation with enhanced profiles
  console.log('\n3. Testing Sample Generation...');

  // Test SECURE_MALE profile (has enhanced parameters)
  const secureProfile = VOICE_PROFILES.SECURE_MALE;

  try {
    console.log('   Generating sample with SECURE_MALE profile...');
    
    const sampleBuffer = await voiceService.generateProfileSample('SECURE_MALE', secureProfile);
    console.log(`   ✅ Sample generated: ${sampleBuffer.length} bytes`);
    
    // Save sample for manual testing
    const outputPath = path.join(__dirname, '../temp/enhanced_voice_test.wav');
    await fs.writeFile(outputPath, sampleBuffer);
    console.log(`   💾 Sample saved to: ${outputPath}`);
    
  } catch (error) {
    console.error('   ❌ Sample generation failed:', error);
  }

  // Test custom profile with advanced parameters
  console.log('\n4. Testing Custom Enhanced Profile...');
  
  const customProfile = {
    name: 'TEST_ENHANCED',
    pitch: -4,
    tempo: 0.9,
    reverb: 20,
    distortion: 10,
    formant: -300,
    chorus: true,
    normalize: true,
    bassGain: 6,
    trebleGain: -3,
    midGain: 2,
    compandRatio: 3.0,
    spectralTilt: -2,
    harmonicDistortion: 15,
    vocoderStrength: 30,
    eqBands: [
      { frequency: 100, gain: 8, width: 1.5 },
      { frequency: 1000, gain: -2, width: 1.0 },
      { frequency: 4000, gain: -4, width: 1.2 }
    ],
    description: 'Test enhanced profile with all parameters',
    userType: 'all'
  };

  try {
    console.log('   Testing custom enhanced profile...');
    const customSample = await voiceService.generateProfileSample('TEST_ENHANCED', customProfile);
    console.log(`   ✅ Custom sample generated: ${customSample.length} bytes`);
    
    // Save custom sample
    const customOutputPath = path.join(__dirname, '../temp/custom_enhanced_test.wav');
    await fs.writeFile(customOutputPath, customSample);
    console.log(`   💾 Custom sample saved to: ${customOutputPath}`);
    
  } catch (error) {
    console.error('   ❌ Custom profile test failed:', error);
  }

  // Test SoX command building
  console.log('\n5. Testing SoX Command Generation...');
  
  try {
    // Access private method for testing (TypeScript hack)
    const buildSoxCommand = (voiceService as any).buildSoxCommand.bind(voiceService);
    
    const testArgs = buildSoxCommand('input.wav', 'output.wav', secureProfile);
    console.log('   Generated SoX command arguments:');
    console.log(`   sox ${testArgs.join(' ')}`);
    
    // Count the number of effects applied
    const effectCount = testArgs.filter((arg: string) =>
      ['pitch', 'tempo', 'bass', 'treble', 'equalizer', 'reverb', 'overdrive', 'chorus', 'compand'].includes(arg)
    ).length;
    
    console.log(`   ✅ Applied ${effectCount} different effects`);
    
  } catch (error) {
    console.error('   ❌ SoX command generation failed:', error);
  }

  console.log('\n6. Performance Comparison...');
  
  try {
    const startTime = Date.now();
    
    // Test basic profile (NORMAL)
    const normalSample = await voiceService.generateProfileSample('NORMAL', VOICE_PROFILES.NORMAL);
    const normalTime = Date.now() - startTime;
    
    const enhancedStartTime = Date.now();
    
    // Test enhanced profile (SECURE_MALE)
    const enhancedSample = await voiceService.generateProfileSample('SECURE_MALE', VOICE_PROFILES.SECURE_MALE);
    const enhancedTime = Date.now() - enhancedStartTime;
    
    console.log(`   Normal profile processing: ${normalTime}ms`);
    console.log(`   Enhanced profile processing: ${enhancedTime}ms`);
    console.log(`   Performance overhead: ${enhancedTime - normalTime}ms (${((enhancedTime / normalTime - 1) * 100).toFixed(1)}%)`);
    
  } catch (error) {
    console.error('   ❌ Performance test failed:', error);
  }

  console.log('\n✅ Enhanced Voice Modulation Test Complete!');
  console.log('\nKey Improvements:');
  console.log('• Enhanced tone modification with bass/treble/mid controls');
  console.log('• Multi-band parametric equalizer');
  console.log('• Advanced compression and spectral processing');
  console.log('• Harmonic distortion for tone coloration');
  console.log('• Vocoder effects for robotic voices');
  console.log('• Spectral tilt for overall tonal character');
  console.log('• Improved formant shifting');
  console.log('• Better SoX effect chaining');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEnhancedVoiceModulation().catch(console.error);
}

export { testEnhancedVoiceModulation };
