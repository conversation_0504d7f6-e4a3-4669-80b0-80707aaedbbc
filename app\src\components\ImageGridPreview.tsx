/**
 * WhatsApp-style Image Grid Preview Component
 * Shows multiple selected images in a grid layout before sending
 */

import React from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  Text,
  StyleSheet,
  Dimensions,
  ScrollView,
} from 'react-native';
import { MediaAttachment } from '../services/MediaService';

const { width: screenWidth } = Dimensions.get('window');

interface ImageGridPreviewProps {
  images: MediaAttachment[];
  onRemoveImage: (index: number) => void;
  onImagePress?: (image: MediaAttachment, index: number) => void;
}

export const ImageGridPreview: React.FC<ImageGridPreviewProps> = ({
  images,
  onRemoveImage,
  onImagePress,
}) => {
  if (!images || images.length === 0) {
    return null;
  }

  // Calculate grid layout based on number of images
  const getGridLayout = (count: number) => {
    if (count === 1) return { columns: 1, rows: 1 };
    if (count === 2) return { columns: 2, rows: 1 };
    if (count <= 4) return { columns: 2, rows: 2 };
    if (count <= 6) return { columns: 3, rows: 2 };
    if (count <= 9) return { columns: 3, rows: 3 };
    return { columns: 3, rows: 4 }; // Max 10 images
  };

  const layout = getGridLayout(images.length);
  const imageSize = (screenWidth - 60) / layout.columns - 4; // Account for padding and margins

  const renderImage = (image: MediaAttachment, index: number) => {
    const isLastImage = index === images.length - 1;
    const remainingCount = images.length - index;
    const showOverlay = index >= 8 && remainingCount > 1; // Show "+X more" overlay for 9th+ images

    return (
      <TouchableOpacity
        key={image.id}
        style={[styles.imageContainer, { width: imageSize, height: imageSize }]}
        onPress={() => onImagePress?.(image, index)}
        activeOpacity={0.8}
      >
        <Image
          source={{ uri: image.uri }}
          style={styles.image}
          resizeMode="cover"
        />
        
        {/* Remove button */}
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => onRemoveImage(index)}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Text style={styles.removeButtonText}>×</Text>
        </TouchableOpacity>

        {/* Show "+X more" overlay for excess images */}
        {showOverlay && (
          <View style={styles.moreOverlay}>
            <Text style={styles.moreText}>+{remainingCount - 1}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerText}>
          {images.length} image{images.length > 1 ? 's' : ''} selected
        </Text>
        {images.length > 1 && (
          <Text style={styles.subHeaderText}>
            Tap to view • Tap × to remove
          </Text>
        )}
      </View>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.grid}>
          {images.slice(0, 9).map((image, index) => renderImage(image, index))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 12,
    marginVertical: 8,
  },
  header: {
    marginBottom: 8,
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  subHeaderText: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  scrollContent: {
    paddingHorizontal: 4,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  imageContainer: {
    position: 'relative',
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#e1e1e1',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  removeButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    lineHeight: 16,
  },
  moreOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
