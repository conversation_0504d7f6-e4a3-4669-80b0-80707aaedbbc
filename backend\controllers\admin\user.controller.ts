import { Request, Response } from 'express';
import User from '../../models/User';
import { generatePaginationData } from '../../utils/pagination';

/**
 * Get all users with pagination for admin dashboard
 * GET /api/admin/users
 */
export async function getUsers(req: Request, res: Response): Promise<void> {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string || '';
    const status = req.query.status as string || '';

    const query: any = {};
    
    // Apply search filter if provided
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { 'profile.displayName': { $regex: search, $options: 'i' } }
      ];
    }
    
    // Apply status filter if provided
    if (status && ['active', 'inactive', 'locked'].includes(status)) {
      query.status = status;
    }

    const total = await User.countDocuments(query);
    const users = await User.find(query, '-expressionHash -deviceFingerprintHash -bleUUIDHash')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    // Generate pagination data
    const pagination = generatePaginationData(page, limit, total);

    res.status(200).json({
      users,
      pagination
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
}

/**
 * Get user details by ID
 * GET /api/admin/users/:id
 */
export async function getUserById(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id, '-expressionHash -deviceFingerprintHash -bleUUIDHash');
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    
    // Include additional stats for admin dashboard
    const userStats = {
      buildsCount: user.builds?.length || 0,
      lastActive: user.lastLoginAt || user.updatedAt,
      deviceInfo: user.deviceMetadata || { model: 'Unknown', os: 'Unknown' },
    };
    
    res.status(200).json({
      user,
      stats: userStats
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Failed to fetch user details' });
  }
}

/**
 * Update user status (activate/deactivate/lock)
 * PATCH /api/admin/users/:id/status
 */
export async function updateUserStatus(req: Request, res: Response): Promise<void> {
  try {
    const { status } = req.body;
    
    if (!status || !['active', 'inactive', 'locked'].includes(status)) {
      res.status(400).json({ error: 'Invalid status value' });
      return;
    }
    
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    
    // Cannot deactivate the superuser
    if (user.isSuperuser && status !== 'active') {
      res.status(403).json({ error: 'Cannot deactivate the superuser account' });
      return;
    }
    
    user.status = status;
    await user.save();
    
    res.status(200).json({ 
      message: `User status updated to ${status}`,
      user: {
        id: user._id,
        username: user.username,
        status: user.status
      }
    });
  } catch (error) {
    console.error('Error updating user status:', error);
    res.status(500).json({ error: 'Failed to update user status' });
  }
}

/**
 * Reset user device
 * POST /api/admin/users/:id/reset-device
 */
export async function resetUserDevice(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    
    // Generate a reset token for the user to re-register their device
    // This would be implemented based on your device registration flow
    const resetToken = Math.random().toString(36).substring(2, 15) + 
                       Math.random().toString(36).substring(2, 15);
    
    // Update user to indicate device reset
    user.deviceMetadata = undefined;
    user.status = 'inactive'; // Temporarily deactivate until new device is registered
    await user.save();
    
    res.status(200).json({
      message: 'User device reset initiated',
      resetToken,
      user: {
        id: user._id,
        username: user.username,
        status: user.status
      }
    });
  } catch (error) {
    console.error('Error resetting user device:', error);
    res.status(500).json({ error: 'Failed to reset user device' });
  }
}

/**
 * Get user activity summary
 * GET /api/admin/users/:id/activity
 */
export async function getUserActivity(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    
    // This would be expanded with real activity data from your database
    // For now, providing a simple structure
    const activity = {
      lastLogin: user.lastLoginAt,
      totalLogins: 0, // This would come from a real counter or log collection
      recentBuilds: [], // This would be populated from your build collection
      deviceHistory: [
        {
          registeredAt: user.deviceMetadata?.registeredAt,
          deviceInfo: `${user.deviceMetadata?.model || 'Unknown'} (${user.deviceMetadata?.os || 'Unknown'})`
        }
      ]
    };
    
    res.status(200).json({ activity });
  } catch (error) {
    console.error('Error fetching user activity:', error);
    res.status(500).json({ error: 'Failed to fetch user activity' });
  }
}

/**
 * Create new user (admin only)
 * POST /api/admin/users
 */
export async function createUser(req: Request, res: Response): Promise<void> {
  try {
    const { username, expression, displayName, isSuperuser } = req.body;
    
    // Validate required fields (removed deviceFingerprint and bleUUID)
    if (!username || !expression || !displayName) {
      res.status(400).json({ error: 'Missing required fields: username, expression, displayName' });
      return;
    }

    // Check if username already exists
    const existingUser = await User.findOne({ username });
    if (existingUser) {
      res.status(409).json({ error: 'Username already exists' });
      return;
    }

    // Hash expression
    const bcrypt = require('bcrypt');
    const expressionHash = await bcrypt.hash(expression, 12);

    // If creating a superuser, ensure all other users are not superuser
    if (isSuperuser === true) {
      await User.updateMany({}, { $set: { isSuperuser: false } });
    }

    // Create new user without device fingerprint and BLE UUID
    // These will be set during first login
    const user = new User({
      username,
      expressionHash,
      unlockExpression: expression, // Store plain text for admin viewing
      profile: { displayName },
      isSuperuser: !!isSuperuser,
      status: 'pending_device_registration', // User needs to login first to register device
      expressionType: 'calculator',
      expressionUpdatedAt: new Date()
    });

    await user.save();

    // Return user data without sensitive hashes
    const userData = {
      _id: user._id,
      username: user.username,
      displayName: user.profile.displayName,
      expression: user.unlockExpression,
      isSuperuser: user.isSuperuser,
      status: user.status,
      deviceRegistrationRequired: true,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.status(201).json(userData);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
}

/**
 * Update user (admin only)
 * PUT /api/admin/users/:id
 */
export async function updateUser(req: Request, res: Response): Promise<void> {
  try {
    const { displayName, expression, isSuperuser, status } = req.body;
    
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    // Update fields if provided
    if (displayName) {
      user.profile.displayName = displayName;
    }

    if (expression) {
      const bcrypt = require('bcrypt');
      user.expressionHash = await bcrypt.hash(expression, 12);
      user.unlockExpression = expression;
      user.expressionUpdatedAt = new Date();
    }

    if (status && ['active', 'inactive', 'locked', 'pending_device_registration'].includes(status)) {
      // Cannot deactivate the superuser
      if (user.isSuperuser && status !== 'active') {
        res.status(403).json({ error: 'Cannot deactivate the superuser account' });
        return;
      }
      user.status = status;
    }

    // Handle superuser status
    if (typeof isSuperuser === 'boolean') {
      if (isSuperuser === true) {
        // Ensure all other users are not superuser
        await User.updateMany({ _id: { $ne: user._id } }, { $set: { isSuperuser: false } });
        user.isSuperuser = true;
      } else {
        user.isSuperuser = isSuperuser;
      }
    }

    await user.save();

    // Return updated user data
    const userData = {
      _id: user._id,
      username: user.username,
      displayName: user.profile.displayName,
      expression: user.unlockExpression,
      isSuperuser: user.isSuperuser,
      status: user.status,
      deviceRegistered: !!(user.deviceFingerprintHash && user.bleUUIDHash),
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.status(200).json(userData);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
}

/**
 * Delete user (admin only)
 * DELETE /api/admin/users/:id
 */
export async function deleteUser(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    // Cannot delete the superuser
    if (user.isSuperuser) {
      res.status(403).json({ error: 'Cannot delete the superuser account' });
      return;
    }

    // Soft delete by setting status to inactive
    user.status = 'inactive';
    await user.save();

    res.status(200).json({ 
      message: 'User deactivated successfully',
      user: {
        id: user._id,
        username: user.username,
        status: user.status
      }
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
}

/**
 * Register BLE device for a user (UPDATED: Direct implementation)
 * POST /api/admin/users/:id/add-ble-device
 */
export async function addBleDevice(req: Request, res: Response): Promise<void> {
  try {
    const userId = req.params.id;
    const {
      deviceId,
      deviceName,
      adData,
      characteristics,
      pairedAt,
      isVerified,
      signature,
      signatureCharUuid
    } = req.body;

    // Validate required fields
    if (!deviceId || !deviceName) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: deviceId, deviceName'
      });
      return;
    }

    // Import models
    const BleDevice = (await import('../../models/BleDevice')).default;
    const User = (await import('../../models/User')).default;

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Check if device already exists for this user
    const existingDevice = await BleDevice.findOne({ userId, deviceId });
    if (existingDevice) {
      res.status(409).json({
        success: false,
        error: 'BLE device already registered for this user',
        device: existingDevice
      });
      return;
    }

    // Create new BLE device record
    const bleDevice = new BleDevice({
      userId,
      deviceId,
      deviceName,
      deviceType: 'generic',
      registrationData: {
        adData,
        characteristics: characteristics || {},
        signature,
        signatureCharUuid,
        registeredAt: new Date(),
        registeredBy: 'admin',
        registrationIP: req.ip
      },
      status: {
        isVerified: !!isVerified,
        isActive: true,
        lastSeen: new Date(),
        connectionCount: 0
      },
      voiceCallAuth: {
        enabled: true,
        callCount: 0,
        authFailures: 0,
        authSuccessRate: 100
      },
      security: {
        riskScore: 0,
        violations: [],
        securityFlags: []
      },
      metadata: {
        notes: 'Registered via legacy admin endpoint',
        lastModified: new Date()
      },
      performance: {
        connectionReliability: 100,
        audioDropouts: 0
      },
      integration: {
        syncedWithUserModel: false
      }
    });

    await bleDevice.save();

    // Also add to User model for backward compatibility
    const { createHash } = await import('crypto');
    const deviceEntry = user.devices.find(d => d.deviceId === deviceId) || {
      deviceId,
      fingerprint: createHash('sha256').update(deviceId).digest('hex'),
      deviceType: 'mobile' as const,
      deviceModel: '',
      os: '',
      browser: '',
      lastUsed: new Date(),
      isActive: true,
      bleDevices: [] as Array<{
        deviceId: string;
        deviceName: string;
        pairedAt: Date;
        lastConnected: Date;
        isVerified: boolean;
        adData?: any;
        characteristics?: Record<string, string | null>;
        signature?: string;
        signatureCharUuid?: string;
      }>
    };

    // Add BLE device to user's device entry
    deviceEntry.bleDevices.push({
      deviceId,
      deviceName,
      pairedAt: pairedAt ? new Date(pairedAt) : new Date(),
      lastConnected: new Date(),
      isVerified: !!isVerified,
      adData,
      characteristics,
      signature,
      signatureCharUuid
    });

    // Add device entry if it's new
    if (!user.devices.find(d => d.deviceId === deviceId)) {
      user.devices.push(deviceEntry);
    }

    await user.save();

    // Mark as synced
    bleDevice.integration.syncedWithUserModel = true;
    bleDevice.integration.lastSyncAt = new Date();
    await bleDevice.save();

    res.status(201).json({
      success: true,
      message: 'BLE device registered successfully',
      device: bleDevice
    });

  } catch (error) {
    console.error('Failed to register BLE device:', error);
    res.status(500).json({ success: false, error: 'Failed to register BLE device' });
  }
}
