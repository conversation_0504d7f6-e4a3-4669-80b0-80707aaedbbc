/**
 * Video Preview Component
 * Shows video thumbnail with play button overlay for attachment preview
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Image,
  Text,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Video } from 'expo-av';
import * as VideoThumbnails from 'expo-video-thumbnails';
import { MediaAttachment } from '../services/MediaService';

const { width: screenWidth } = Dimensions.get('window');

interface VideoPreviewProps {
  attachment: MediaAttachment;
  style?: any;
  showPlayButton?: boolean;
}

export const VideoPreview: React.FC<VideoPreviewProps> = ({
  attachment,
  style,
  showPlayButton = true,
}) => {
  const [thumbnailUri, setThumbnailUri] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    generateThumbnail();
  }, [attachment.uri]);

  const generateThumbnail = async () => {
    if (!attachment.uri || !attachment.isVideo) return;

    setIsGenerating(true);
    setError(null);

    try {
      console.log('🎥 Generating optimized video thumbnail for:', attachment.name);

      const startTime = Date.now();
      const { uri } = await VideoThumbnails.getThumbnailAsync(attachment.uri, {
        time: 1000, // 1 second into the video
        quality: 0.6, // Reduced quality for faster generation
        headers: {}, // Ensure no extra headers
      });

      const duration = Date.now() - startTime;
      setThumbnailUri(uri);
      console.log(`✅ Video thumbnail generated in ${duration}ms:`, uri);
    } catch (err) {
      console.error('❌ Failed to generate video thumbnail:', err);
      setError('Failed to generate preview');
    } finally {
      setIsGenerating(false);
    }
  };

  if (error) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorIcon}>🎥</Text>
          <Text style={styles.errorText}>Video</Text>
          <Text style={styles.sizeText}>
            {(attachment.size / (1024 * 1024)).toFixed(1)} MB
          </Text>
        </View>
        {showPlayButton && (
          <View style={styles.modernPlayButton}>
            <View style={styles.playIconContainer}>
              <Text style={styles.modernPlayIcon}>▶</Text>
            </View>
          </View>
        )}
      </View>
    );
  }

  if (isGenerating || !thumbnailUri) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingIcon}>🎥</Text>
          <Text style={styles.loadingText}>
            {isGenerating ? 'Generating preview...' : 'Video'}
          </Text>
        </View>
        {showPlayButton && (
          <View style={styles.modernPlayButton}>
            <View style={styles.playIconContainer}>
              <Text style={styles.modernPlayIcon}>▶</Text>
            </View>
          </View>
        )}
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <Image
        source={{ uri: thumbnailUri }}
        style={styles.thumbnail}
        resizeMode="cover"
      />
      {showPlayButton && (
        <View style={styles.modernPlayButton}>
          <View style={styles.playIconContainer}>
            <Text style={styles.modernPlayIcon}>▶</Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  modernPlayButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -28 }, { translateY: -28 }],
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  playIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernPlayIcon: {
    fontSize: 18,
    color: '#000',
    fontWeight: '600',
    textAlign: 'center',
    marginLeft: 2, // Slight offset to center the triangle visually
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  sizeText: {
    fontSize: 12,
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  loadingText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});
