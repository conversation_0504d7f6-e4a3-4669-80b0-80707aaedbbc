## Document for examples
You can add your example to this directory.
If you want to share it with other developers, please use this directory.

## Rules
If you want to merge your example by a pull request, please follow these guidelines. The license of your program is same as WORLD.
- Please determine a file name to appropriately describe the function.
- Please write your contact address (e.g. email).
- Please write header information in .cpp to clearly show the purpose and how to use this program.
- Please add the compile command in the makefile in this directory.
- Please don't change the WORLD-core (in "src") programs.
- Please permit to modify the programs. I might fix the program.

Follows are rules for modification.
- To modify the program, please send a pull request. However, I accept only the request by the same developer.
- If you want to add a similar function, please determine a different name.

## Caution
Since I do only the check whether the program works or not, I can't guarantee the quality of the program in this directory.
If you have a question in an example, please contact the developer (I can't answer the question).
