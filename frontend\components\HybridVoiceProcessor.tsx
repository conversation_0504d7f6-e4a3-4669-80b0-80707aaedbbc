import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import apiClient from '../utils/apiClient';
import {
  Mic as MicrophoneIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Upload as CloudArrowUpIcon,
  Download as CloudArrowDownIcon,
  Settings as CogIcon,
  Warning as ExclamationTriangleIcon,
  CheckCircle as CheckCircleIcon,
  VolumeUp as SpeakerWaveIcon,
  <PERSON>ne as SlidersIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  Timer as TimerIcon
} from '@mui/icons-material';

interface HybridProfile {
  name: string;
  description: string;
  type: 'hybrid';
  neutralization: {
    enabled: boolean;
    f0Neutralization: boolean;
    formantNormalization: boolean;
    spectralSmoothing: number;
    noiseLevel: number;
  };
  modulation: {
    enabled: boolean;
    pitch: number;
    tempo: number;
    reverb: number;
    distortion: number;
    formant: number;
    chorus: boolean;
  };
  performance: {
    realTimeMode: boolean;
    latencyTarget: number;
    qualityMode: 'speed' | 'balanced' | 'quality';
    parallelProcessing: boolean;
  };
  security: {
    reversible: boolean;
    inputToneRemoval: boolean;
    antiForensic: boolean;
    outputVariation: boolean;
  };
}

interface HybridVoiceProcessorProps {
  onProfileChange?: (profile: string) => void;
  selectedProfile?: string;
  className?: string;
}

const HybridVoiceProcessor: React.FC<HybridVoiceProcessorProps> = ({
  onProfileChange,
  selectedProfile = 'REALTIME_HYBRID_SECURE',
  className = ''
}) => {
  const [profiles, setProfiles] = useState<Record<string, HybridProfile>>({});
  const [capabilities, setCapabilities] = useState<any>(null);
  const [currentProfile, setCurrentProfile] = useState<string>(selectedProfile);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [processedAudioUrl, setProcessedAudioUrl] = useState<string | null>(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [processingTime, setProcessingTime] = useState<number | null>(null);
  const [testResults, setTestResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const recordingIntervalRef = useRef<number | null>(null);

  useEffect(() => {
    fetchProfiles();
    fetchCapabilities();
  }, []);

  useEffect(() => {
    if (onProfileChange) {
      onProfileChange(currentProfile);
    }
  }, [currentProfile, onProfileChange]);

  const fetchProfiles = async () => {
    try {
      const response = await apiClient.get('/api/voice/hybrid/profiles');
      if (response.data.success) {
        setProfiles(response.data.data.profiles);
      }
    } catch (error) {
      console.error('Failed to fetch hybrid profiles:', error);
      setError('Failed to load hybrid profiles');
    }
  };

  const fetchCapabilities = async () => {
    try {
      const response = await apiClient.get('/api/voice/hybrid/capabilities');
      if (response.data.success) {
        setCapabilities(response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch capabilities:', error);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        }
      });
      
      streamRef.current = stream;
      audioChunksRef.current = [];
      
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        setAudioBlob(audioBlob);
        setRecordingTime(0);
        if (recordingIntervalRef.current) {
          clearInterval(recordingIntervalRef.current);
        }
      };
      
      mediaRecorderRef.current.start(100);
      setIsRecording(true);
      setRecordingTime(0);
      setError(null);
      setSuccess(null);
      setProcessedAudioUrl(null);
      
      // Start timer
      recordingIntervalRef.current = window.setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
    } catch (error) {
      console.error('Failed to start recording:', error);
      setError('Failed to access microphone');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
      }
    }
  };

  const processAudio = async () => {
    if (!audioBlob) {
      setError('No audio to process');
      return;
    }

    if (!profiles[currentProfile]) {
      setError('Invalid profile selected');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setSuccess(null);

    try {
      // Convert webm to wav for better compatibility
      const wavBlob = await convertToWav(audioBlob);
      
      const formData = new FormData();
      formData.append('audio', wavBlob, 'recording.wav');
      formData.append('profileName', currentProfile);

      const startTime = Date.now();
      const response = await apiClient.post('/api/voice/hybrid/process', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseType: 'blob'
      });

      const endTime = Date.now();
      setProcessingTime(endTime - startTime);

      // Get processing metadata from headers
      const processingTimeHeader = response.headers['x-processing-time'];
      const profileUsed = response.headers['x-profile-used'];
      const inputToneRemoved = response.headers['x-input-tone-removed'];
      const antiForensic = response.headers['x-anti-forensic'];

      // Create URL for processed audio
      const processedBlob = new Blob([response.data], { type: 'audio/wav' });
      const audioUrl = URL.createObjectURL(processedBlob);
      setProcessedAudioUrl(audioUrl);

      setSuccess(`Hybrid processing completed in ${processingTimeHeader || processingTime}ms. Input tone characteristics completely removed.`);

    } catch (error: any) {
      console.error('Hybrid processing failed:', error);
      setError(error.response?.data?.error || 'Hybrid voice processing failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const runPerformanceTest = async () => {
    if (!audioBlob) {
      setError('No audio to test');
      return;
    }

    setIsTesting(true);
    setError(null);
    setTestResults(null);

    try {
      const wavBlob = await convertToWav(audioBlob);
      
      const formData = new FormData();
      formData.append('audio', wavBlob, 'test.wav');

      const response = await apiClient.post('/api/voice/hybrid/realtime-test', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response.data.success) {
        setTestResults(response.data.data);
        setSuccess('Performance test completed successfully');
      }

    } catch (error: any) {
      console.error('Performance test failed:', error);
      setError(error.response?.data?.error || 'Performance test failed');
    } finally {
      setIsTesting(false);
    }
  };

  const convertToWav = async (blob: Blob): Promise<Blob> => {
    // For now, return the blob as-is
    // In production, you might want to implement proper conversion
    return blob;
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getLatencyStatus = (latency: number, target: number) => {
    if (latency <= target) {
      return { color: 'text-green-600', status: 'Excellent' };
    } else if (latency <= target * 1.5) {
      return { color: 'text-yellow-600', status: 'Good' };
    } else {
      return { color: 'text-red-600', status: 'Needs Optimization' };
    }
  };

  const profileData = profiles[currentProfile];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
        <div className="flex items-center mb-4">
          <div className="p-2 bg-blue-100 rounded-lg mr-3">
            <SecurityIcon className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-blue-900">Hybrid Voice Processor</h3>
            <p className="text-sm text-blue-700">SoX Modulation + Voice Neutralization for complete input tone removal</p>
          </div>
        </div>
        
        {capabilities && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center">
              <CheckCircleIcon className={`w-4 h-4 mr-2 ${capabilities.hybridAvailable ? 'text-green-500' : 'text-red-500'}`} />
              <span>Hybrid Processing: {capabilities.hybridAvailable ? 'Available' : 'Unavailable'}</span>
            </div>
            <div className="flex items-center">
              <TimerIcon className="w-4 h-4 mr-2 text-blue-500" />
              <span>Real-time Latency: ~{capabilities.estimatedLatency?.realtime}ms</span>
            </div>
            <div className="flex items-center">
              <SpeedIcon className="w-4 h-4 mr-2 text-purple-500" />
              <span>Input Tone Removal: Complete</span>
            </div>
          </div>
        )}
      </div>

      {/* Profile Selection */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h4 className="text-lg font-medium text-gray-900 mb-4">Hybrid Profile Selection</h4>
        <div className="space-y-4">
          {Object.entries(profiles).map(([key, profile]) => {
            // Add null checks for profile properties
            const performance = profile?.performance || {};
            const neutralization = profile?.neutralization || {};
            const modulation = profile?.modulation || {};
            const security = profile?.security || {};

            return (
              <div
                key={key}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  currentProfile === key
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setCurrentProfile(key)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h5 className="font-medium text-gray-900">{profile?.name || key}</h5>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        performance.realTimeMode
                          ? 'bg-green-100 text-green-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {performance.realTimeMode ? 'Real-time' : 'Offline'}
                      </span>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        {performance.qualityMode || 'balanced'}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{profile?.description || 'No description available'}</p>

                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-xs">
                      <div>
                        <span className="font-medium text-gray-500">Target Latency:</span>
                        <div className="text-gray-900">{performance.latencyTarget || 100}ms</div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-500">F0 Neutralization:</span>
                        <div className={`font-medium ${neutralization.f0Neutralization ? 'text-green-600' : 'text-gray-400'}`}>
                          {neutralization.f0Neutralization ? 'Yes' : 'No'}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-500">SoX Modulation:</span>
                        <div className={`font-medium ${modulation.enabled ? 'text-blue-600' : 'text-gray-400'}`}>
                          {modulation.enabled ? 'Enabled' : 'Disabled'}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-500">Anti-forensic:</span>
                        <div className={`font-medium ${security.antiForensic ? 'text-red-600' : 'text-gray-400'}`}>
                          {security.antiForensic ? 'Yes' : 'No'}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Recording Interface */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h4 className="text-lg font-medium text-gray-900 mb-4">Voice Recording & Processing</h4>
        
        <div className="space-y-4">
          <div className="flex items-center justify-center space-x-4">
            <button
              onClick={isRecording ? stopRecording : startRecording}
              disabled={isProcessing}
              className={`flex items-center px-6 py-3 rounded-lg font-medium transition-colors ${
                isRecording
                  ? 'bg-red-500 hover:bg-red-600 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              } disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {isRecording ? (
                <>
                  <StopIcon className="w-5 h-5 mr-2" />
                  Stop Recording ({formatTime(recordingTime)})
                </>
              ) : (
                <>
                  <MicrophoneIcon className="w-5 h-5 mr-2" />
                  Start Recording
                </>
              )}
            </button>

            {audioBlob && (
              <button
                onClick={processAudio}
                disabled={isProcessing || isRecording}
                className="flex items-center px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <SecurityIcon className="w-5 h-5 mr-2" />
                    Apply Hybrid Processing
                  </>
                )}
              </button>
            )}

            {audioBlob && (
              <button
                onClick={runPerformanceTest}
                disabled={isTesting || isRecording || isProcessing}
                className="flex items-center px-6 py-3 bg-purple-500 hover:bg-purple-600 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isTesting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Testing...
                  </>
                ) : (
                  <>
                    <SpeedIcon className="w-5 h-5 mr-2" />
                    Performance Test
                  </>
                )}
              </button>
            )}
          </div>

          {/* Processing Results */}
          {processingTime && profileData && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h5 className="font-medium text-gray-900 mb-2">Processing Results</h5>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Processing Time:</span>
                  <div className={`text-lg font-semibold ${getLatencyStatus(processingTime, profileData?.performance?.latencyTarget || 100).color}`}>
                    {processingTime}ms
                  </div>
                  <div className="text-xs text-gray-500">
                    Target: {profileData?.performance?.latencyTarget || 100}ms
                  </div>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Status:</span>
                  <div className={`text-lg font-semibold ${getLatencyStatus(processingTime, profileData?.performance?.latencyTarget || 100).color}`}>
                    {getLatencyStatus(processingTime, profileData?.performance?.latencyTarget || 100).status}
                  </div>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Input Tone:</span>
                  <div className="text-lg font-semibold text-green-600">
                    {profileData?.security?.inputToneRemoval ? 'Completely Removed' : 'Preserved'}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Audio Playback */}
          {processedAudioUrl && (
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h5 className="font-medium text-green-900 mb-2">Processed Audio</h5>
              <audio controls className="w-full">
                <source src={processedAudioUrl} type="audio/wav" />
                Your browser does not support the audio element.
              </audio>
              <p className="text-sm text-green-700 mt-2">
                ✅ Voice processed with complete input tone removal using hybrid SoX+Neutralization
              </p>
            </div>
          )}

          {/* Performance Test Results */}
          {testResults && (
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h5 className="font-medium text-blue-900 mb-3">Performance Test Results</h5>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
                <div>
                  <span className="font-medium text-blue-700">Total Tests:</span>
                  <div className="text-lg font-semibold">{testResults.summary.totalTests}</div>
                </div>
                <div>
                  <span className="font-medium text-blue-700">Average Latency:</span>
                  <div className="text-lg font-semibold">{testResults.summary.averageProcessingTime}ms</div>
                </div>
                <div>
                  <span className="font-medium text-blue-700">Real-time Capable:</span>
                  <div className={`text-lg font-semibold ${testResults.summary.realTimeCapable ? 'text-green-600' : 'text-red-600'}`}>
                    {testResults.summary.realTimeCapable ? 'Yes' : 'No'}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                {testResults.testResults.map((result: any, index: number) => (
                  <div key={index} className="flex items-center justify-between text-sm p-2 bg-white rounded">
                    <span className="font-medium">{result.profileName}</span>
                    <div className="flex items-center space-x-4">
                      <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                        {result.success ? `${result.processingTime}ms` : 'Failed'}
                      </span>
                      {result.success && (
                        <span className={`text-xs px-2 py-1 rounded ${
                          result.meetsTarget ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {result.meetsTarget ? 'On Target' : 'Over Target'}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {testResults.recommendations.bestRealTimeProfile && (
                <div className="mt-4 p-3 bg-blue-100 rounded">
                  <p className="text-sm text-blue-800">
                    <strong>Recommended:</strong> {testResults.recommendations.bestRealTimeProfile} for best real-time performance
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Error/Success Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="w-5 h-5 text-red-600 mr-2" />
                <span className="text-red-800">{error}</span>
              </div>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <CheckCircleIcon className="w-5 h-5 text-green-600 mr-2" />
                <span className="text-green-800">{success}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HybridVoiceProcessor;
