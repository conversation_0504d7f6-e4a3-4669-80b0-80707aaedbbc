/**
 * Real-time Voice Call API with WORLD Vocoder Integration
 * Handles voice call initiation, management, and real-time audio processing
 */

import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import { realTimeVoiceStreamingService } from '../../services/realTimeVoiceStreaming';
import { websocketService } from '../../services/websocket';
import { worldVocoderService, WORLD_VOICE_PROFILES } from '../../services/worldVocoderService';
import UserModel from '../../models/User';
import VoiceCallModel from '../../models/VoiceCall';
import AuditLogModel from '../../models/AuditLog';
import * as crypto from 'crypto';
import multer from 'multer';
import path from 'path';

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: { fileSize: 20 * 1024 * 1024 } // 20MB max file size
});

const router = Router();

// Helper function to get authenticated user ID
// Helper for user endpoints (legacy, used in multiple places)
function getAuthenticatedUserId(req: Request, res: Response): string | null {
  if ((req as any).user && (req as any).user.id) {
    return (req as any).user.id;
  }
  res.status(401).json({
    success: false,
    error: 'Authentication required'
  });
  return null;
}
function getAuthenticatedAdminId(req: Request, res: Response): string | null {
  if (!(req as any).admin || !(req as any).admin.id) {
    res.status(401).json({
      success: false,
      error: 'Admin authentication required'
    });
    return null;
  }
  return (req as any).admin.id;
}

/**
 * Initiate a voice call with real-time voice morphing
 */
router.post('/initiate', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { recipientId, initiatorProfile, recipientProfile } = req.body;
    const initiatorId = getAuthenticatedUserId(req, res);
    if (!initiatorId) return; // Response already sent by helper

    // Validate users exist
    const [initiator, recipient] = await Promise.all([
      UserModel.findById(initiatorId),
      UserModel.findById(recipientId)
    ]);

    if (!initiator || !recipient) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Validate voice profiles
    const availableProfiles = Object.keys(WORLD_VOICE_PROFILES);
    if (!availableProfiles.includes(initiatorProfile) || !availableProfiles.includes(recipientProfile)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid voice profile'
      });
    }

    // Check profile restrictions (NORMAL_VOICE only for regular users)
    if (initiatorProfile === 'NORMAL_VOICE' && initiator.isSuperuser) {
      return res.status(403).json({
        success: false,
        error: 'NORMAL_VOICE profile not available for superusers'
      });
    }

    if (recipientProfile === 'NORMAL_VOICE' && recipient.isSuperuser) {
      return res.status(403).json({
        success: false,
        error: 'NORMAL_VOICE profile not available for superusers'
      });
    }

    // Initiate the call
    const callId = await realTimeVoiceStreamingService.initiateCall(
      initiatorId,
      recipientId,
      initiatorProfile,
      recipientProfile
    );

    // Create call record in database
    const voiceCall = new VoiceCallModel({
      callId,
      initiatorId,
      recipientId,
      status: 'initiating',
      startTime: new Date(),
      voiceProfiles: {
        initiator: initiatorProfile,
        recipient: recipientProfile
      },
      recordingEnabled: true,
      metadata: {
        worldVocoderEnabled: worldVocoderService.isAvailable(),
        realTimeProcessing: true,
        securityLevel: 'high'
      }
    });

    await voiceCall.save();

    // Send invitation to recipient via WebSocket
    websocketService.sendVoiceCallInvitation(callId, initiatorId, recipientId, {
      initiatorName: initiator.profile?.displayName || initiator.username,
      voiceProfile: recipientProfile
    });

    // Log the call initiation
    await AuditLogModel.create({
      action: 'voice_call_initiated',
      userId: initiatorId,
      targetUserId: recipientId,
      details: {
        callId,
        initiatorProfile,
        recipientProfile,
        worldVocoderEnabled: worldVocoderService.isAvailable()
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });

    res.json({
      success: true,
      callId,
      status: 'initiating',
      profiles: {
        initiator: initiatorProfile,
        recipient: recipientProfile
      },
      worldVocoderEnabled: worldVocoderService.isAvailable()
    });

  } catch (error) {
    console.error('Voice call initiation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initiate voice call'
    });
  }
});

/**
 * Accept an incoming voice call
 */
router.post('/accept/:callId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const userId = getAuthenticatedUserId(req, res);
    if (!userId) return; // Response already sent by helper

    // Find the call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      return res.status(404).json({
        success: false,
        error: 'Call not found'
      });
    }

    // Verify user is the recipient
    if (voiceCall.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to accept this call'
      });
    }

    // Update call status
    voiceCall.status = 'ringing';
    await voiceCall.save();

    // Notify initiator that call was accepted
    if (voiceCall.initiatorId) {
      websocketService.broadcastVoiceCallEvent(callId, [voiceCall.initiatorId], {
        action: 'call_accepted',
        acceptedBy: userId
      });
    }

    res.json({
      success: true,
      callId,
      status: 'ringing',
      message: 'Call accepted, establishing connection...'
    });

  } catch (error) {
    console.error('Voice call accept error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to accept voice call'
    });
  }
});

/**
 * End a voice call
 */
router.post('/end/:callId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const userId = getAuthenticatedUserId(req, res);
    if (!userId) return; // Response already sent by helper

    // Find the call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      return res.status(404).json({
        success: false,
        error: 'Call not found'
      });
    }

    // Verify user is a participant
    if (voiceCall.initiatorId !== userId && voiceCall.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to end this call'
      });
    }

    // End the call in the streaming service
    await realTimeVoiceStreamingService.endCall(callId, userId);

    // Update call record
    voiceCall.status = 'ended';
    voiceCall.endTime = new Date();
    voiceCall.endedBy = userId;
    await voiceCall.save();

    // Notify other participant
    const otherParticipant = userId === voiceCall.initiatorId ? voiceCall.recipientId : voiceCall.initiatorId;
    if (otherParticipant) {
      websocketService.broadcastVoiceCallEvent(callId, [otherParticipant], {
        action: 'call_ended',
        endedBy: userId
      });
    }

    // Log the call end
    await AuditLogModel.create({
      action: 'voice_call_ended',
      userId,
      targetUserId: otherParticipant,
      details: {
        callId,
        duration: voiceCall.endTime.getTime() - voiceCall.startTime.getTime(),
        endedBy: userId
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });

    res.json({
      success: true,
      callId,
      status: 'ended',
      duration: voiceCall.endTime.getTime() - voiceCall.startTime.getTime()
    });

  } catch (error) {
    console.error('Voice call end error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to end voice call'
    });
  }
});

/**
 * Get call status and statistics
 */
router.get('/status/:callId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { callId } = req.params;
    const userId = getAuthenticatedUserId(req, res);
    if (!userId) return; // Response already sent by helper

    // Find the call
    const voiceCall = await VoiceCallModel.findOne({ callId });
    if (!voiceCall) {
      return res.status(404).json({
        success: false,
        error: 'Call not found'
      });
    }

    // Verify user is a participant
    if (voiceCall.initiatorId !== userId && voiceCall.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to view this call'
      });
    }

    // Get real-time statistics
    const stats = realTimeVoiceStreamingService.getCallStats(callId);

    res.json({
      success: true,
      callId,
      status: voiceCall.status,
      startTime: voiceCall.startTime,
      endTime: voiceCall.endTime,
      participants: {
        initiator: voiceCall.initiatorId,
        recipient: voiceCall.recipientId
      },
      voiceProfiles: voiceCall.voiceProfiles,
      recordingEnabled: voiceCall.metadata?.recordingEnabled ?? true,
      realTimeStats: stats,
      worldVocoderEnabled: worldVocoderService.isAvailable()
    });

  } catch (error) {
    console.error('Voice call status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get call status'
    });
  }
});

/**
 * Get available voice profiles for user
 */
router.get('/profiles', authenticateToken, async (req: Request, res: Response) => {
  try {
    // Check if admin is authenticated
    const adminId = getAuthenticatedAdminId(req, res);
    if (!adminId) return; // Response already sent by helper

    // Get all available profiles
    const allProfiles = Object.keys(WORLD_VOICE_PROFILES);

    // For admin, return all profiles
    const availableProfiles = allProfiles;

    // Get profile details
    const profileDetails = availableProfiles.map(profileName => ({
      name: profileName,
      description: getProfileDescription(profileName),
      parameters: WORLD_VOICE_PROFILES[profileName],
      userType: profileName === 'NORMAL_VOICE' ? 'regular_only' : 'all'
    }));

    res.json({
      success: true,
      profiles: profileDetails,
      worldVocoderEnabled: worldVocoderService.isNativeAvailable(),
      userType: 'superuser' // Admin panel always gets superuser privileges
    });

  } catch (error) {
    console.error('Get voice profiles error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get voice profiles'
    });
  }
});

/**
 * Get profile description for display
 */
function getProfileDescription(profileName: string): string {
  const descriptions: Record<string, string> = {
    'SECURE_DEEP_MALE': 'Deep, masculine voice with security distortion',
    'SECURE_HIGH_FEMALE': 'Higher pitch, feminine voice with light distortion',
    'ROBOTIC_SYNTHETIC': 'Mechanical, robotic voice effect',
    'WHISPER_SOFT': 'Soft, whispered voice tone',
    'DRAMATIC_BASS': 'Very deep, dramatic bass voice',
    'ETHEREAL_HIGH': 'High-pitched, ethereal voice',
    'MECHANICAL_DRONE': 'Mechanical drone-like voice',
    'WARM_TENOR': 'Warm, tenor voice quality',
    'CRYSTAL_SOPRANO': 'Clear, high soprano voice',
    'DARK_BARITONE': 'Dark, rich baritone voice',
    'BRIGHT_ALTO': 'Bright, alto voice range',
    'MYSTERIOUS_ECHO': 'Mysterious voice with echo effects',
    'ENERGETIC_YOUNG': 'Energetic, youthful voice',
    'WISE_ELDER': 'Mature, wise voice tone',
    'DIGITAL_GLITCH': 'Digital, glitched voice effect',
    'SMOOTH_RADIO': 'Smooth, radio announcer voice',
    'INTENSE_GROWL': 'Intense, growling voice',
    'GENTLE_BREEZE': 'Gentle, soft voice like a breeze',
    'POWERFUL_BOOM': 'Powerful, booming voice',
    'SUBTLE_SHIFT': 'Subtle voice modification',
    'NORMAL_VOICE': 'Natural voice (regular users only)'
  };

  return descriptions[profileName] || 'Custom voice profile';
}

/**
 * Test voice profile with uploaded audio
 */
router.post('/test-profile', authenticateToken, upload.single('audio'), async (req: Request, res: Response) => {
  try {
    const { profileName } = req.body;
    const adminId = getAuthenticatedAdminId(req, res);
    if (!adminId) return; // Response already sent by helper
    
    // Validate file exists
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Audio file is required'
      });
    }
    
    // Validate profile name
    if (!profileName || !WORLD_VOICE_PROFILES[profileName]) {
      return res.status(400).json({
        success: false,
        error: 'Valid profile name is required',
        availableProfiles: Object.keys(WORLD_VOICE_PROFILES)
      });
    }
    
    console.log(`Processing audio file (${req.file.size} bytes) with profile ${profileName}`);

    // Process audio with the selected profile
    try {
      const audioBuffer = req.file.buffer;
      const profile = WORLD_VOICE_PROFILES[profileName];
      
      console.log('🔧 Starting audio processing...');
      
      // Add comprehensive error handling for the WORLD vocoder processing
      let processedAudio: Buffer;
      try {
        processedAudio = await worldVocoderService.processAudioWithProfile(audioBuffer, profile);
        console.log('✅ Audio processing completed successfully');
      } catch (vocoderError) {
        console.error('❌ WORLD vocoder processing failed:', vocoderError);
        
        // Fallback: Create a simple WAV header with the original audio
        const wavHeader = Buffer.alloc(44);
        const totalLength = 44 + audioBuffer.length;
        
        // Write WAV header
        wavHeader.write('RIFF', 0, 4, 'ascii');
        wavHeader.writeUInt32LE(totalLength - 8, 4);
        wavHeader.write('WAVE', 8, 4, 'ascii');
        wavHeader.write('fmt ', 12, 4, 'ascii');
        wavHeader.writeUInt32LE(16, 16); // PCM format chunk size
        wavHeader.writeUInt16LE(1, 20); // PCM format
        wavHeader.writeUInt16LE(1, 22); // Mono
        wavHeader.writeUInt32LE(48000, 24); // Sample rate
        wavHeader.writeUInt32LE(96000, 28); // Byte rate
        wavHeader.writeUInt16LE(2, 32); // Block align
        wavHeader.writeUInt16LE(16, 34); // Bits per sample
        wavHeader.write('data', 36, 4, 'ascii');
        wavHeader.writeUInt32LE(audioBuffer.length, 40);
        
        processedAudio = Buffer.concat([wavHeader, audioBuffer]);
        console.log('🔄 Using fallback processing - returned original audio with WAV header');
      }
      
      // Set content type and send processed audio
      res.set({
        'Content-Type': 'audio/wav',
        'Content-Length': processedAudio.length.toString(),
        'X-Processing-Mode': 'world-vocoder',
        'X-Profile-Used': profileName
      });
      
      return res.send(processedAudio);
    } catch (processingError) {
      console.error('❌ Audio processing error:', processingError);
      let errorMessage = 'Unknown error';
      if (processingError instanceof Error) {
        errorMessage = processingError.message;
      } else if (typeof processingError === 'object' && processingError && 'message' in processingError) {
        errorMessage = (processingError as any).message;
      } else if (typeof processingError === 'string') {
        errorMessage = processingError;
      }
      return res.status(500).json({
        success: false,
        error: 'Failed to process audio with selected profile',
        details: errorMessage
      });
    }
  } catch (error) {
    console.error('❌ Test profile error:', error);
    return res.status(500).json({
      success: false,
      error: 'Server error while testing voice profile',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Test custom voice profile with uploaded audio
 */
router.post('/test-custom-profile', authenticateToken, upload.single('audio'), async (req: Request, res: Response) => {
  try {
    const { profileData } = req.body;
    const adminId = getAuthenticatedAdminId(req, res);
    if (!adminId) return; // Response already sent by helper

    // Validate file exists
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Audio file is required'
      });
    }

    // Validate and parse profile data
    let customProfile;
    try {
      customProfile = JSON.parse(profileData);
    } catch (parseError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid profile data format'
      });
    }

    // Validate profile structure
    if (!customProfile.parameters || typeof customProfile.parameters !== 'object') {
      return res.status(400).json({
        success: false,
        error: 'Profile must contain parameters object'
      });
    }

    console.log(`Processing audio file (${req.file.buffer.length} bytes) with custom profile`, customProfile.name);

    try {
      const audioBuffer = req.file.buffer;

      // Convert custom profile to WORLD voice profile format
      const worldProfile = {
        pitchScale: customProfile.parameters.pitchScale || 1.0,
        spectralWarp: customProfile.parameters.spectralWarp || 0.0,
        reverbAmount: customProfile.parameters.reverbAmount || 0.0,
        eqTilt: customProfile.parameters.eqTilt || 0.0,
        temporalJitter: customProfile.parameters.temporalJitter || 0.0,
        spectralNoise: customProfile.parameters.spectralNoise || 0.0,
        antiForensic: true // Custom profiles are always anti-forensic
      };

      console.log('🔧 Starting custom profile audio processing...');

      // Process audio with custom profile
      const processedAudio = await worldVocoderService.processAudioWithProfile(audioBuffer, worldProfile);
      console.log('✅ Custom profile audio processing completed successfully');

      // Set content type and send processed audio
      res.set({
        'Content-Type': 'audio/wav',
        'Content-Length': processedAudio.length.toString(),
        'X-Processing-Mode': 'custom-profile',
        'X-Profile-Name': customProfile.name || 'unnamed'
      });

      return res.send(processedAudio);
    } catch (processingError) {
      console.error('❌ Custom profile audio processing error:', processingError);
      return res.status(500).json({
        success: false,
        error: 'Failed to process audio with custom profile',
        details: processingError instanceof Error ? processingError.message : 'Unknown error'
      });
    }
  } catch (error: any) {
    console.error('Error in test-custom-profile endpoint:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during custom profile processing',
      details: error.message
    });
  }
});

export default router;
