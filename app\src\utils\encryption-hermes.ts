/**
 * <PERSON><PERSON>-Compatible End-to-End Encryption Utilities
 * Provides encryption/decryption for chat messages and attachments
 * Uses only Expo-compatible APIs to avoid require() issues with <PERSON><PERSON>
 */

import * as Crypto from 'expo-crypto';

export interface EncryptedData {
  data: string;
  iv: string;
  salt: string;
  isEncrypted: true;
}

export interface DecryptedData {
  data: any;
  isEncrypted: false;
}

export class HermesCompatibleEncryption {
  private static readonly KEY_LENGTH = 32; // 256 bits
  private static readonly IV_LENGTH = 16; // 128 bits
  
  /**
   * Generate a secure encryption key based on user credentials
   * Uses only Expo.Crypto to avoid CommonJS issues
   */
  static async generateKey(userId: string, deviceFingerprint: string): Promise<string> {
    const keyMaterial = `${userId}:${deviceFingerprint}:ccalc-chat-key`;
    const digest = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      keyMaterial,
      { encoding: Crypto.CryptoEncoding.HEX }
    );
    return digest;
  }

  /**
   * Generate secure random values using Expo.Crypto
   */
  static async generateSecureRandom(length: number = 16): Promise<string> {
    const randomBytes = await Crypto.getRandomBytesAsync(length);
    return Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Simple XOR-based encryption (for demonstration - in production use proper AES)
   * This avoids crypto-js dependency issues with Hermes
   */
  static async encryptMessage(
    content: any,
    userId: string,
    deviceFingerprint: string
  ): Promise<EncryptedData> {
    const key = await this.generateKey(userId, deviceFingerprint);
    const salt = await this.generateSecureRandom(16);
    const iv = await this.generateSecureRandom(16);
    
    // Convert content to JSON string
    const plaintext = JSON.stringify(content);
    
    // Simple XOR encryption (replace with proper AES in production)
    const encrypted = this.xorEncrypt(plaintext, key + salt + iv);

    return {
      data: encrypted,
      iv,
      salt,
      isEncrypted: true
    };
  }

  /**
   * Decrypt chat message content
   */
  static async decryptMessage(
    encryptedData: EncryptedData,
    userId: string,
    deviceFingerprint: string
  ): Promise<DecryptedData> {
    const key = await this.generateKey(userId, deviceFingerprint);
    
    // Simple XOR decryption (replace with proper AES in production)
    const decrypted = this.xorDecrypt(encryptedData.data, key + encryptedData.salt + encryptedData.iv);
    
    try {
      const content = JSON.parse(decrypted);
      return {
        data: content,
        isEncrypted: false
      };
    } catch (error) {
      throw new Error('Failed to decrypt message: Invalid data or key');
    }
  }

  /**
   * Simple XOR encryption/decryption
   * Note: This is for demonstration only. In production, use proper AES encryption
   */
  private static xorEncrypt(text: string, key: string): string {
    let result = '';
    for (let i = 0; i < text.length; i++) {
      const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
      result += String.fromCharCode(charCode);
    }
    return btoa(result); // Base64 encode
  }

  private static xorDecrypt(encryptedText: string, key: string): string {
    const text = atob(encryptedText); // Base64 decode
    let result = '';
    for (let i = 0; i < text.length; i++) {
      const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
      result += String.fromCharCode(charCode);
    }
    return result;
  }

  /**
   * Encrypt attachment data (images, videos, etc.)
   */
  static async encryptAttachment(
    data: string,
    userId: string,
    deviceFingerprint: string
  ): Promise<EncryptedData> {
    return this.encryptMessage({ attachment: data }, userId, deviceFingerprint);
  }

  /**
   * Decrypt attachment data
   */
  static async decryptAttachment(
    encryptedData: EncryptedData,
    userId: string,
    deviceFingerprint: string
  ): Promise<string> {
    const decrypted = await this.decryptMessage(encryptedData, userId, deviceFingerprint);
    return decrypted.data.attachment;
  }

  /**
   * Generate a secure device fingerprint
   */
  static async generateDeviceFingerprint(): Promise<string> {
    // Use device-specific information to create a unique fingerprint
    const timestamp = Date.now().toString();
    const random = await this.generateSecureRandom(32);
    const combined = `${timestamp}:${random}`;
    
    return await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      combined,
      { encoding: Crypto.CryptoEncoding.HEX }
    );
  }

  /**
   * Validate encrypted data structure
   */
  static isValidEncryptedData(data: any): data is EncryptedData {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.data === 'string' &&
      typeof data.iv === 'string' &&
      typeof data.salt === 'string' &&
      data.isEncrypted === true
    );
  }
}

// Export for backward compatibility
export const ChatEncryption = HermesCompatibleEncryption;
