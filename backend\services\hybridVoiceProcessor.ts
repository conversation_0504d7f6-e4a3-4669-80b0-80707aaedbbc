/**
 * Hybrid Voice Processing Service
 * Combines SoX modulation power with neutralization techniques
 * Provides real-time voice transformation with complete input tone removal
 * Target: <100ms latency for real-time voice calls
 */

import { spawn } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';
import { VoiceModulationService, VoiceModulationProfile } from './voiceModulation';
import { LightweightVoiceNeutralizer, NeutralizationConfig } from './lightweightVoiceNeutralizer';

export interface HybridVoiceProfile {
    name: string;
    description: string;
    type: 'hybrid';
    
    // Neutralization stage (pre-processing)
    neutralization: {
        enabled: boolean;
        f0Neutralization: boolean;
        formantNormalization: boolean;
        spectralSmoothing: number;
        noiseLevel: number;
    };
    
    // SoX modulation stage (post-processing)
    modulation: {
        enabled: boolean;
        pitch: number;
        tempo: number;
        reverb: number;
        distortion: number;
        formant: number;
        chorus: boolean;
        customize: boolean;
        customEffects?: string[];
    };
    
    // Performance settings
    performance: {
        realTimeMode: boolean;
        latencyTarget: number; // milliseconds
        qualityMode: 'speed' | 'balanced' | 'quality';
        parallelProcessing: boolean;
    };
    
    // Security features
    security: {
        reversible: boolean;
        inputToneRemoval: boolean;
        antiForensic: boolean;
        outputVariation: boolean;
    };
}

export const HYBRID_VOICE_PROFILES: Record<string, HybridVoiceProfile> = {
    // Real-time profiles optimized for <100ms
    REALTIME_HYBRID_SECURE: {
        name: 'Real-time Hybrid Secure',
        description: 'Fast, secure voice transformation with complete input tone removal',
        type: 'hybrid',
        neutralization: {
            enabled: true,
            f0Neutralization: true,
            formantNormalization: false, // Skip for speed
            spectralSmoothing: 0.3,
            noiseLevel: 0.01
        },
        modulation: {
            enabled: true,
            pitch: -4,
            tempo: 0.95,
            reverb: 15,
            distortion: 8,
            formant: -200,
            chorus: false,
            customize: false
        },
        performance: {
            realTimeMode: true,
            latencyTarget: 80,
            qualityMode: 'speed',
            parallelProcessing: true
        },
        security: {
            reversible: false,
            inputToneRemoval: true,
            antiForensic: true,
            outputVariation: false
        }
    },

    REALTIME_HYBRID_BALANCED: {
        name: 'Real-time Hybrid Balanced',
        description: 'Balanced quality and speed with input neutralization',
        type: 'hybrid',
        neutralization: {
            enabled: true,
            f0Neutralization: true,
            formantNormalization: true,
            spectralSmoothing: 0.25,
            noiseLevel: 0.008
        },
        modulation: {
            enabled: true,
            pitch: -2,
            tempo: 0.98,
            reverb: 12,
            distortion: 5,
            formant: -150,
            chorus: false,
            customize: false
        },
        performance: {
            realTimeMode: true,
            latencyTarget: 100,
            qualityMode: 'balanced',
            parallelProcessing: true
        },
        security: {
            reversible: false,
            inputToneRemoval: true,
            antiForensic: true,
            outputVariation: true
        }
    },

    // High-quality profiles for recorded messages
    OFFLINE_HYBRID_PREMIUM: {
        name: 'Offline Hybrid Premium',
        description: 'Maximum quality hybrid processing with complete voice anonymization',
        type: 'hybrid',
        neutralization: {
            enabled: true,
            f0Neutralization: true,
            formantNormalization: true,
            spectralSmoothing: 0.4,
            noiseLevel: 0.015
        },
        modulation: {
            enabled: true,
            pitch: -6,
            tempo: 0.92,
            reverb: 20,
            distortion: 10,
            formant: -300,
            chorus: true,
            customize: true,
            customEffects: ['lowpass', '7000', 'highpass', '300', 'compand', '0.02,0.20', '5:-60,-40,-10', '-5', '-90', '0.1']
        },
        performance: {
            realTimeMode: false,
            latencyTarget: 500,
            qualityMode: 'quality',
            parallelProcessing: false
        },
        security: {
            reversible: false,
            inputToneRemoval: true,
            antiForensic: true,
            outputVariation: true
        }
    }
};

export class HybridVoiceProcessor {
    private tempDir: string;
    private neutralizer: LightweightVoiceNeutralizer;
    private modulator: VoiceModulationService;
    private soxPath: string;
    private soxAvailable: boolean;

    constructor() {
        this.tempDir = path.join(__dirname, '../temp/hybrid');
        this.neutralizer = new LightweightVoiceNeutralizer();
        this.modulator = new VoiceModulationService();
        this.soxPath = process.env.SOX_PATH || 'sox';
        this.soxAvailable = false;
        this.ensureTempDir();
        this.checkSoxAvailability();
    }

    private async checkSoxAvailability(): Promise<void> {
        try {
            const { spawn } = require('child_process');
            const process = spawn(this.soxPath, ['--version'], { stdio: 'pipe' });
            
            await new Promise((resolve, reject) => {
                let resolved = false;
                const timeout = setTimeout(() => {
                    if (!resolved) {
                        resolved = true;
                        process.kill();
                        reject(new Error('SoX check timeout'));
                    }
                }, 2000);

                process.on('close', (code: number) => {
                    if (!resolved) {
                        resolved = true;
                        clearTimeout(timeout);
                        this.soxAvailable = (code === 0);
                        resolve(void 0);
                    }
                });

                process.on('error', () => {
                    if (!resolved) {
                        resolved = true;
                        clearTimeout(timeout);
                        this.soxAvailable = false;
                        resolve(void 0);
                    }
                });
            });
        } catch (error) {
            this.soxAvailable = false;
        }
    }

    private async ensureTempDir(): Promise<void> {
        try {
            await fs.mkdir(this.tempDir, { recursive: true });
        } catch (error) {
            console.error('Failed to create hybrid temp directory:', error);
        }
    }

    /**
     * Main hybrid processing function
     * Combines neutralization + modulation for complete voice transformation
     */
    async processVoice(
        inputBuffer: Buffer,
        profile: HybridVoiceProfile,
        userId?: string
    ): Promise<Buffer> {
        const sessionId = crypto.randomUUID();
        const startTime = Date.now();

        try {
            console.log(`🔄 Starting hybrid voice processing: ${profile.name}`);

            let processedBuffer = inputBuffer;

            // Stage 1: Voice Neutralization (remove input characteristics)
            if (profile.neutralization.enabled) {
                const neutralConfig: NeutralizationConfig = {
                    level: 'MEDIUM',
                    preserveClarity: true,
                    realTimeMode: profile.performance.realTimeMode,
                    latencyTarget: Math.floor(profile.performance.latencyTarget * 0.4), // 40% of budget
                    processing: {
                        f0Neutralization: profile.neutralization.f0Neutralization,
                        formantNormalization: profile.neutralization.formantNormalization,
                        spectralSmoothing: profile.neutralization.spectralSmoothing,
                        temporalJitter: 2,
                        noiseLevel: profile.neutralization.noiseLevel
                    }
                };

                console.log(`🎯 Applying neutralization stage...`);
                processedBuffer = await this.neutralizer.neutralizeVoice(
                    processedBuffer,
                    neutralConfig,
                    userId
                );
            }

            // Stage 2: SoX Modulation (apply voice transformation)
            if (profile.modulation.enabled) {
                const modulationProfile: VoiceModulationProfile = {
                    pitch: profile.modulation.pitch,
                    tempo: profile.modulation.tempo,
                    reverb: profile.modulation.reverb,
                    distortion: profile.modulation.distortion,
                    formant: profile.modulation.formant,
                    chorus: profile.modulation.chorus,
                    normalize: true,
                    description: `Hybrid modulation for ${profile.name}`
                };

                console.log(`🎛️ Applying modulation stage...`);
                
                if (this.soxAvailable && profile.performance.realTimeMode) {
                    // Use optimized real-time SoX processing when available
                    processedBuffer = await this.applyOptimizedSoXModulation(
                        processedBuffer,
                        modulationProfile,
                        profile,
                        sessionId
                    );
                } else {
                    // Fallback to standard modulation service (works without SoX)
                    console.log(this.soxAvailable ? '🎵 Using standard modulation (offline mode)' : '⚠️ SoX not available, using fallback modulation');
                    processedBuffer = await this.modulator.modulateVoice(
                        processedBuffer,
                        modulationProfile,
                        userId
                    );
                }
            }

            // Stage 3: Anti-forensic processing (if enabled)
            if (profile.security.antiForensic) {
                processedBuffer = await this.applyAntiForensicProcessing(
                    processedBuffer,
                    profile,
                    sessionId
                );
            }

            const processingTime = Date.now() - startTime;
            console.log(`✅ Hybrid processing completed in ${processingTime}ms (target: ${profile.performance.latencyTarget}ms)`);

            // Log performance metrics
            if (userId) {
                console.log(`Hybrid voice processing for user ${userId}: ${profile.name}, ${processingTime}ms`);
            }

            // Warn if target latency exceeded
            if (profile.performance.realTimeMode && processingTime > profile.performance.latencyTarget) {
                console.warn(`⚠️ Latency target exceeded: ${processingTime}ms > ${profile.performance.latencyTarget}ms`);
            }

            return processedBuffer;

        } catch (error) {
            console.error('Hybrid voice processing failed:', error);
            throw new Error('Hybrid voice processing failed');
        }
    }

    /**
     * Optimized SoX processing for real-time use
     * Uses streamlined command set for <60ms processing
     */
    private async applyOptimizedSoXModulation(
        inputBuffer: Buffer,
        profile: VoiceModulationProfile,
        hybridProfile: HybridVoiceProfile,
        sessionId: string
    ): Promise<Buffer> {
        const inputFile = path.join(this.tempDir, `input_${sessionId}.wav`);
        const outputFile = path.join(this.tempDir, `output_${sessionId}.wav`);

        try {
            // Write input to temp file
            await fs.writeFile(inputFile, inputBuffer);

            // Build optimized SoX command for speed
            const soxArgs = this.buildOptimizedSoXCommand(inputFile, outputFile, profile, hybridProfile);

            // Execute SoX with timeout for real-time constraints
            await this.executeSoXWithTimeout(soxArgs, hybridProfile.performance.latencyTarget - 40);

            // Read result
            const result = await fs.readFile(outputFile);
            
            // Cleanup
            await this.cleanup([inputFile, outputFile]);

            return result;

        } catch (error) {
            await this.cleanup([inputFile, outputFile]);
            throw error;
        }
    }

    /**
     * Build optimized SoX command for real-time processing
     */
    private buildOptimizedSoXCommand(
        inputFile: string,
        outputFile: string,
        profile: VoiceModulationProfile,
        hybridProfile: HybridVoiceProfile
    ): string[] {
        const args = [inputFile, outputFile];

        // Optimize based on quality mode
        if (hybridProfile.performance.qualityMode === 'speed') {
            // Speed-optimized effects only
            if (profile.pitch !== 0) {
                args.push('pitch', profile.pitch.toString());
            }
            if (profile.tempo !== 1.0) {
                args.push('tempo', profile.tempo.toString());
            }
            // Skip heavy effects for speed
            
        } else if (hybridProfile.performance.qualityMode === 'balanced') {
            // Balanced effects
            if (profile.pitch !== 0) {
                args.push('pitch', profile.pitch.toString());
            }
            if (profile.tempo !== 1.0) {
                args.push('tempo', profile.tempo.toString());
            }
            if (profile.reverb > 0) {
                args.push('reverb', Math.min(profile.reverb, 15).toString()); // Limited reverb
            }
            if (profile.distortion > 0) {
                args.push('overdrive', Math.min(profile.distortion, 10).toString());
            }
            
        } else {
            // Quality mode - full effects
            if (profile.pitch !== 0) {
                args.push('pitch', profile.pitch.toString());
            }
            if (profile.tempo !== 1.0) {
                args.push('tempo', profile.tempo.toString());
            }
            if (profile.formant !== 0) {
                args.push('bend', '0.1', `${profile.formant},${profile.pitch * 50}`, '0.1');
            }
            if (profile.reverb > 0) {
                args.push('reverb', profile.reverb.toString());
            }
            if (profile.distortion > 0) {
                args.push('overdrive', profile.distortion.toString());
            }
            if (profile.chorus) {
                args.push('chorus', '0.7', '0.9', '55', '0.4', '0.25', '2', '-t');
            }
            
            // Add custom effects if specified
            if (hybridProfile.modulation.customize && hybridProfile.modulation.customEffects) {
                args.push(...hybridProfile.modulation.customEffects);
            }
        }

        // Always normalize for consistency
        args.push('norm', '-3');

        return args;
    }

    /**
     * Execute SoX with timeout for real-time constraints
     */
    private async executeSoXWithTimeout(args: string[], timeoutMs: number): Promise<void> {
        return new Promise((resolve, reject) => {
            const soxProcess = spawn(this.soxPath, args, {
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let stderr = '';
            let isCompleted = false;

            // Set timeout
            const timeout = setTimeout(() => {
                if (!isCompleted) {
                    soxProcess.kill('SIGKILL');
                    reject(new Error(`SoX processing timed out after ${timeoutMs}ms`));
                }
            }, timeoutMs);

            soxProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            soxProcess.on('close', (code) => {
                isCompleted = true;
                clearTimeout(timeout);
                
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`SoX process failed with code ${code}: ${stderr}`));
                }
            });

            soxProcess.on('error', (error) => {
                isCompleted = true;
                clearTimeout(timeout);
                reject(new Error(`SoX execution failed: ${error.message}`));
            });
        });
    }

    /**
     * Apply anti-forensic processing to prevent voice analysis
     */
    private async applyAntiForensicProcessing(
        inputBuffer: Buffer,
        profile: HybridVoiceProfile,
        sessionId: string
    ): Promise<Buffer> {
        console.log('🛡️ Applying anti-forensic processing...');

        // Add subtle random variations to prevent consistent patterns
        const audioData = this.bufferToFloat32Array(inputBuffer);
        
        // Apply random micro-variations
        for (let i = 0; i < audioData.length; i++) {
            if (Math.random() < 0.001) { // 0.1% of samples
                audioData[i] += (Math.random() - 0.5) * 0.001;
            }
        }

        return this.float32ArrayToBuffer(audioData);
    }

    /**
     * Real-time streaming interface for voice calls
     */
    async processVoiceStream(
        audioStream: NodeJS.ReadableStream,
        profile: HybridVoiceProfile,
        userId?: string
    ): Promise<NodeJS.ReadableStream> {
        console.log(`🔄 Starting hybrid stream processing: ${profile.name}`);

        // For real-time streaming, we need to process in chunks
        const { Readable, Transform } = require('stream');

        const processingTransform = new Transform({
            objectMode: false,
            transform: async (chunk: Buffer, encoding: any, callback: any) => {
                try {
                    const processed = await this.processVoice(chunk, profile, userId);
                    callback(null, processed);
                } catch (error) {
                    callback(error);
                }
            }
        });

        return audioStream.pipe(processingTransform);
    }

    /**
     * Check hybrid processing capabilities
     */
    async checkCapabilities(): Promise<{
        neutralizationAvailable: boolean;
        soxAvailable: boolean;
        hybridAvailable: boolean;
        estimatedLatency: {
            realtime: number;
            balanced: number;
            quality: number;
        };
    }> {
        // Wait a bit for SoX availability check to complete if needed
        if (this.soxAvailable === false) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        return {
            neutralizationAvailable: true, // Always available (pure JS)
            soxAvailable: this.soxAvailable,
            hybridAvailable: true, // Always available with fallback modulation
            estimatedLatency: {
                realtime: 80,   // ms
                balanced: 120,  // ms
                quality: 300    // ms
            }
        };
    }

    /**
     * Get available hybrid profiles
     */
    getAvailableProfiles(): Record<string, HybridVoiceProfile> {
        return HYBRID_VOICE_PROFILES;
    }

    /**
     * Check if SoX is available for advanced modulation (public method)
     */
    async isSoxAvailable(): Promise<boolean> {
        return this.soxAvailable;
    }

    /**
     * Get SoX availability status
     */
    getSoxAvailability(): boolean {
        return this.soxAvailable;
    }
    
    /**
     * Utility: Convert buffer to Float32Array
     */
    private bufferToFloat32Array(buffer: Buffer): Float32Array {
        // Assuming 16-bit PCM WAV data
        const samples = new Int16Array(buffer.buffer, buffer.byteOffset + 44, (buffer.length - 44) / 2);
        const floatSamples = new Float32Array(samples.length);
        
        for (let i = 0; i < samples.length; i++) {
            floatSamples[i] = samples[i] / 32768; // Convert to -1.0 to 1.0 range
        }
        
        return floatSamples;
    }

    /**
     * Utility: Convert Float32Array to buffer
     */
    private float32ArrayToBuffer(floatData: Float32Array): Buffer {
        // Convert back to 16-bit PCM
        const samples = new Int16Array(floatData.length);
        
        for (let i = 0; i < floatData.length; i++) {
            samples[i] = Math.max(-32768, Math.min(32767, floatData[i] * 32768));
        }
        
        // Create WAV header + data
        const dataSize = samples.length * 2;
        const buffer = Buffer.alloc(44 + dataSize);
        
        // WAV header
        buffer.write('RIFF', 0);
        buffer.writeUInt32LE(36 + dataSize, 4);
        buffer.write('WAVE', 8);
        buffer.write('fmt ', 12);
        buffer.writeUInt32LE(16, 16);
        buffer.writeUInt16LE(1, 20);
        buffer.writeUInt16LE(1, 22);
        buffer.writeUInt32LE(44100, 24);
        buffer.writeUInt32LE(44100 * 2, 28);
        buffer.writeUInt16LE(2, 32);
        buffer.writeUInt16LE(16, 34);
        buffer.write('data', 36);
        buffer.writeUInt32LE(dataSize, 40);
        
        // Copy sample data
        Buffer.from(samples.buffer).copy(buffer, 44);
        
        return buffer;
    }

    /**
     * Cleanup temporary files
     */
    private async cleanup(files: string[]): Promise<void> {
        for (const file of files) {
            try {
                await fs.unlink(file);
            } catch (error) {
                // Ignore cleanup errors
            }
        }
    }
}
