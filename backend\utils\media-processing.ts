import sharp from 'sharp';
import ffmpeg from 'fluent-ffmpeg';
import path from 'path';

export async function generateThumbnail(input: string | Buffer, mimeType: string): Promise<Buffer | null> {
  try {
    if (mimeType.startsWith('image/')) {
      // Handle both file path and buffer input
      let sharpInput: string | Buffer;
      if (typeof input === 'string') {
        // If it's a base64 string, convert to buffer
        if (input.startsWith('data:') || input.match(/^[A-Za-z0-9+/=]+$/)) {
          sharpInput = Buffer.from(input.replace(/^data:image\/[a-z]+;base64,/, ''), 'base64');
        } else {
          // It's a file path
          sharpInput = input;
        }
      } else {
        // It's already a buffer
        sharpInput = input;
      }

      return await sharp(sharpInput)
        .resize(200, 200, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .jpeg({ quality: 80 })
        .toBuffer();
    }

    if (mimeType.startsWith('video/')) {
      return new Promise(async (resolve, reject) => {
        try {
          let tempVideoPath: string;

          // Handle buffer input by writing to temp file
          if (typeof input !== 'string') {
            const tempDir = path.join(process.cwd(), 'temp');
            if (!require('fs').existsSync(tempDir)) {
              require('fs').mkdirSync(tempDir, { recursive: true });
            }

            tempVideoPath = path.join(tempDir, `video_${Date.now()}.mp4`);
            await require('fs').promises.writeFile(tempVideoPath, input);
          } else {
            tempVideoPath = input;
          }

          const outputPath = path.join(process.cwd(), 'temp', `thumb_${Date.now()}.jpg`);

          ffmpeg(tempVideoPath)
            .screenshot({
              timestamps: ['50%'],
              filename: path.basename(outputPath),
              folder: path.dirname(outputPath),
              size: '200x200'
            })
            .on('end', async () => {
              try {
                const thumbnail = await sharp(outputPath)
                  .jpeg({ quality: 80 })
                  .toBuffer();

                // Clean up temp files
                if (typeof input !== 'string') {
                  require('fs').unlinkSync(tempVideoPath);
                }
                require('fs').unlinkSync(outputPath);

                resolve(thumbnail);
              } catch (error) {
                reject(error);
              }
            })
            .on('error', (error) => {
              // Clean up temp files on error
              if (typeof input !== 'string') {
                try { require('fs').unlinkSync(tempVideoPath); } catch { }
              }
              try { require('fs').unlinkSync(outputPath); } catch { }
              reject(error);
            });
        } catch (error) {
          reject(error);
        }
      });
    }

    return null;
  } catch (error) {
    console.error('Thumbnail generation error:', error);
    return null;
  }
}
