/**
 * Enhanced Voice Modulation Controller
 * Handles advanced voice modulation with SoX integration and tone modification
 */

import { Request, Response } from 'express';
import { VoiceModulationService } from '../../services/voiceModulation';
import { VOICE_PROFILES, CUSTOM_PROFILE_EXAMPLES } from '../../services/voiceModulation';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';

const voiceModulationService = new VoiceModulationService();

/**
 * Get all enhanced voice profiles
 * GET /api/voice/profiles/enhanced
 */
export const getEnhancedProfiles = async (req: Request, res: Response): Promise<void> => {
  try {
    // Combine built-in profiles with custom examples
    const builtInProfiles = Object.entries(VOICE_PROFILES).map(([name, profile]) => ({
      name,
      ...profile,
      // Ensure all profiles have all unified properties
      pitchScale: profile.pitchScale ?? 1.0,
      spectralWarp: profile.spectralWarp ?? 0.0,
      reverbAmount: profile.reverbAmount ?? 0.0,
      eqTilt: profile.eqTilt ?? 0.0,
      temporalJitter: profile.temporalJitter ?? 0.0,
      spectralNoise: profile.spectralNoise ?? 0.0,
      bassGain: profile.bassGain ?? 0,
      trebleGain: profile.trebleGain ?? 0,
      midGain: profile.midGain ?? 0,
      compandRatio: profile.compandRatio ?? 1.0,
      spectralTilt: profile.spectralTilt ?? 0,
      harmonicDistortion: profile.harmonicDistortion ?? 0,
      vocoderStrength: ('vocoderStrength' in profile ? (profile as any).vocoderStrength : 0),
      eqBands: profile.eqBands ?? [],
      preserveClarity: profile.preserveClarity !== false,
      antiForensic: profile.antiForensic ?? false,
      realTimeOptimized: profile.realTimeOptimized ?? false,
      isCustom: false,
      presetType: 'advanced' as const, // All profiles are now advanced
      customSoxArgs: [],
      advancedMode: false
    }));

    const customProfiles = Object.entries(CUSTOM_PROFILE_EXAMPLES).map(([name, profile]) => ({
      ...profile,
      isCustom: true,
      customSoxArgs: [],
      advancedMode: false
    }));

    const allProfiles = [...builtInProfiles, ...customProfiles];

    res.json({
      success: true,
      profiles: allProfiles,
      count: allProfiles.length
    });
  } catch (error: any) {
    console.error('Failed to get enhanced profiles:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve voice profiles'
    });
  }
};

/**
 * Create a new enhanced voice profile
 * POST /api/voice/profiles/enhanced
 */
export const createEnhancedProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      name,
      description,
      // Basic parameters
      pitch,
      tempo,
      reverb,
      distortion,
      formant,
      chorus,
      normalize,
      // WORLD Vocoder parameters
      pitchScale,
      spectralWarp,
      reverbAmount,
      eqTilt,
      temporalJitter,
      spectralNoise,
      // Enhanced tone modification
      bassGain,
      trebleGain,
      midGain,
      compandRatio,
      spectralTilt,
      harmonicDistortion,
      vocoderStrength,
      eqBands,
      // Processing preferences
      preserveClarity,
      antiForensic,
      realTimeOptimized,
      // Configuration
      customSoxArgs,
      advancedMode,
      presetType,
      userType
    } = req.body;

    if (!name || !description) {
      res.status(400).json({
        success: false,
        error: 'Profile name and description are required'
      });
      return;
    }

    // Validate parameters
    const validatedProfile = {
      name: name.trim(),
      description: description.trim(),

      // Basic parameters
      pitch: Math.max(-12, Math.min(12, parseFloat(pitch) || 0)),
      tempo: Math.max(0.5, Math.min(2.0, parseFloat(tempo) || 1.0)),
      reverb: Math.max(0, Math.min(100, parseInt(reverb) || 0)),
      distortion: Math.max(0, Math.min(100, parseInt(distortion) || 0)),
      formant: Math.max(-1000, Math.min(1000, parseInt(formant) || 0)),
      chorus: Boolean(chorus),
      normalize: Boolean(normalize !== false),

      // WORLD Vocoder parameters
      pitchScale: Math.max(0.7, Math.min(1.3, parseFloat(pitchScale) || 1.0)),
      spectralWarp: Math.max(-10, Math.min(10, parseFloat(spectralWarp) || 0.0)),
      reverbAmount: Math.max(0, Math.min(50, parseFloat(reverbAmount) || 0.0)),
      eqTilt: Math.max(-6, Math.min(6, parseFloat(eqTilt) || 0.0)),
      temporalJitter: Math.max(0, Math.min(10, parseFloat(temporalJitter) || 0.0)),
      spectralNoise: Math.max(0, Math.min(10, parseFloat(spectralNoise) || 0.0)),

      // Enhanced tone modification
      bassGain: Math.max(-20, Math.min(20, parseFloat(bassGain) || 0)),
      trebleGain: Math.max(-20, Math.min(20, parseFloat(trebleGain) || 0)),
      midGain: Math.max(-20, Math.min(20, parseFloat(midGain) || 0)),
      compandRatio: Math.max(1.0, Math.min(10.0, parseFloat(compandRatio) || 1.0)),
      spectralTilt: Math.max(-6, Math.min(6, parseFloat(spectralTilt) || 0)),
      harmonicDistortion: Math.max(0, Math.min(50, parseInt(harmonicDistortion) || 0)),
      vocoderStrength: Math.max(0, Math.min(100, parseInt(vocoderStrength) || 0)),

      // Multi-band EQ
      eqBands: Array.isArray(eqBands) ? eqBands.map((band: any) => ({
        frequency: Math.max(20, Math.min(20000, parseInt(band.frequency) || 1000)),
        gain: Math.max(-20, Math.min(20, parseFloat(band.gain) || 0)),
        width: Math.max(0.1, Math.min(5.0, parseFloat(band.width) || 1.0))
      })) : [],

      // Processing preferences
      preserveClarity: Boolean(preserveClarity !== false),
      antiForensic: Boolean(antiForensic),
      realTimeOptimized: Boolean(realTimeOptimized),

      // Configuration
      customSoxArgs: Array.isArray(customSoxArgs) ? customSoxArgs.filter((arg: any) =>
        typeof arg === 'string' && arg.trim()
      ) : [],
      advancedMode: Boolean(advancedMode),
      presetType: ['basic', 'advanced', 'expert'].includes(presetType) ? presetType : 'basic',
      userType: ['all', 'regular', 'superuser'].includes(userType) ? userType : 'all',
      isCustom: true
    };

    // TODO: Save to database or file system
    // For now, we'll just validate and return success
    console.log('Enhanced profile created:', validatedProfile);

    res.json({
      success: true,
      message: 'Enhanced voice profile created successfully',
      profile: validatedProfile
    });
  } catch (error: any) {
    console.error('Failed to create enhanced profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create voice profile'
    });
  }
};

/**
 * Test an enhanced voice profile
 * POST /api/voice/test-enhanced-profile
 */
export const testEnhancedProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { profile, generateSample } = req.body;

    if (!profile) {
      res.status(400).json({
        success: false,
        error: 'Profile is required'
      });
      return;
    }

    let audioBuffer: Buffer;

    if (generateSample) {
      // Generate a test audio sample
      audioBuffer = await voiceModulationService.generateProfileSample(
        profile.name || 'test',
        profile
      );
    } else {
      // Use uploaded audio file (if any)
      // For now, generate a sample
      audioBuffer = await voiceModulationService.generateProfileSample(
        profile.name || 'test',
        profile
      );
    }

    // Set appropriate headers for audio response
    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('Content-Length', audioBuffer.length);
    res.setHeader('Content-Disposition', `attachment; filename="${profile.name || 'test'}_sample.wav"`);

    res.send(audioBuffer);
  } catch (error: any) {
    console.error('Failed to test enhanced profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test voice profile'
    });
  }
};

/**
 * Test an enhanced voice profile with uploaded audio
 * POST /api/voice/test-enhanced-profile-upload
 */
export const testEnhancedProfileWithUpload = async (req: Request, res: Response): Promise<void> => {
  try {
    const audioFile = req.file;
    const profileData = req.body.profile;

    if (!audioFile) {
      res.status(400).json({
        success: false,
        error: 'Audio file is required'
      });
      return;
    }

    if (!profileData) {
      res.status(400).json({
        success: false,
        error: 'Profile data is required'
      });
      return;
    }

    let profile;
    try {
      profile = typeof profileData === 'string' ? JSON.parse(profileData) : profileData;
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Invalid profile data format'
      });
      return;
    }

    // Process the uploaded audio with the profile
    const audioBuffer = await voiceModulationService.processUploadedAudio(
      audioFile.buffer,
      audioFile.originalname,
      profile
    );

    // Set appropriate headers for audio response
    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('Content-Length', audioBuffer.length);
    res.setHeader('Content-Disposition', `attachment; filename="${profile.name || 'processed'}_${audioFile.originalname}"`);

    res.send(audioBuffer);
  } catch (error: any) {
    console.error('Failed to test enhanced profile with upload:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process uploaded audio with voice profile'
    });
  }
};

/**
 * Get enhanced profile by name
 * GET /api/voice/profiles/enhanced/:name
 */
export const getEnhancedProfileByName = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name } = req.params;

    // Check built-in profiles first
    const builtInProfile = (VOICE_PROFILES as any)[name];
    if (builtInProfile) {
      res.json({
        success: true,
        profile: {
          name,
          ...builtInProfile,
          isCustom: false,
          presetType: 'basic',
          customSoxArgs: [],
          advancedMode: false
        }
      });
      return;
    }

    // Check custom profile examples
    const customProfile = (CUSTOM_PROFILE_EXAMPLES as any)[name];
    if (customProfile) {
      res.json({
        success: true,
        profile: {
          ...customProfile,
          isCustom: true,
          customSoxArgs: [],
          advancedMode: false
        }
      });
      return;
    }

    res.status(404).json({
      success: false,
      error: 'Profile not found'
    });
  } catch (error: any) {
    console.error('Failed to get enhanced profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve voice profile'
    });
  }
};

/**
 * Update user's voice profile with enhanced settings
 * PUT /api/voice/users/:userId/enhanced-profile
 */
export const updateUserEnhancedProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const { profileName, customSettings } = req.body;

    if (!userId || !profileName) {
      res.status(400).json({
        success: false,
        error: 'User ID and profile name are required'
      });
      return;
    }

    // TODO: Update user's voice profile in database
    // This would integrate with the existing user management system

    console.log(`Updated enhanced voice profile for user ${userId}:`, {
      profileName,
      customSettings
    });

    res.json({
      success: true,
      message: 'User voice profile updated successfully',
      userId,
      profileName,
      customSettings
    });
  } catch (error: any) {
    console.error('Failed to update user enhanced profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user voice profile'
    });
  }
};

/**
 * Get SoX capabilities and available effects
 * GET /api/voice/sox-capabilities
 */
export const getSoxCapabilities = async (_req: Request, res: Response): Promise<void> => {
  try {
    const isAvailable = await voiceModulationService.checkSoxAvailability();
    
    const capabilities = {
      available: isAvailable,
      effects: [
        'pitch', 'tempo', 'reverb', 'overdrive', 'chorus', 'equalizer',
        'bass', 'treble', 'compand', 'lowpass', 'highpass', 'bend',
        'tremolo', 'phaser', 'echo', 'delay', 'flanger'
      ],
      formats: ['wav', 'mp3', 'flac', 'ogg'],
      sampleRates: [8000, 16000, 22050, 44100, 48000, 96000],
      bitDepths: [8, 16, 24, 32]
    };

    res.json({
      success: true,
      capabilities
    });
  } catch (error: any) {
    console.error('Failed to get SoX capabilities:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve SoX capabilities'
    });
  }
};
