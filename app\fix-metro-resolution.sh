#!/bin/bash

# CCALC App Metro Resolution Fix Script
# Fixes the "Unable to resolve module ./app/index" error

echo "🔧 CCALC Metro Resolution Fix"
echo "=============================="

# Change to app directory
cd "$(dirname "$0")"
echo "📁 Working directory: $(pwd)"

# Step 1: Clear all caches
echo ""
echo "🧹 Step 1: Clearing all caches..."
rm -rf node_modules/.cache
rm -rf .expo
rm -rf dist
npx expo start --clear >/dev/null 2>&1 || true
npx react-native start --reset-cache >/dev/null 2>&1 || true

# Step 2: Verify file structure
echo ""
echo "📋 Step 2: Verifying file structure..."
if [ ! -f "index.js" ]; then
    echo "❌ ERROR: index.js not found in app directory"
    exit 1
fi

if [ ! -f "package.json" ]; then
    echo "❌ ERROR: package.json not found in app directory"
    exit 1
fi

if [ ! -f "src/App.tsx" ]; then
    echo "❌ ERROR: src/App.tsx not found"
    exit 1
fi

echo "✅ All required files exist"

# Step 3: Check and fix package.json main field
echo ""
echo "📦 Step 3: Checking package.json main field..."
if ! grep -q '"main": "index.js"' package.json; then
    echo "⚠️  Fixing package.json main field..."
    # This will be handled manually if needed
fi

# Step 4: Clear Metro cache specifically
echo ""
echo "🚇 Step 4: Clearing Metro cache..."
npx expo r -c || npx expo start --clear --no-open >/dev/null 2>&1 &
EXPO_PID=$!
sleep 5
kill $EXPO_PID 2>/dev/null || true

# Step 5: Check Metro config
echo ""
echo "⚙️  Step 5: Verifying Metro configuration..."
if [ -f "metro.config.js" ]; then
    echo "✅ Metro config exists"
else
    echo "❌ Metro config missing"
fi

# Step 6: Ensure we're not in workspace root
echo ""
echo "🏗️  Step 6: Checking workspace structure..."
if [ -f "../package.json" ] && grep -q '"workspaces"' ../package.json; then
    echo "✅ Confirmed we're in a workspace setup"
    echo "📁 Make sure to always run 'npm start' from the app/ directory"
else
    echo "⚠️  Not in expected workspace structure"
fi

# Step 7: Install missing dependencies
echo ""
echo "📚 Step 7: Checking dependencies..."
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Step 8: Final verification
echo ""
echo "✅ Step 8: Final verification..."
echo "   📱 Entry point: $(grep '"main"' package.json | cut -d'"' -f4)"
echo "   📂 Current directory: $(pwd)"
echo "   🗂️  Index.js exists: $([ -f "index.js" ] && echo "YES" || echo "NO")"

echo ""
echo "🚀 Fix completed! To start the app:"
echo "   1. Make sure you're in the app/ directory"
echo "   2. Run: npm run start:clear"
echo "   3. Or run: npx expo start --clear"
echo ""
echo "🔍 If the issue persists:"
echo "   - Delete node_modules: rm -rf node_modules && npm install"
echo "   - Clear all Expo cache: rm -rf ~/.expo"
echo "   - Restart your terminal and try again"
