const fs = require('fs');
const path = require('path');

function scanDir(dir, badFiles = []) {
  for (const entry of fs.readdirSync(dir)) {
    const fullPath = path.join(dir, entry);
    if (fs.statSync(fullPath).isDirectory()) {
      scanDir(fullPath, badFiles);
    } else if (entry.endsWith('.json')) {
      try {
        const content = fs.readFileSync(fullPath, 'utf8').trim();
        // Check for empty, only quotes, or suspicious content
        if (
          content === '' ||
          content === '""' ||
          content === "''"
        ) {
          badFiles.push({ file: fullPath, error: 'Empty or only quotes' });
        } else {
          JSON.parse(content);
        }
      } catch (err) {
        badFiles.push({ file: fullPath, error: err.message });
      }
    }
  }
  return badFiles;
}

const root = process.cwd();
const badFiles = scanDir(root);

if (badFiles.length === 0) {
  console.log('✅ All JSON files are valid.');
} else {
  console.log('❌ Problematic JSON files found:');
  badFiles.forEach(({ file, error }) => {
    console.log(`- ${file}: ${error}`);
  });
}
