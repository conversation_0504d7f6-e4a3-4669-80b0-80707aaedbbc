/**
 * End-to-End Encryption Utilities
 * Provides encryption/decryption for chat messages and attachments
 * Uses Hermes-compatible fallback to avoid require() issues
 */

import { HermesCompatibleEncryption } from './encryption-hermes';

export interface EncryptedData {
  data: string;
  iv: string;
  salt: string;
  isEncrypted: true;
}

export interface DecryptedData {
  data: any;
  isEncrypted: false;
}

export class ChatEncryption {
  /**
   * Generate a secure encryption key based on user credentials
   */
  static async generateKey(userId: string, deviceFingerprint: string): Promise<string> {
    // Always use Hermes-compatible encryption
    return HermesCompatibleEncryption.generateKey(userId, deviceFingerprint);
  }

  /**
   * Generate secure random values with fallback
   */
  static async generateSecureRandom(purpose: string): Promise<string> {
    // Always use Hermes-compatible encryption
    return HermesCompatibleEncryption.generateSecureRandom(16);
  }

  /**
   * Encrypt chat message content
   */
  static async encryptMessage(
    content: any,
    userId: string,
    deviceFingerprint: string
  ): Promise<EncryptedData> {
    // Always use Hermes-compatible encryption
    return HermesCompatibleEncryption.encryptMessage(content, userId, deviceFingerprint);
  }

  /**
   * Decrypt chat message content
   */
  static async decryptMessage(
    encryptedData: EncryptedData,
    userId: string,
    deviceFingerprint: string
  ): Promise<any> {
    // Always use Hermes-compatible decryption
    const result = await HermesCompatibleEncryption.decryptMessage(encryptedData, userId, deviceFingerprint);
    return result.data;
  }

  /**
   * Encrypt attachment file data - simplified for Hermes compatibility
   */
  static async encryptAttachment(
    fileData: ArrayBuffer | Uint8Array,
    userId: string,
    deviceFingerprint: string
  ): Promise<EncryptedData> {
    // Convert file data to string and use message encryption
    const dataStr = typeof fileData === 'string'
      ? fileData
      : btoa(String.fromCharCode(...new Uint8Array(fileData)));

    return this.encryptMessage({ fileData: dataStr }, userId, deviceFingerprint);
  }

  /**
   * Decrypt attachment file data - simplified for Hermes compatibility
   */
  static async decryptAttachment(
    encryptedData: EncryptedData,
    userId: string,
    deviceFingerprint: string
  ): Promise<ArrayBuffer> {
    const result = await this.decryptMessage(encryptedData, userId, deviceFingerprint);

    // Convert back to ArrayBuffer
    const dataStr = result.fileData;
    const binaryString = atob(dataStr);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
  }
}
