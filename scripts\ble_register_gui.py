import asyncio
import threading
import tkinter as tk
from tkinter import messagebox, simpledialog, ttk
from bleak import BleakScanner, BleakClient
import requests
import json
import uuid

BACKEND_API_URL = "http://localhost:3000/api/admin/users/{user_id}/ble-devices"  # Updated for new BLE device system
USER_LOOKUP_API_URL = "http://localhost:3000/api/admin/users?search={username}"  # Update as needed
CUSTOM_SERVICE_UUID = "0000c0de-0000-1000-8000-00805f9b34fb"
CUSTOM_CHAR_UUID = "0000beef-0000-1000-8000-00805f9b34fb"

class BLERegisterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("BLE Device Registration")
        self.username = None
        self.user_id = None
        self.devices = []
        self.device_ads = {}  # Store advertisement data for devices
        self.selected_device = None
        self.setup_ui()

    def setup_ui(self):
        frame = ttk.Frame(self.root, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(frame, text="Username:").grid(row=0, column=0, sticky=tk.W)
        self.username_entry = ttk.Entry(frame, width=30)
        self.username_entry.grid(row=0, column=1, sticky=tk.W)

        self.scan_btn = ttk.Button(frame, text="Scan BLE Devices", command=self.scan_devices)
        self.scan_btn.grid(row=1, column=0, columnspan=2, pady=10)

        self.device_list = tk.Listbox(frame, width=50, height=8)
        self.device_list.grid(row=2, column=0, columnspan=2, pady=5)

        self.register_btn = ttk.Button(frame, text="Register Selected Device", command=self.register_device, state=tk.DISABLED)
        self.register_btn.grid(row=3, column=0, columnspan=2, pady=10)

        self.status_label = ttk.Label(frame, text="Status: Ready")
        self.status_label.grid(row=4, column=0, columnspan=2, sticky=tk.W)

        self.device_list.bind('<<ListboxSelect>>', self.on_device_select)

    def scan_devices(self):
        self.status_label.config(text="Status: Scanning for BLE devices...")
        self.device_list.delete(0, tk.END)
        self.devices = []
        self.device_ads = {}  # Clear advertisement data
        self.register_btn.config(state=tk.DISABLED)
        threading.Thread(target=self._scan_devices_thread, daemon=True).start()

    def _scan_devices_thread(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Store devices with their advertisement data
        self.devices = []
        self.device_ads = {}
        
        def detection_callback(device, advertisement_data):
            if device not in self.devices:
                self.devices.append(device)
                self.device_ads[device.address] = advertisement_data
        
        async def scan_with_callback():
            scanner = BleakScanner(detection_callback)
            await scanner.start()
            await asyncio.sleep(5.0)
            await scanner.stop()
        
        loop.run_until_complete(scan_with_callback())
        self.root.after(0, self._update_device_list)

    def _update_device_list(self):
        self.device_list.delete(0, tk.END)
        for idx, d in enumerate(self.devices):
            # Get RSSI from advertisement data (non-deprecated approach)
            rssi = 'N/A'
            if d.address in self.device_ads:
                rssi = self.device_ads[d.address].rssi or 'N/A'
            self.device_list.insert(tk.END, f"[{idx}] {d.name} ({d.address}) RSSI={rssi}")
        self.status_label.config(text=f"Status: Found {len(self.devices)} device(s)")

    def on_device_select(self, event):
        selection = self.device_list.curselection()
        if selection:
            self.selected_device = self.devices[selection[0]]
            self.register_btn.config(state=tk.NORMAL)
        else:
            self.selected_device = None
            self.register_btn.config(state=tk.DISABLED)

    def register_device(self):
        username = self.username_entry.get().strip()
        if not username:
            messagebox.showerror("Error", "Please enter a Username.")
            return
        if not self.selected_device:
            messagebox.showerror("Error", "Please select a BLE device.")
            return
        self.status_label.config(text="Status: Looking up user ID...")
        threading.Thread(target=self._lookup_and_register, args=(username,), daemon=True).start()

    def _lookup_and_register(self, username):
        try:
            # Lookup userId from backend
            url = USER_LOOKUP_API_URL.format(username=username)
            resp = requests.get(url)
            if resp.status_code != 200:
                self.root.after(0, lambda: messagebox.showerror("Error", f"User lookup failed: {resp.status_code} {resp.text}"))
                return
            data = resp.json()
            users = data.get('users', [])
            user = next((u for u in users if u['username'] == username), None)
            if not user:
                self.root.after(0, lambda: messagebox.showerror("Error", f"User '{username}' not found."))
                return
            user_id = user['_id'] if '_id' in user else user['id']
            self.user_id = user_id
            self.status_label.config(text=f"Status: Registering device for user {username} (ID: {user_id})...")
            # Proceed to BLE registration
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self._register_ble_device(user_id, self.selected_device))
            self.root.after(0, lambda: self.status_label.config(text="Status: " + result))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", str(e)))

    async def _register_ble_device(self, user_id, device):
        async with BleakClient(device.address) as client:
            ad_data = device.metadata
            services = await client.get_services()
            characteristics = {}
            for service in services:
                for char in service.characteristics:
                    try:
                        value = await client.read_gatt_char(char.uuid)
                        characteristics[char.uuid] = value.hex()
                    except Exception:
                        characteristics[char.uuid] = None
            signature = str(uuid.uuid4())
            try:
                await client.write_gatt_char(CUSTOM_CHAR_UUID, signature.encode('utf-8'), response=True)
            except Exception as e:
                return f"Failed to write signature: {e}"
            # Updated data format for new BLE device system
            ble_data = {
                "deviceId": device.address,
                "deviceName": device.name or f"BLE-{device.address[-8:]}",
                "deviceType": "earbud" if any(keyword in (device.name or "").lower()
                                            for keyword in ["airpods", "buds", "earbud", "headphone"]) else "generic",
                "adData": ad_data,
                "characteristics": characteristics,
                "signature": signature,
                "signatureCharUuid": CUSTOM_CHAR_UUID,
                "services": list(characteristics.keys()) if characteristics else [],
                "rssi": getattr(device, 'rssi', None),
                "registeredBy": "script",
                "scriptVersion": "2.0",
                "manufacturer": ad_data.get('manufacturer') if isinstance(ad_data, dict) else None,
                "notes": f"Registered via BLE GUI script on {str(device.details.get('timestamp', ''))}"
            }
            url = BACKEND_API_URL.format(user_id=user_id)
            resp = requests.post(url, json=ble_data)
            if resp.status_code == 200:
                return "Device registered successfully!"
            else:
                return f"Backend error: {resp.status_code} {resp.text}"

def main():
    root = tk.Tk()
    app = BLERegisterApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
