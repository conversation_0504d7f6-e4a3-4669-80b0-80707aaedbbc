/**
 * Voice Recordings Management Component
 * Provides admin interface for managing voice call recordings
 * Includes playback, download, and delete functionality
 */

import React, { useState, useEffect, useRef } from 'react';
import Icon from './Icon';
import tokenManager from '../../utils/tokenManager';

interface VoiceRecording {
  callId: string;
  startTime: string;
  endTime?: string;
  duration: number;
  fileSize: number;
  participants: {
    initiator: { id: string; name: string };
    recipient: { id: string; name: string };
  };
  voiceProfiles: {
    initiator: string;
    recipient: string;
  };
  status: string;
  worldVocoderEnabled: boolean;
  hasRecording: boolean;
}

interface RecordingStats {
  totalRecordings: number;
  totalDuration: number;
  totalSize: number;
  averageDuration: number;
  worldVocoderUsage: number;
  worldVocoderPercentage: number;
  timeRange: string;
}

const VoiceRecordingsManagement: React.FC = () => {
  const [recordings, setRecordings] = useState<VoiceRecording[]>([]);
  const [stats, setStats] = useState<RecordingStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    userId: '',
    startDate: '',
    endDate: '',
    timeRange: '30d'
  });
  
  // Audio playback state
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);
  const [playbackProgress, setPlaybackProgress] = useState(0);
  const [playbackDuration, setPlaybackDuration] = useState(0);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    fetchRecordings();
    fetchStats();
  }, [currentPage, filters]);

  const fetchRecordings = async () => {
    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      });

      const response = await fetch(`/api/voice/recordings?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${tokenManager.getToken()}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRecordings(data.recordings);
        setTotalPages(data.pagination.totalPages);
      }
    } catch (error) {
      console.error('Failed to fetch recordings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch(`/api/voice/recordings/stats/overview?timeRange=${filters.timeRange}`, {
        headers: {
          'Authorization': `Bearer ${tokenManager.getToken()}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const playRecording = async (callId: string) => {
    if (currentlyPlaying === callId) {
      // Stop current playback
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setCurrentlyPlaying(null);
      setPlaybackProgress(0);
      return;
    }

    try {
      if (audioRef.current) {
        audioRef.current.src = `/api/voice/recordings/${callId}/stream`;
        audioRef.current.load();
        
        audioRef.current.onloadedmetadata = () => {
          setPlaybackDuration(audioRef.current?.duration || 0);
        };
        
        audioRef.current.ontimeupdate = () => {
          if (audioRef.current) {
            setPlaybackProgress(audioRef.current.currentTime);
          }
        };
        
        audioRef.current.onended = () => {
          setCurrentlyPlaying(null);
          setPlaybackProgress(0);
        };
        
        await audioRef.current.play();
        setCurrentlyPlaying(callId);
      }
    } catch (error) {
      console.error('Failed to play recording:', error);
      alert('Failed to play recording');
    }
  };

  const downloadRecording = async (callId: string, participantNames: string) => {
    try {
      const response = await fetch(`/api/voice/recordings/${callId}/download`, {
        headers: {
          'Authorization': `Bearer ${tokenManager.getToken()}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `voice_call_${callId}_${participantNames.replace(/[^a-zA-Z0-9]/g, '_')}.wav`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to download recording:', error);
      alert('Failed to download recording');
    }
  };

  const deleteRecording = async (callId: string) => {
    if (!confirm('Are you sure you want to delete this recording? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/voice/recordings/${callId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${tokenManager.getToken()}`
        }
      });

      if (response.ok) {
        await fetchRecordings();
        await fetchStats();
        alert('Recording deleted successfully');
      }
    } catch (error) {
      console.error('Failed to delete recording:', error);
      alert('Failed to delete recording');
    }
  };

  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
    }
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.totalRecordings}</div>
            <div className="text-sm text-gray-600">Total Recordings</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="text-2xl font-bold text-green-600">{formatDuration(stats.totalDuration * 1000)}</div>
            <div className="text-sm text-gray-600">Total Duration</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="text-2xl font-bold text-purple-600">{formatFileSize(stats.totalSize)}</div>
            <div className="text-sm text-gray-600">Total Size</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="text-2xl font-bold text-orange-600">{stats.worldVocoderPercentage}%</div>
            <div className="text-sm text-gray-600">WORLD Vocoder Usage</div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Filters</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Time Range
            </label>
            <select
              value={filters.timeRange}
              onChange={(e) => setFilters(prev => ({ ...prev, timeRange: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              User ID
            </label>
            <input
              type="text"
              value={filters.userId}
              onChange={(e) => setFilters(prev => ({ ...prev, userId: e.target.value }))}
              placeholder="Filter by user ID"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Recordings Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Voice Call Recordings</h3>
        </div>
        
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading recordings...</p>
          </div>
        ) : recordings.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            No recordings found
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Call Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Participants
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Voice Profiles
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration / Size
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recordings.map((recording) => (
                  <tr key={recording.callId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {recording.callId}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(recording.startTime)}
                        </div>
                        <div className="flex items-center mt-1">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            recording.status === 'ended' 
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {recording.status}
                          </span>
                          {recording.worldVocoderEnabled && (
                            <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                              WORLD
                            </span>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div>📞 {recording.participants.initiator.name}</div>
                        <div>📱 {recording.participants.recipient.name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div>🎤 {recording.voiceProfiles.initiator.replace(/_/g, ' ')}</div>
                        <div>🎧 {recording.voiceProfiles.recipient.replace(/_/g, ' ')}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div>{formatDuration(recording.duration)}</div>
                        <div className="text-gray-500">{formatFileSize(recording.fileSize)}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => playRecording(recording.callId)}
                          className="text-blue-600 hover:text-blue-900"
                          title={currentlyPlaying === recording.callId ? 'Stop' : 'Play'}
                        >
                          <Icon 
                            name={currentlyPlaying === recording.callId ? 'stop' : 'play'} 
                            className="w-4 h-4" 
                          />
                        </button>
                        <button
                          onClick={() => downloadRecording(
                            recording.callId, 
                            `${recording.participants.initiator.name}_${recording.participants.recipient.name}`
                          )}
                          className="text-green-600 hover:text-green-900"
                          title="Download"
                        >
                          <Icon name="download" className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => deleteRecording(recording.callId)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete"
                        >
                          <Icon name="trash" className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-3 border-t border-gray-200 flex justify-between items-center">
            <div className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Audio Player */}
      <audio ref={audioRef} style={{ display: 'none' }} />
      
      {/* Playback Progress */}
      {currentlyPlaying && (
        <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 min-w-80">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-medium text-gray-900">
              Playing: {currentlyPlaying}
            </div>
            <button
              onClick={() => playRecording(currentlyPlaying)}
              className="text-gray-500 hover:text-gray-700"
            >
              <Icon name="x" className="w-4 h-4" />
            </button>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-100"
              style={{ width: `${(playbackProgress / playbackDuration) * 100}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>{formatDuration(playbackProgress * 1000)}</span>
            <span>{formatDuration(playbackDuration * 1000)}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default VoiceRecordingsManagement;
