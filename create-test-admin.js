/**
 * Create a simple admin user for testing
 */

require('ts-node/register');

async function createTestAdmin() {
  const bcrypt = require('bcryptjs');
  const mongoose = require('mongoose');
  
  // Connect to MongoDB
  await mongoose.connect('mongodb://localhost:27017/ccalc', {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });
  
  console.log('Connected to MongoDB');
  
  // Import the Admin model
  const Admin = require('./backend/models/Admin').default;
  
  // Check if testadmin already exists
  const existingAdmin = await Admin.findOne({ username: 'testadmin' });
  if (existingAdmin) {
    console.log('✅ Test admin already exists');
    await mongoose.connection.close();
    return;
  }
  
  // Create test admin
  const hashedPassword = await bcrypt.hash('testpassword123', 10);
  
  const testAdmin = new Admin({
    username: 'testadmin',
    email: '<EMAIL>',
    password: hashedPassword,
    role: 'admin',
    isActive: true
  });
  
  await testAdmin.save();
  console.log('✅ Test admin created successfully');
  
  await mongoose.connection.close();
}

createTestAdmin().catch(console.error);
