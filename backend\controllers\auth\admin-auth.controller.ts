import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import AdminModel from '../../models/Admin';
import { generateChallenge, verifySignature } from '../../utils/ppk';
import crypto from 'crypto';
import securityLogger from '../../utils/security-logger';
import { generateCsrfToken } from '../../utils/csrf-protection';
import { generateAuthChallenge, validateAuthChallenge } from '../../utils/challenge-session';

/**
 * Step 1: Initial admin login request to get PPK challenge if required
 * POST /api/auth/admin/login/init
 */
export async function adminLoginInitiate(req: Request, res: Response): Promise<void> {
  try {
    const { username } = req.body as { username: string };

    if (!username) {
      res.status(400).json({ error: 'Username is required' });
      return;
    }

    // Rate limiting check could be added here
    const ipAddress = req.ip || req.socket.remoteAddress;

    const admin = await AdminModel.findOne({ username, isActive: true });

    if (!admin) {
      // Use consistent timing to prevent username enumeration attacks
      await bcrypt.compare('dummy', '$2a$10$invalidhashfordummycomparison');
      res.status(401).json({ error: 'Authentication failed' });
      return;
    }

    // Generate challenge for PPK authentication if required
    const requiresPPK = admin.authMethod === 'ppk' || admin.authMethod === 'both';
    const challenge = requiresPPK ? generateChallenge() : undefined;

    console.log('DEBUG - adminLoginInitiate detailed:');
    console.log('  username:', username);
    console.log('  admin found:', !!admin);
    console.log('  admin.authMethod:', admin.authMethod);
    console.log('  admin.ppkEnabled:', admin.ppkEnabled);
    console.log('  requiresPPK calculated:', requiresPPK);
    console.log('  challenge generated:', challenge);
    console.log('  challenge type:', typeof challenge);
    console.log('  challenge length:', challenge ? challenge.length : 'N/A');

    // Include CSRF token if needed for higher security
    const csrfToken = crypto.randomBytes(32).toString('hex');

    // Store the challenge and CSRF token in session or cache with short TTL
    // This is implementation-dependent, but necessary for security

    const responseData = {
      requiresPPK,
      challenge,
      authMethod: admin.authMethod,
      csrfToken: csrfToken,
      message: requiresPPK ? 'PPK signature required' : 'Proceed with password'
    }; console.log('DEBUG: Full response data being sent:', JSON.stringify(responseData, null, 2));

    // Send response - wrap in try-catch to catch any middleware AuditLog errors
    try {
      res.status(200).json(responseData);
    } catch (responseError) {
      console.error('Response sending error:', responseError);
      res.status(500).json({ error: 'Failed to send response' });
    }
  } catch (error) {
    console.error('Admin login init error:', error);
    if (error instanceof Error) {
      console.error('Error stack:', error.stack);
    }
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Step 2: Complete admin login with password and/or PPK signature
 * POST /api/auth/admin/login/complete
 */
export async function adminLoginComplete(req: Request, res: Response): Promise<void> {
  try {
    const { username, password, ppkSignature, challenge, deviceFingerprint = 'web' } = req.body;

    if (!username || !password) {
      res.status(400).json({ error: 'Username and password are required' });
      return;
    }

    const admin = await AdminModel.findOne({ username, isActive: true }); if (!admin) {
      // Use consistent timing to prevent username enumeration
      await bcrypt.compare('dummy', '$2a$10$invalidhashfordummycomparison');
      res.status(401).json({ error: 'Authentication failed' });
      return;
    }    // Verify this is actually an admin account
    if (admin.role !== 'superadmin') {
      securityLogger.logSecurityEvent({
        eventType: 'failed_login',
        userType: 'admin',
        username: admin.username,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        success: false,
        reason: 'Non-admin role attempted admin login',
        details: { role: admin.role }
      });

      res.status(403).json({
        error: 'Admin access required - Please use the mobile app for user accounts',
        code: 'ADMIN_ROLE_REQUIRED'
      });
      return;
    }

    // Validate password
    const passwordValid = await bcrypt.compare(password, admin.password);
    if (!passwordValid) {
      securityLogger.logLoginAttempt(username, 'admin', false, req, undefined, 'Invalid password');
      res.status(401).json({ error: 'Authentication failed' });
      return;
    }

    // If PPK is enabled, verify signature
    if (admin.ppkEnabled) {
      if (!challenge || !ppkSignature) {
        res.status(401).json({ error: 'PPK authentication required' });
        return;
      } try {
        if (!admin.ppkPublicKey) {
          throw new Error('PPK public key not configured');
        }
        const signatureValid = verifySignature(challenge, ppkSignature, admin.ppkPublicKey);
        if (!signatureValid) {
          throw new Error('PPK signature verification failed');
        }
      } catch (error) {
        securityLogger.logLoginAttempt(
          username,
          'admin',
          false,
          req,
          undefined,
          'PPK verification failed',
          { error: (error as Error).message, challenge, ppkSignature, publicKey: admin.ppkPublicKey }
        );
        res.status(401).json({ error: 'PPK verification failed' });
        return;
      }
    }

    // Update admin data
    admin.lastLogin = new Date();

    // Store device info for audit trail
    const deviceInfo = {
      fingerprint: deviceFingerprint,
      ipAddress: req.ip || req.socket.remoteAddress,
      userAgent: req.headers['user-agent'] || 'unknown',
      loginTime: new Date()
    };

    await admin.save();    // Generate JWT token with appropriate claims
    const tokenPayload = {
      adminId: (admin as any)._id,
      username: admin.username,
      role: admin.role,
      type: 'admin',
      isSuperuser: true, // Mark admin as superuser for WebSocket broadcasting
      fingerprint: crypto.createHash('sha256').update(deviceFingerprint).digest('hex').slice(0, 16)
    }; const expiryValue = process.env.ADMIN_TOKEN_EXPIRY || '8h';
    console.log('DEBUG: ADMIN_TOKEN_EXPIRY value:', expiryValue);

    const tokenOptions = {
      expiresIn: expiryValue
    };
    const token = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET || 'fallback-secret',
      tokenOptions as jwt.SignOptions
    );

    // Generate CSRF token
    const adminCsrfToken = generateCsrfToken((admin as any)._id.toString());

    // Set cookies with appropriate security settings
    res.cookie('admin-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax', // Allow cross-site requests
      path: '/',
      maxAge: 8 * 60 * 60 * 1000, // 8 hours
      domain: process.env.COOKIE_DOMAIN || undefined,
    });

    // Set non-HttpOnly cookie for client-side access if needed
    res.cookie('admin-token-access', token, {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 8 * 60 * 60 * 1000, // 8 hours
      domain: process.env.COOKIE_DOMAIN || undefined,
    });

    console.log('Cookies set successfully:', {
      'admin-token': 'httpOnly=true',
      'admin-token-access': 'httpOnly=false',
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      domain: process.env.COOKIE_DOMAIN || 'default'
    });

    // Also set token in header for debugging and alternative access
    res.setHeader('X-Admin-Token', token);    // Log successful login
    securityLogger.logLoginAttempt(
      username,
      'admin',
      true,
      req,
      (admin as any)._id.toString(),
      undefined,
      { role: admin.role }
    );

    res.status(200).json({
      token,
      csrfToken: adminCsrfToken, admin: {
        id: (admin as any)._id,
        username: admin.username,
        email: admin.email,
        role: admin.role,
        ppkEnabled: admin.ppkEnabled,
        authMethod: admin.authMethod
      }
    });
  } catch (error) {
    console.error('Admin login complete error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Legacy admin login endpoint (simplified for password-only auth)
 * POST /api/auth/admin/login
 */
export async function adminLogin(req: Request, res: Response): Promise<void> {
  try {
    const { username, password, deviceFingerprint, challenge, ppkSignature } = req.body as {
      username: string;
      password: string;
      deviceFingerprint?: string;
      challenge?: string;
      ppkSignature?: string;
    };

    if (!username || !password) {
      res.status(400).json({ error: 'Missing required fields' });
      return;
    }

    const admin = await AdminModel.findOne({ username, isActive: true });

    if (!admin) {
      // Use consistent timing to prevent username enumeration attacks
      await bcrypt.compare('dummy', '$2a$10$invalidhashfordummycomparison');
      res.status(401).json({ error: 'Authentication failed' });
      return;
    }    // Verify this is actually an admin account
    if (admin.role !== 'superadmin') {
      securityLogger.logSecurityEvent({
        eventType: 'failed_login',
        userType: 'admin',
        username: admin.username,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        success: false,
        reason: 'Non-admin role attempted admin login',
        details: { role: admin.role }
      });

      res.status(403).json({
        error: 'Admin access required - Please use the mobile app for user accounts',
        code: 'ADMIN_ROLE_REQUIRED'
      });
      return;
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, admin.password);
    if (!isPasswordValid) {
      res.status(401).json({ error: 'Authentication failed' });
      return;
    }    // If PPK signature is provided, verify it
    if (challenge && ppkSignature && admin.ppkEnabled && admin.ppkPublicKey) {
      try {
        if (!admin.ppkPublicKey) {
          throw new Error('PPK public key not configured');
        }
        const isSignatureValid = verifySignature(challenge, ppkSignature, admin.ppkPublicKey);
        if (!isSignatureValid) {
          res.status(401).json({ error: 'Invalid PPK signature' });
          return;
        }
      } catch (error) {
        console.error('PPK verification error:', error);
        res.status(401).json({ error: 'PPK verification failed' });
        return;
      }
    }

    // Update last login timestamp
    admin.lastLogin = new Date();
    await admin.save();    // Generate JWT token
    const expiryValue = process.env.ADMIN_TOKEN_EXPIRY || '8h';
    console.log('DEBUG: Legacy login ADMIN_TOKEN_EXPIRY value:', expiryValue);

    const token = jwt.sign(
      {
        adminId: (admin as any)._id,
        username: admin.username,
        role: admin.role,
        type: 'admin',
        fingerprint: deviceFingerprint ?
          crypto.createHash('sha256').update(deviceFingerprint).digest('hex').slice(0, 16) :
          undefined
      },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: expiryValue } as any
    );// Generate CSRF token for authenticated requests
    const legacyCsrfToken = generateCsrfToken((admin as any)._id.toString());

    // Set secure HTTP-only cookie with token
    res.cookie('admin-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax', // Changed from 'strict' to 'lax' for cross-domain requests
      path: '/',
      maxAge: 8 * 60 * 60 * 1000, // 8 hours
      domain: process.env.COOKIE_DOMAIN || undefined // Allow specific domain setting
    });

    // Also add token to response headers for debugging and alternative access
    res.setHeader('X-Admin-Token', token);

    res.status(200).json({
      token,
      csrfToken: legacyCsrfToken,
      admin: {
        id: (admin as any)._id,
        username: admin.username,
        email: admin.email,
        role: admin.role,
        ppkEnabled: admin.ppkEnabled,
        authMethod: admin.authMethod
      }
    });

  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
