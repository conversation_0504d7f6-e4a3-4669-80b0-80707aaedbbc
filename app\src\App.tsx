/**
 * Main App Component
 * Handles navigation between Calculator and Chat screens based on authentication
 */

import React, { useState, useEffect } from 'react';
import { SafeAreaView, View, Text, StyleSheet } from 'react-native';

import { CalculatorScreen } from './screens/CalculatorScreen';
import { ChatListScreen, ChatListItem } from './screens/ChatListScreen';
import { ChatScreen } from './screens/ChatScreen';

type AppScreen = 'calculator' | 'chatList' | 'chat';

export default function App() {
  const [currentScreen, setCurrentScreen] = useState<AppScreen>('calculator');
  const [selectedChat, setSelectedChat] = useState<ChatListItem | undefined>(undefined);
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  // Initialize app services safely
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('🚀 Initializing CCALC app...');

        // Add any necessary initialization here
        // For now, just mark as initialized
        setIsInitialized(true);
        console.log('✅ App initialization complete');
      } catch (error) {
        console.error('❌ App initialization failed:', error);
        setInitError(error instanceof Error ? error.message : 'Initialization failed');
        // Still mark as initialized to show calculator
        setIsInitialized(true);
      }
    };

    initializeApp();
  }, []);

  const handleAuthenticationSuccess = () => {
    console.log('🔄 App: handleAuthenticationSuccess called');
    setCurrentScreen('chatList');
    console.log('✅ App: Navigated to chat list');
  };

  const handleSelectChat = (chatUser: ChatListItem) => {
    setSelectedChat(chatUser);
    setCurrentScreen('chat');
  };

  const handleBackToList = () => {
    setSelectedChat(undefined);
    setCurrentScreen('chatList');
  };

  const handleLogout = () => {
    setCurrentScreen('calculator');
    setSelectedChat(undefined);
  };

  const renderScreen = () => {
    switch (currentScreen) {
      case 'calculator':
        return <CalculatorScreen onAuthenticationSuccess={handleAuthenticationSuccess} />;

      case 'chatList':
        return (
          <ChatListScreen
            onSelectChat={handleSelectChat}
            onLogout={handleLogout}
          />
        );

      case 'chat':
        return (
          <ChatScreen
            chatUser={selectedChat}
            onBack={handleBackToList}
            onLogout={handleLogout}
          />
        );

      default:
        return <CalculatorScreen onAuthenticationSuccess={handleAuthenticationSuccess} />;
    }
  };

  // Show loading screen during initialization
  if (!isInitialized) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Calculator</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error screen if initialization failed
  if (initError) {
    console.warn('⚠️ App running with initialization error:', initError);
    // Still show calculator - don't block the user
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderScreen()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
});


