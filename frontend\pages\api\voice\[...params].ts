// Next.js API route to proxy voice API requests to backend
import type { NextApiRequest, NextApiResponse } from 'next';
import FormData from 'form-data';
import axios from 'axios';
import multer from 'multer';
import { promisify } from 'util';

const upload = multer();
const uploadMiddleware = promisify(upload.single('audio'));

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';

  // Reconstruct the full path from params
  const { params } = req.query;
  const pathSegments = Array.isArray(params) ? params : [params];
  const fullPath = pathSegments.join('/');

  const url = `${backendUrl}/api/voice/${fullPath}`;

  try {
    if (req.method === 'POST' && fullPath.includes('test-modulation')) {
      // Handle multipart form data for audio upload
      await uploadMiddleware(req as any, res as any);

      const form = new FormData();
      const file = (req as any).file;
      const { profileName } = (req as any).body;

      if (file) {
        form.append('audio', file.buffer, {
          filename: file.originalname,
          contentType: file.mimetype,
        });
      }

      if (profileName) {
        form.append('profileName', profileName);
      }

      const response = await axios({
        method: 'POST',
        url,
        headers: {
          ...form.getHeaders(),
          'Authorization': req.headers.authorization,
        },
        data: form,
        responseType: 'stream',
        validateStatus: () => true,
      });

      res.setHeader('Content-Type', response.headers['content-type'] || 'audio/wav');
      response.data.pipe(res);
    } else {
      // Handle regular JSON requests
      const response = await axios({
        method: req.method,
        url,
        headers: {
          ...req.headers,
          host: undefined,
        },
        data: req.body,
        params: req.query.params ? undefined : req.query,
        validateStatus: () => true,
      });

      res.status(response.status).json(response.data);
    }
  } catch (error: any) {
    console.error('Voice API proxy error:', error.message);
    res.status(500).json({
      error: 'Proxy error',
      details: error.message,
      url: url.replace(process.env.NEXT_PUBLIC_BACKEND_URL || '', '[BACKEND_URL]')
    });
  }
}
