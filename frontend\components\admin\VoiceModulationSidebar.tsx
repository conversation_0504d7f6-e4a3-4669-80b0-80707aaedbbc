import React, { useState, useEffect, useRef } from 'react';
import Icon from './Icon';
import tokenManager from '../../utils/tokenManager';

interface VoiceProfile {
  name: string;
  pitch: number;
  tempo: number;
  reverb: number;
  distortion: number;
  formant: number;
  chorus: boolean;
  normalize: boolean;
  description?: string;
  type?: 'standard' | 'custom';
  isEditable?: boolean;
}

interface VoiceModulationSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const VoiceModulationSidebar: React.FC<VoiceModulationSidebarProps> = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState<'profiles' | 'create' | 'test'>('profiles');
  const [profiles, setProfiles] = useState<VoiceProfile[]>([]);
  const [selectedProfile, setSelectedProfile] = useState<VoiceProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [testingProfile, setTestingProfile] = useState<boolean>(false);
  
  // Custom profile creation state
  const [customProfile, setCustomProfile] = useState<VoiceProfile>({
    name: '',
    pitch: 0,
    tempo: 1.0,
    reverb: 0,
    distortion: 0,
    formant: 0,
    chorus: false,
    normalize: true,
    description: ''
  });

  const audioRef = useRef<HTMLAudioElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  // Fetch available profiles
  useEffect(() => {
    if (isOpen) {
      fetchProfiles();
    }
  }, [isOpen]);

  const fetchProfiles = async () => {
    try {
      setIsLoading(true);
      const token = tokenManager.getToken('admin');
      console.log('VoiceModulationSidebar.fetchProfiles - token:', token ? 'present' : 'null');
      const response = await fetch('/api/voice/profiles/all', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setProfiles(data.data.profiles || []);
      }
    } catch (error) {
      console.error('Failed to fetch profiles:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      chunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/wav' });
        const file = new File([audioBlob], 'recording.wav', { type: 'audio/wav' });
        setAudioFile(file);
        
        // Clean up previous audio URL
        if (audioUrl) {
          URL.revokeObjectURL(audioUrl);
        }
        
        const newAudioUrl = URL.createObjectURL(audioBlob);
        setAudioUrl(newAudioUrl);
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Failed to start recording:', error);
      alert('Failed to access microphone');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const testProfile = async (profile: VoiceProfile) => {
    if (!audioFile) {
      alert('Please record audio first');
      return;
    }

    try {
      setTestingProfile(true);
      const token = tokenManager.getToken('admin');
      console.log('VoiceModulationSidebar.testProfile - token:', token ? 'present' : 'null');
      const formData = new FormData();
      formData.append('audio', audioFile);
      formData.append('pitch', profile.pitch.toString());
      formData.append('tempo', profile.tempo.toString());
      formData.append('reverb', profile.reverb.toString());
      formData.append('distortion', profile.distortion.toString());
      formData.append('formant', profile.formant.toString());
      formData.append('chorus', profile.chorus.toString());
      formData.append('normalize', profile.normalize.toString());

      const response = await fetch('/api/voice/profiles/test-custom', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        
        // Clean up previous modulated audio URL
        if (audioUrl && audioUrl.includes('blob:')) {
          URL.revokeObjectURL(audioUrl);
        }
        
        const modulatedAudioUrl = URL.createObjectURL(audioBlob);
        setAudioUrl(modulatedAudioUrl);
        
        // Auto-play the result
        if (audioRef.current) {
          audioRef.current.src = modulatedAudioUrl;
          audioRef.current.play();
          setIsPlaying(true);
        }
      } else {
        throw new Error('Profile test failed');
      }
    } catch (error) {
      console.error('Failed to test profile:', error);
      alert('Failed to test profile');
    } finally {
      setTestingProfile(false);
    }
  };

  const createCustomProfile = async () => {
    if (!customProfile.name.trim()) {
      alert('Please enter a profile name');
      return;
    }

    try {
      setIsLoading(true);
      const token = tokenManager.getToken('admin');
      console.log('VoiceModulationSidebar.createCustomProfile - token:', token ? 'present' : 'null');
      const response = await fetch('/api/voice/profiles/custom', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(customProfile)
      });

      if (response.ok) {
        const data = await response.json();
        alert('Custom profile created successfully!');
        
        // Reset form
        setCustomProfile({
          name: '',
          pitch: 0,
          tempo: 1.0,
          reverb: 0,
          distortion: 0,
          formant: 0,
          chorus: false,
          normalize: true,
          description: ''
        });
        
        // Refresh profiles list
        await fetchProfiles();
        setActiveTab('profiles');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create profile');
      }
    } catch (error) {
      console.error('Failed to create profile:', error);
      alert(`Failed to create profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const playAudio = () => {
    if (audioRef.current && audioUrl) {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        audioRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex">
      {/* Overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      {/* Sidebar */}
      <div className="relative bg-white w-96 h-full shadow-2xl overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b bg-gray-50">
          <h2 className="text-xl font-bold text-gray-900">Voice Modulation</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-200 rounded-lg transition-colors"
          >
            <Icon name="close" size={20} />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b bg-gray-50">
          {[
            { key: 'profiles', label: 'Profiles', icon: 'view' },
            { key: 'create', label: 'Create', icon: 'add' },
            { key: 'test', label: 'Test', icon: 'calls' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ${
                activeTab === tab.key
                  ? 'text-indigo-600 border-b-2 border-indigo-600 bg-white'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
              }`}
            >
              <Icon name={tab.icon as any} size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Profiles Tab */}
          {activeTab === 'profiles' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Available Profiles</h3>
              
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                </div>
              ) : (
                <div className="space-y-3">
                  {profiles.map((profile, index) => (
                    <div
                      key={`${profile.name}-${index}`}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        selectedProfile?.name === profile.name
                          ? 'border-indigo-600 bg-indigo-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                      onClick={() => setSelectedProfile(profile)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900">{profile.name}</h4>
                          <p className="text-sm text-gray-500">{profile.description}</p>
                          <div className="mt-2 text-xs text-gray-400">
                            Pitch: {profile.pitch} | Tempo: {profile.tempo} | 
                            Reverb: {profile.reverb}% | Distortion: {profile.distortion}%
                          </div>
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            profile.type === 'custom' 
                              ? 'bg-green-100 text-green-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {profile.type === 'custom' ? 'Custom' : 'Standard'}
                          </span>
                          {audioFile && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                testProfile(profile);
                              }}
                              disabled={testingProfile}
                              className="px-3 py-1 text-xs bg-indigo-600 text-white rounded hover:bg-indigo-700 disabled:opacity-50"
                            >
                              {testingProfile ? 'Testing...' : 'Test'}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Create Tab */}
          {activeTab === 'create' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Create Custom Profile</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Profile Name
                  </label>
                  <input
                    type="text"
                    value={customProfile.name}
                    onChange={(e) => setCustomProfile({ ...customProfile, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-600 focus:border-transparent"
                    placeholder="Enter profile name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={customProfile.description}
                    onChange={(e) => setCustomProfile({ ...customProfile, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-600 focus:border-transparent"
                    rows={2}
                    placeholder="Optional description"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Pitch ({customProfile.pitch})
                    </label>
                    <input
                      type="range"
                      min="-12"
                      max="12"
                      step="0.1"
                      value={customProfile.pitch}
                      onChange={(e) => setCustomProfile({ ...customProfile, pitch: parseFloat(e.target.value) })}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>-12</span>
                      <span>12</span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tempo ({customProfile.tempo})
                    </label>
                    <input
                      type="range"
                      min="0.5"
                      max="2.0"
                      step="0.05"
                      value={customProfile.tempo}
                      onChange={(e) => setCustomProfile({ ...customProfile, tempo: parseFloat(e.target.value) })}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>0.5x</span>
                      <span>2.0x</span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Reverb ({customProfile.reverb}%)
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      step="1"
                      value={customProfile.reverb}
                      onChange={(e) => setCustomProfile({ ...customProfile, reverb: parseInt(e.target.value) })}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>0%</span>
                      <span>100%</span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Distortion ({customProfile.distortion}%)
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      step="1"
                      value={customProfile.distortion}
                      onChange={(e) => setCustomProfile({ ...customProfile, distortion: parseInt(e.target.value) })}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>0%</span>
                      <span>100%</span>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Formant Shift ({customProfile.formant} Hz)
                  </label>
                  <input
                    type="range"
                    min="-1000"
                    max="1000"
                    step="10"
                    value={customProfile.formant}
                    onChange={(e) => setCustomProfile({ ...customProfile, formant: parseInt(e.target.value) })}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>-1000 Hz</span>
                    <span>1000 Hz</span>
                  </div>
                </div>

                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={customProfile.chorus}
                      onChange={(e) => setCustomProfile({ ...customProfile, chorus: e.target.checked })}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Add Chorus Effect</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={customProfile.normalize}
                      onChange={(e) => setCustomProfile({ ...customProfile, normalize: e.target.checked })}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Normalize Audio</span>
                  </label>
                </div>

                <button
                  onClick={createCustomProfile}
                  disabled={isLoading || !customProfile.name.trim()}
                  className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoading ? 'Creating...' : 'Create Profile'}
                </button>
              </div>
            </div>
          )}

          {/* Test Tab */}
          {activeTab === 'test' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Test Voice Modulation</h3>
              
              <div className="space-y-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-3">Record Audio</h4>
                  
                  <div className="flex items-center space-x-3">
                    {!isRecording ? (
                      <button
                        onClick={startRecording}
                        className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                      >
                        <Icon name="calls" size={16} />
                        <span>Start Recording</span>
                      </button>
                    ) : (
                      <button
                        onClick={stopRecording}
                        className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <Icon name="close" size={16} />
                        <span>Stop Recording</span>
                      </button>
                    )}
                    
                    {audioUrl && (
                      <button
                        onClick={playAudio}
                        className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <Icon name={isPlaying ? 'close' : 'arrow-right'} size={16} />
                        <span>{isPlaying ? 'Pause' : 'Play'}</span>
                      </button>
                    )}
                  </div>
                  
                  {isRecording && (
                    <div className="mt-3 flex items-center space-x-2 text-red-600">
                      <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
                      <span className="text-sm">Recording...</span>
                    </div>
                  )}
                </div>

                {audioFile && (
                  <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center space-x-2 text-green-800">
                      <Icon name="check" size={16} />
                      <span className="text-sm font-medium">Audio recorded successfully!</span>
                    </div>
                    <p className="text-sm text-green-600 mt-1">
                      You can now test this audio with any profile in the Profiles tab.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Hidden audio element for playback */}
        <audio
          ref={audioRef}
          onEnded={() => setIsPlaying(false)}
          onError={() => setIsPlaying(false)}
          style={{ display: 'none' }}
        />
      </div>
    </div>
  );
};

export default VoiceModulationSidebar;
