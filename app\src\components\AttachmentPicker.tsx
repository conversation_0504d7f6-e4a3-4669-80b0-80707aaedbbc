/**
 * Clean & Minimal Attachment Picker
 * Gallery, Documents, Audio Files only (NO voice notes)
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as Haptics from 'expo-haptics';
import { theme } from '../utils/theme';
import IsolatedMediaService from '../services/IsolatedMediaService';
import { MediaAttachment } from '../services/MediaService';

export interface AttachmentOption {
  id: string;
  title: string;
  color: string;
}

export interface AttachmentPickerProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (attachment: MediaAttachment | MediaAttachment[]) => void;
}

const { width } = Dimensions.get('window');

export const AttachmentPicker: React.FC<AttachmentPickerProps> = ({
  visible,
  onClose,
  onSelect,
}) => {
  const slideAnim = React.useRef(new Animated.Value(300)).current;
  const isolatedMediaService = React.useRef(new IsolatedMediaService()).current;

  React.useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 65,
        friction: 8,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // Modern, polished options with isolated media - NO camera or voice notes
  const attachmentOptions: AttachmentOption[] = [
    {
      id: 'gallery',
      title: 'Photos & Videos',
      color: '#007AFF',
    },
    {
      id: 'document',
      title: 'Documents',
      color: '#34C759',
    },
  ];

  const handleOptionPress = async (option: AttachmentOption) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    try {
      switch (option.id) {
        case 'gallery':
          // Use isolated media service for limited gallery access (Instagram-like)
          const galleryAttachment = await isolatedMediaService.selectFromLibrary({
            allowsEditing: false,
            quality: 0.8,
            mediaTypes: 'all',
            allowsMultipleSelection: true, // Enable multiple selection
          });

          if (galleryAttachment) {
            onSelect(galleryAttachment);
            onClose();
          }
          return;

        // Camera option removed as per requirements

        case 'document':
          // Use isolated media service for documents
          const documentAttachment = await isolatedMediaService.selectDocument();

          if (documentAttachment) {
            onSelect(documentAttachment);
            onClose();
          }
          return;

        // Audio option removed - audio files can be selected via Documents

        default:
          return;
      }
    } catch (error) {
      console.error('Attachment picker error:', error);
      alert('Failed to select attachment');
    }
  };

  const getFileCategory = (mimeType: string): string => {
    if (mimeType.startsWith('application/pdf')) return 'PDF';
    if (mimeType.startsWith('application/msword') || 
        mimeType.includes('wordprocessingml')) return 'Document';
    if (mimeType.startsWith('application/vnd.ms-excel') || 
        mimeType.includes('spreadsheetml')) return 'Spreadsheet';
    if (mimeType.startsWith('text/')) return 'Text File';
    if (mimeType.startsWith('audio/')) return 'Audio';
    if (mimeType.startsWith('video/')) return 'Video';
    if (mimeType.startsWith('image/')) return 'Image';
    return 'File';
  };

  return (
    <Modal
      visible={visible}
      animationType="none"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity 
          style={styles.backdrop} 
          activeOpacity={1} 
          onPress={onClose}
        />
        
        <Animated.View
          style={[
            styles.container,
            { transform: [{ translateY: slideAnim }] }
          ]}
        >
          <View style={styles.handle} />
          
          <View style={styles.modernHeader}>
            <Text style={styles.modernHeaderTitle}>Share</Text>
            <Text style={styles.modernHeaderSubtitle}>Choose what to share</Text>
          </View>

          <View style={styles.modernContent}>
            {attachmentOptions.map((option, index) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.modernOption,
                  index === attachmentOptions.length - 1 && styles.modernOptionLast
                ]}
                onPress={() => handleOptionPress(option)}
                activeOpacity={0.6}
              >
                <View style={[styles.modernOptionIcon, { backgroundColor: option.color }]}>
                  <Text style={styles.modernOptionIconText}>
                    {option.id === 'gallery' ? '📷' : '📄'}
                  </Text>
                </View>
                <View style={styles.modernOptionContent}>
                  <Text style={styles.modernOptionTitle}>{option.title}</Text>
                  <Text style={styles.modernOptionDescription}>
                    {option.id === 'gallery'
                      ? 'Select photos and videos from your library'
                      : 'Choose documents, audio files, and other files'
                    }
                  </Text>
                </View>
                <Text style={styles.modernOptionArrow}>›</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  container: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    paddingBottom: 34,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 16,
  },
  handle: {
    width: 36,
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 8,
    opacity: 0.6,
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.label,
    textAlign: 'center',
    letterSpacing: -0.4,
  },
  content: {
    paddingHorizontal: 24,
    gap: 12,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 18,
    paddingHorizontal: 20,
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  optionIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 16,
  },
  optionTitle: {
    fontSize: 17,
    fontWeight: '500',
    color: theme.colors.label,
    letterSpacing: -0.4,
  },
  // Modern styles
  modernHeader: {
    paddingHorizontal: 28,
    paddingVertical: 24,
    alignItems: 'center',
  },
  modernHeaderTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
    letterSpacing: -0.6,
  },
  modernHeaderSubtitle: {
    fontSize: 15,
    fontWeight: '400',
    color: theme.colors.textSecondary,
    opacity: 0.8,
  },
  modernContent: {
    paddingHorizontal: 20,
    paddingBottom: 8,
  },
  modernOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.06)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  modernOptionLast: {
    marginBottom: 4,
  },
  modernOptionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  modernOptionIconText: {
    fontSize: 20,
  },
  modernOptionContent: {
    flex: 1,
    marginRight: 12,
  },
  modernOptionTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
    letterSpacing: -0.4,
  },
  modernOptionDescription: {
    fontSize: 14,
    fontWeight: '400',
    color: theme.colors.textSecondary,
    lineHeight: 18,
    opacity: 0.8,
  },
  modernOptionArrow: {
    fontSize: 20,
    fontWeight: '300',
    color: theme.colors.textSecondary,
    opacity: 0.6,
  },
});
