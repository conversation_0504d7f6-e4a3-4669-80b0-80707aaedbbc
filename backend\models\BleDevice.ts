import mongoose, { Document, Schema } from 'mongoose';

/**
 * Dedicated BLE Device Model for comprehensive device management
 * This model provides detailed tracking and management of BLE devices
 * separate from the User model for better organization and querying
 */
export interface IBleDevice extends Document {
  userId: mongoose.Types.ObjectId;
  deviceId: string; // BLE device MAC address or unique identifier
  deviceName: string;
  deviceType: 'earbud' | 'headphone' | 'speaker' | 'generic';
  
  // Registration data from Python scripts
  registrationData: {
    adData?: any; // Advertisement data captured during registration
    characteristics: Record<string, string | null>; // GATT characteristics
    signature?: string; // Unique signature written to device
    signatureCharUuid?: string; // Characteristic UUID where signature was written
    services?: string[]; // Available GATT services
    rssi?: number; // Signal strength at registration
    registeredAt: Date;
    registeredBy: 'script' | 'admin' | 'user'; // How device was registered
    scriptVersion?: string; // Version of registration script used
    registrationIP?: string; // IP address where registration occurred
  };
  
  // Connection and verification status
  status: {
    isVerified: boolean;
    isActive: boolean;
    lastSeen: Date;
    lastConnected?: Date;
    connectionCount: number;
    lastRssi?: number;
    connectionHistory: Array<{
      timestamp: Date;
      rssi?: number;
      duration?: number; // Connection duration in seconds
      disconnectReason?: string;
    }>;
  };
  
  // Voice call authorization
  voiceCallAuth: {
    enabled: boolean; // Whether this device can authorize voice calls
    lastUsedForCall?: Date;
    callCount: number;
    authFailures: number;
    lastAuthFailure?: Date;
    authSuccessRate: number; // Calculated success rate
  };
  
  // Security and audit
  security: {
    authChallenge?: string; // Current auth challenge for verification
    challengeExpiry?: Date;
    encryptedAuthData?: string; // Encrypted authentication data
    riskScore: number; // 0-100 risk assessment
    violations: Array<{
      type: 'auth_failure' | 'suspicious_activity' | 'connection_anomaly' | 'security_breach';
      timestamp: Date;
      details: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      resolved: boolean;
    }>;
    lastSecurityScan?: Date;
    securityFlags: string[]; // Array of security flags/warnings
  };
  
  // Metadata and device information
  metadata: {
    manufacturer?: string;
    model?: string;
    firmwareVersion?: string;
    batteryLevel?: number;
    capabilities?: string[];
    notes?: string; // Admin notes
    tags?: string[]; // Admin tags for organization
    
    // Technical specifications
    bluetoothVersion?: string;
    supportedCodecs?: string[];
    maxRange?: number; // in meters
    batteryCapacity?: number; // in mAh
    
    // Admin management
    addedBy?: mongoose.Types.ObjectId; // Admin who added the device
    lastModifiedBy?: mongoose.Types.ObjectId; // Admin who last modified
    lastModified?: Date;
  };
  
  // Performance metrics
  performance: {
    averageRssi?: number;
    connectionReliability?: number; // 0-100 percentage
    averageConnectionTime?: number; // in seconds
    dataTransferRate?: number; // in bytes/sec
    latency?: number; // in milliseconds
    
    // Call-specific metrics
    voiceCallQuality?: number; // 0-100 quality score
    audioDropouts?: number; // Count of audio dropouts
    lastQualityCheck?: Date;
  };
  
  // Integration with other systems
  integration: {
    syncedWithUserModel: boolean; // Whether synced with User.bleDevices
    lastSyncAt?: Date;
    externalReferences?: Array<{
      system: string;
      reference: string;
      lastUpdated: Date;
    }>;
  };

  // Methods
  updateConnectionStatus(rssi?: number, connected?: boolean): void;
  recordVoiceCallUsage(success?: boolean): void;
  addSecurityViolation(type: string, details: string, severity?: string): void;
}

const BleDeviceSchema = new Schema<IBleDevice>(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    deviceId: { type: String, required: true, index: true },
    deviceName: { type: String, required: true },
    deviceType: { 
      type: String, 
      enum: ['earbud', 'headphone', 'speaker', 'generic'], 
      default: 'generic',
      index: true
    },
    
    // Registration data
    registrationData: {
      adData: Schema.Types.Mixed,
      characteristics: { type: Schema.Types.Mixed, default: {} },
      signature: String,
      signatureCharUuid: String,
      services: [String],
      rssi: Number,
      registeredAt: { type: Date, default: Date.now },
      registeredBy: { 
        type: String, 
        enum: ['script', 'admin', 'user'], 
        default: 'script' 
      },
      scriptVersion: String,
      registrationIP: String
    },
    
    // Status tracking
    status: {
      isVerified: { type: Boolean, default: false, index: true },
      isActive: { type: Boolean, default: true, index: true },
      lastSeen: { type: Date, default: Date.now, index: true },
      lastConnected: Date,
      connectionCount: { type: Number, default: 0 },
      lastRssi: Number,
      connectionHistory: [{
        timestamp: Date,
        rssi: Number,
        duration: Number,
        disconnectReason: String
      }]
    },
    
    // Voice call authorization
    voiceCallAuth: {
      enabled: { type: Boolean, default: true, index: true },
      lastUsedForCall: Date,
      callCount: { type: Number, default: 0 },
      authFailures: { type: Number, default: 0 },
      lastAuthFailure: Date,
      authSuccessRate: { type: Number, default: 100, min: 0, max: 100 }
    },
    
    // Security
    security: {
      authChallenge: String,
      challengeExpiry: Date,
      encryptedAuthData: String,
      riskScore: { type: Number, default: 0, min: 0, max: 100 },
      violations: [{
        type: { 
          type: String, 
          enum: ['auth_failure', 'suspicious_activity', 'connection_anomaly', 'security_breach'] 
        },
        timestamp: Date,
        details: String,
        severity: { 
          type: String, 
          enum: ['low', 'medium', 'high', 'critical'],
          default: 'low'
        },
        resolved: { type: Boolean, default: false }
      }],
      lastSecurityScan: Date,
      securityFlags: [String]
    },
    
    // Metadata
    metadata: {
      manufacturer: String,
      model: String,
      firmwareVersion: String,
      batteryLevel: { type: Number, min: 0, max: 100 },
      capabilities: [String],
      notes: String,
      tags: [String],
      bluetoothVersion: String,
      supportedCodecs: [String],
      maxRange: Number,
      batteryCapacity: Number,
      addedBy: { type: Schema.Types.ObjectId, ref: 'Admin' },
      lastModifiedBy: { type: Schema.Types.ObjectId, ref: 'Admin' },
      lastModified: Date
    },
    
    // Performance metrics
    performance: {
      averageRssi: Number,
      connectionReliability: { type: Number, min: 0, max: 100 },
      averageConnectionTime: Number,
      dataTransferRate: Number,
      latency: Number,
      voiceCallQuality: { type: Number, min: 0, max: 100 },
      audioDropouts: { type: Number, default: 0 },
      lastQualityCheck: Date
    },
    
    // Integration
    integration: {
      syncedWithUserModel: { type: Boolean, default: false },
      lastSyncAt: Date,
      externalReferences: [{
        system: String,
        reference: String,
        lastUpdated: Date
      }]
    }
  },
  {
    timestamps: true,
    collection: 'ble_devices'
  }
);

// Compound indexes for efficient querying
BleDeviceSchema.index({ userId: 1, deviceId: 1 }, { unique: true });
BleDeviceSchema.index({ userId: 1, 'status.isActive': 1 });
BleDeviceSchema.index({ userId: 1, 'voiceCallAuth.enabled': 1 });
BleDeviceSchema.index({ 'status.lastSeen': 1 });
BleDeviceSchema.index({ 'security.riskScore': 1 });

// Virtual for device age
BleDeviceSchema.virtual('deviceAge').get(function() {
  return Date.now() - this.registrationData.registeredAt.getTime();
});

// Methods
BleDeviceSchema.methods.updateConnectionStatus = function(rssi?: number, connected: boolean = true) {
  this.status.lastSeen = new Date();
  this.status.connectionCount += 1;
  
  if (rssi) {
    this.status.lastRssi = rssi;
    
    // Update average RSSI
    if (this.performance.averageRssi) {
      this.performance.averageRssi = (this.performance.averageRssi + rssi) / 2;
    } else {
      this.performance.averageRssi = rssi;
    }
  }
  
  if (connected) {
    this.status.lastConnected = new Date();
  }
  
  // Add to connection history
  this.status.connectionHistory.push({
    timestamp: new Date(),
    rssi: rssi,
    duration: 0 // Will be updated when disconnected
  });
  
  // Keep only last 100 connection records
  if (this.status.connectionHistory.length > 100) {
    this.status.connectionHistory = this.status.connectionHistory.slice(-100);
  }
};

BleDeviceSchema.methods.recordVoiceCallUsage = function(success: boolean = true) {
  this.voiceCallAuth.callCount += 1;
  this.voiceCallAuth.lastUsedForCall = new Date();
  
  if (!success) {
    this.voiceCallAuth.authFailures += 1;
    this.voiceCallAuth.lastAuthFailure = new Date();
  }
  
  // Update success rate
  this.voiceCallAuth.authSuccessRate = 
    ((this.voiceCallAuth.callCount - this.voiceCallAuth.authFailures) / this.voiceCallAuth.callCount) * 100;
};

BleDeviceSchema.methods.addSecurityViolation = function(
  type: string, 
  details: string, 
  severity: string = 'low'
) {
  this.security.violations.push({
    type,
    timestamp: new Date(),
    details,
    severity,
    resolved: false
  });
  
  // Increase risk score based on severity
  const riskIncrease = {
    low: 5,
    medium: 15,
    high: 30,
    critical: 50
  }[severity] || 5;
  
  this.security.riskScore = Math.min(100, this.security.riskScore + riskIncrease);
};

export default mongoose.model<IBleDevice>('BleDevice', BleDeviceSchema);
