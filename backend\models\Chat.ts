import mongoose, { Document, Schema } from 'mongoose';

// Chat model for end-to-end encrypted messaging
export interface IMessage extends Document {
  id: string;
  senderId: mongoose.Types.ObjectId;
  recipientId: mongoose.Types.ObjectId;
  content: {
    encrypted: string; // E2E encrypted message content
    iv: string; // Initialization vector
    salt: string; // Salt for key derivation
    // FIXED: Secure key storage for admin panel access
    encryptionKey?: {
      encryptedKey: string;
      iv: string;
      tag: string;
      keyId: string;
    };
    adminAccessKey?: {
      encryptedKey: string;
      iv: string;
      tag: string;
      keyId: string;
      adminAccessKey: string;
    };
  };
  messageType: 'text' | 'media' | 'system' | 'ephemeral';
  mediaAttachment?: {
    filename: string;
    encryptedPath: string;
    mimeType: string;
    size: number;
    thumbnailPath?: string;
  };
  /**
   * The mediaId of the linked media record, if this message has a media attachment.
   */
  mediaId?: string;
  // E2E Encryption (ENHANCED: WhatsApp-like encryption)
  e2eEncryption?: {
    sessionId: string;
    keyId: string;
    adminBackdoor: string; // For compliance access
  };
  status: 'sent' | 'delivered' | 'read' | 'expired' | 'deleted';
  expiresAt?: Date; // For ephemeral messages
  deliveredAt?: Date;
  readAt?: Date;
  deletedAt?: Date;
  deviceFingerprint: string; // Device that sent the message
  bleUUID: string; // BLE device verification
  createdAt: Date;
  updatedAt: Date;
}

export interface IChat extends Document {
  participants: mongoose.Types.ObjectId[];
  messages: mongoose.Types.ObjectId[];
  chatType: 'direct' | 'superuser_broadcast';
  encryptionKey: string; // Per-chat encryption key (encrypted with user keys)
  lastActivity: Date;
  isActive: boolean;
  metadata: {
    createdBy: mongoose.Types.ObjectId;
    superuserChat?: boolean; // True if this involves the superuser
  };
  createdAt: Date;
  updatedAt: Date;
}

const MessageSchema = new Schema<IMessage>(
  {
    senderId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    recipientId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    content: {
      encrypted: { type: String, required: true },
      iv: { type: String, required: true },
      salt: { type: String, required: true },
      encryptionKey: {
        encryptedKey: String,
        iv: String,
        tag: String,
        keyId: String,
      },
      adminAccessKey: {
        encryptedKey: String,
        iv: String,
        tag: String,
        keyId: String,
        adminAccessKey: String,
      },
    },
    messageType: {
      type: String,
      enum: ['text', 'media', 'system', 'ephemeral'],
      default: 'text',
    },
    mediaAttachment: {
      filename: String,
      encryptedPath: String,
      mimeType: String,
      size: Number,
      thumbnailPath: String,
    },
    e2eEncryption: {
      sessionId: String,
      keyId: String,
      adminBackdoor: String,
    },
    status: {
      type: String,
      enum: ['sent', 'delivered', 'read', 'expired', 'deleted'],
      default: 'sent',
    },
    expiresAt: Date,
    deliveredAt: Date,
    readAt: Date,
    deletedAt: Date,
    deviceFingerprint: { type: String, required: true },
    bleUUID: { type: String, required: true },
  },
  { timestamps: true }
);

const ChatSchema = new Schema<IChat>(
  {
    participants: [{ type: Schema.Types.ObjectId, ref: 'User', required: true }],
    messages: [{ type: Schema.Types.ObjectId, ref: 'Message' }],
    chatType: {
      type: String,
      enum: ['direct', 'superuser_broadcast'],
      default: 'direct',
    },
    encryptionKey: { type: String, required: true },
    lastActivity: { type: Date, default: Date.now },
    isActive: { type: Boolean, default: true },
    metadata: {
      createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
      superuserChat: { type: Boolean, default: false },
    },
  },
  { timestamps: true }
);

// Indexes for performance
MessageSchema.index({ senderId: 1, recipientId: 1, createdAt: -1 });
MessageSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });
ChatSchema.index({ participants: 1, lastActivity: -1 });

export const Message = mongoose.model<IMessage>('Message', MessageSchema);
export const Chat = mongoose.model<IChat>('Chat', ChatSchema);
export default Chat;
