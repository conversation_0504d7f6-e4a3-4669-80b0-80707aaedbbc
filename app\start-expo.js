#!/usr/bin/env node

/**
 * Expo Start Script
 * Ensures Metro bundler runs from the correct directory
 */

const { execSync } = require('child_process');
const path = require('path');

// Ensure we're in the app directory
const appDir = __dirname;
process.chdir(appDir);

console.log('🚀 Starting Expo from app directory:', appDir);
console.log('📁 Current working directory:', process.cwd());

try {
  // Clear Metro cache first
  console.log('🧹 Clearing Metro cache...');
  execSync('npx expo start --clear', { stdio: 'inherit', cwd: appDir });
} catch (error) {
  console.error('❌ Failed to start Expo:', error.message);
  process.exit(1);
}
