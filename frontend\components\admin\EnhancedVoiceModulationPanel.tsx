/**
 * Enhanced Voice Modulation Panel
 * Advanced configuration interface for voice modulation with SoX integration
 * Provides comprehensive tone modification controls for admin panel
 */

import React, { useState, useEffect, useRef } from 'react';
import tokenManager from '../../utils/tokenManager';
import apiClient from '../../utils/apiClient';

export interface EnhancedVoiceProfile {
  name: string;
  description: string;

  // Basic parameters (legacy compatibility)
  pitch: number;
  tempo: number;
  reverb: number;
  distortion: number;
  formant: number;
  chorus: boolean;
  normalize: boolean;

  // WORLD Vocoder Parameters (Advanced Voice Processing)
  pitchScale: number;      // Pitch modification (0.7-1.3)
  spectralWarp: number;    // Formant shifting (-10% to +10%)
  reverbAmount: number;    // Spatial distortion (0-50%)
  eqTilt: number;         // Frequency emphasis (-6dB to +6dB)
  temporalJitter: number; // Anti-forensic timing variation (0-10%)
  spectralNoise: number;  // Irreversible spectral masking (0-10%)

  // Enhanced tone modification parameters
  bassGain: number;
  trebleGain: number;
  midGain: number;
  compandRatio: number;
  spectralTilt: number;
  harmonicDistortion: number;
  vocoderStrength: number;

  // Multi-band equalizer
  eqBands: Array<{
    frequency: number;
    gain: number;
    width: number;
  }>;

  // Processing preferences
  preserveClarity: boolean;
  antiForensic: boolean;
  realTimeOptimized: boolean;

  // Custom SoX parameters
  customSoxArgs: string[];
  advancedMode: boolean;
  presetType: 'basic' | 'advanced' | 'expert';
  userType: 'all' | 'regular' | 'superuser';
  isCustom: boolean;
}

interface Props {
  isOpen: boolean;
  onClose: () => void;
  userId?: string;
  currentProfile?: string;
  onProfileUpdate?: (profileName: string) => void;
}

const EnhancedVoiceModulationPanel: React.FC<Props> = ({
  isOpen,
  onClose,
  userId,
  currentProfile,
  onProfileUpdate
}) => {
  const [activeTab, setActiveTab] = useState<'profiles' | 'create' | 'samples'>('profiles');
  const [profiles, setProfiles] = useState<EnhancedVoiceProfile[]>([]);
  const [selectedProfile, setSelectedProfile] = useState<EnhancedVoiceProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string>('');
  const [error, setError] = useState<string>('');

  // Audio testing and upload
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [testingProfile, setTestingProfile] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentlyPlayingProfile, setCurrentlyPlayingProfile] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  
  // Custom profile creation state
  const [customProfile, setCustomProfile] = useState<EnhancedVoiceProfile>({
    name: '',
    description: '',

    // Basic parameters
    pitch: 0,
    tempo: 1.0,
    reverb: 0,
    distortion: 0,
    formant: 0,
    chorus: false,
    normalize: true,

    // WORLD Vocoder parameters
    pitchScale: 1.0,        // Default neutral (0.7-1.3)
    spectralWarp: 0.0,      // Default neutral (-10 to +10)
    reverbAmount: 0.0,      // Default dry (0-50%)
    eqTilt: 0.0,           // Default flat (-6 to +6 dB)
    temporalJitter: 0.0,    // Default none (0-10%)
    spectralNoise: 0.0,     // Default none (0-10%)

    // Enhanced tone modification
    bassGain: 0,
    trebleGain: 0,
    midGain: 0,
    compandRatio: 1.0,
    spectralTilt: 0,
    harmonicDistortion: 0,
    vocoderStrength: 0,

    // Multi-band EQ
    eqBands: [
      { frequency: 100, gain: 0, width: 1.0 },
      { frequency: 1000, gain: 0, width: 1.0 },
      { frequency: 8000, gain: 0, width: 1.0 }
    ],

    // Processing preferences
    preserveClarity: true,
    antiForensic: false,
    realTimeOptimized: false,

    // Configuration
    customSoxArgs: [],
    advancedMode: false,
    presetType: 'basic',
    userType: 'all',
    isCustom: true
  });

  // Fetch available profiles
  useEffect(() => {
    if (isOpen) {
      fetchProfiles();
    }
  }, [isOpen]);

  const fetchProfiles = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.get('/api/voice/profiles/enhanced');
      
      if (response.data.success) {
        setProfiles(response.data.profiles);
      }
    } catch (error: any) {
      console.error('Failed to fetch profiles:', error);
      setError('Failed to load voice profiles');
    } finally {
      setIsLoading(false);
    }
  };

  const testProfile = async (profile: EnhancedVoiceProfile) => {
    try {
      setTestingProfile(true);
      setError('');
      
      const response = await apiClient.post('/api/voice/test-enhanced-profile', {
        profile: profile,
        generateSample: true
      }, {
        responseType: 'blob'
      });

      if (response.data) {
        // Clean up previous audio URL
        if (audioUrl && audioUrl.includes('blob:')) {
          URL.revokeObjectURL(audioUrl);
        }
        
        const audioBlob = new Blob([response.data], { type: 'audio/wav' });
        const newAudioUrl = URL.createObjectURL(audioBlob);
        setAudioUrl(newAudioUrl);
        
        // Auto-play the result
        if (audioRef.current) {
          audioRef.current.src = newAudioUrl;
          audioRef.current.play();
          setIsPlaying(true);
        }
      }
    } catch (error: any) {
      console.error('Failed to test profile:', error);
      setError('Failed to test profile');
    } finally {
      setTestingProfile(false);
    }
  };

  const createCustomProfile = async () => {
    try {
      setIsLoading(true);
      setError('');
      setMessage('Creating enhanced profile...');

      const response = await apiClient.post('/api/voice/profiles/enhanced', customProfile);

      if (response.data.success) {
        setMessage('Enhanced profile created successfully!');

        // Reset form
        setCustomProfile({
          name: '',
          description: '',

          // Basic parameters
          pitch: 0,
          tempo: 1.0,
          reverb: 0,
          distortion: 0,
          formant: 0,
          chorus: false,
          normalize: true,

          // WORLD Vocoder parameters
          pitchScale: 1.0,
          spectralWarp: 0.0,
          reverbAmount: 0.0,
          eqTilt: 0.0,
          temporalJitter: 0.0,
          spectralNoise: 0.0,

          // Enhanced tone modification
          bassGain: 0,
          trebleGain: 0,
          midGain: 0,
          compandRatio: 1.0,
          spectralTilt: 0,
          harmonicDistortion: 0,
          vocoderStrength: 0,

          // Multi-band EQ
          eqBands: [
            { frequency: 100, gain: 0, width: 1.0 },
            { frequency: 1000, gain: 0, width: 1.0 },
            { frequency: 8000, gain: 0, width: 1.0 }
          ],

          // Processing preferences
          preserveClarity: true,
          antiForensic: false,
          realTimeOptimized: false,

          // Configuration
          customSoxArgs: [],
          advancedMode: false,
          presetType: 'basic',
          userType: 'all',
          isCustom: true
        });

        // Refresh profiles list
        await fetchProfiles();
        setActiveTab('profiles');
      } else {
        throw new Error(response.data.error || 'Failed to create profile');
      }
    } catch (error: any) {
      console.error('Failed to create profile:', error);
      setError(`Failed to create profile: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testProfileWithUploadedAudio = async (profile: EnhancedVoiceProfile) => {
    if (!audioFile) {
      setError('No audio file selected');
      return;
    }

    try {
      setTestingProfile(true);
      setCurrentlyPlayingProfile(profile.name);
      setError('');
      setMessage(`Testing ${profile.name} with uploaded audio...`);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('audioFile', audioFile);
      formData.append('profile', JSON.stringify(profile));

      const response = await apiClient.post('/api/voice/test-enhanced-profile-upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseType: 'blob'
      });

      if (response.data) {
        // Clean up previous audio URL
        if (audioUrl && audioUrl.includes('blob:')) {
          URL.revokeObjectURL(audioUrl);
        }

        const audioBlob = new Blob([response.data], { type: 'audio/wav' });
        const newAudioUrl = URL.createObjectURL(audioBlob);
        setAudioUrl(newAudioUrl);
        setMessage(`Successfully processed audio with ${profile.name} profile`);

        // Auto-play the result
        if (audioRef.current) {
          audioRef.current.src = newAudioUrl;
          audioRef.current.play();
          setIsPlaying(true);
        }
      }
    } catch (error: any) {
      console.error('Failed to test profile with uploaded audio:', error);
      setError(`Failed to test profile: ${error.message}`);
      setCurrentlyPlayingProfile(null);
    } finally {
      setTestingProfile(false);
    }
  };

  const updateEqBand = (index: number, field: 'frequency' | 'gain' | 'width', value: number) => {
    const newEqBands = [...customProfile.eqBands];
    newEqBands[index] = { ...newEqBands[index], [field]: value };
    setCustomProfile({ ...customProfile, eqBands: newEqBands });
  };

  const addEqBand = () => {
    setCustomProfile({
      ...customProfile,
      eqBands: [...customProfile.eqBands, { frequency: 1000, gain: 0, width: 1.0 }]
    });
  };

  const removeEqBand = (index: number) => {
    const newEqBands = customProfile.eqBands.filter((_, i) => i !== index);
    setCustomProfile({ ...customProfile, eqBands: newEqBands });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-7xl h-5/6 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex-shrink-0 bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold">Custom Voice Profile Studio</h2>
              <p className="text-purple-100 mt-1">Comprehensive voice transformation with WORLD vocoder + Enhanced SoX processing</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex-shrink-0 px-6 py-3 border-b border-gray-100">
          <div className="flex space-x-1">
            {['profiles', 'create', 'samples'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab as any)}
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === tab
                    ? 'bg-purple-100 text-purple-700'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                }`}
              >
                {tab === 'profiles' ? 'Browse Profiles' :
                 tab === 'create' ? 'Create Profile' : 'Test Samples'}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Messages */}
          {message && (
            <div className={`mb-4 p-4 rounded-lg text-sm ${
              message.includes('successfully') || message.includes('created')
                ? 'bg-green-50 border border-green-200 text-green-800'
                : message.includes('failed') || message.includes('error')
                ? 'bg-red-50 border border-red-200 text-red-800'
                : 'bg-blue-50 border border-blue-200 text-blue-800'
            }`}>
              <div className="flex">
                <svg className="flex-shrink-0 w-4 h-4 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <p className="ml-2">{message}</p>
              </div>
            </div>
          )}

          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 text-red-800 rounded-lg text-sm">
              <div className="flex">
                <svg className="flex-shrink-0 w-4 h-4 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <p className="ml-2">{error}</p>
              </div>
            </div>
          )}

          {/* Audio Player */}
          {audioUrl && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Profile Test Audio</h4>
              <audio
                ref={audioRef}
                controls
                className="w-full"
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onEnded={() => setIsPlaying(false)}
              >
                <source src={audioUrl} type="audio/wav" />
                Your browser does not support the audio element.
              </audio>
            </div>
          )}

          {/* Profiles Tab */}
          {activeTab === 'profiles' && (
            <div className="space-y-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                  <p className="ml-3 text-gray-600">Loading profiles...</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {profiles.map((profile, index) => (
                    <div
                      key={index}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        selectedProfile?.name === profile.name
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedProfile(profile)}
                    >
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="font-medium text-gray-900">{profile.name}</h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          profile.presetType === 'expert' ? 'bg-red-100 text-red-800' :
                          profile.presetType === 'advanced' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {profile.presetType}
                        </span>
                      </div>

                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{profile.description}</p>

                      {/* Key Parameters */}
                      <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-3">
                        <div>Pitch: {profile.pitchScale?.toFixed(2) || '1.00'}×</div>
                        <div>Spectral: {profile.spectralWarp?.toFixed(1) || '0.0'}</div>
                        <div>EQ Tilt: {profile.eqTilt?.toFixed(1) || '0.0'}dB</div>
                        <div>Reverb: {profile.reverbAmount?.toFixed(1) || '0.0'}%</div>
                      </div>

                      {/* Anti-forensic indicator */}
                      {((profile.temporalJitter || 0) > 0 || (profile.spectralNoise || 0) > 0) && (
                        <div className="flex items-center text-xs text-orange-600 mb-3">
                          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          Anti-forensic enabled
                        </div>
                      )}

                      <div className="flex space-x-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            testProfile(profile);
                          }}
                          disabled={testingProfile}
                          className="flex-1 px-3 py-2 bg-purple-50 text-purple-700 text-sm rounded-md hover:bg-purple-100 disabled:opacity-50 transition-colors"
                        >
                          {testingProfile ? 'Testing...' : 'Test'}
                        </button>
                        {userId && onProfileUpdate && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onProfileUpdate(profile.name);
                            }}
                            className="flex-1 px-3 py-2 bg-green-50 text-green-700 text-sm rounded-md hover:bg-green-100 transition-colors"
                          >
                            Apply
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Create Profile Tab */}
          {activeTab === 'create' && (
            <div className="space-y-6">
              <div className="text-sm text-gray-600">
                Create custom voice profiles with advanced parameters for specific users or use cases.
              </div>

              {/* Profile Basic Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-md font-semibold text-gray-900 mb-4">Profile Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Profile Name</label>
                    <input
                      type="text"
                      value={customProfile.name}
                      onChange={(e) => setCustomProfile({ ...customProfile, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="Enter profile name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">User Access</label>
                    <select
                      value={customProfile.userType}
                      onChange={(e) => setCustomProfile({ ...customProfile, userType: e.target.value as any })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      <option value="all">All Users</option>
                      <option value="regular">Regular Users Only</option>
                      <option value="superuser">Superuser Only</option>
                    </select>
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    value={customProfile.description}
                    onChange={(e) => setCustomProfile({ ...customProfile, description: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Describe the voice profile characteristics and intended use"
                  />
                </div>
              </div>

              {/* Voice Parameters */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Primary Controls */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-md font-semibold text-gray-900 mb-4">Primary Voice Controls</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Pitch Scale: {customProfile.pitchScale.toFixed(2)}
                      </label>
                      <input
                        type="range"
                        min="0.7"
                        max="1.3"
                        step="0.01"
                        value={customProfile.pitchScale}
                        onChange={(e) => setCustomProfile({ ...customProfile, pitchScale: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0.7 (lower)</span>
                        <span>1.3 (higher)</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Spectral Warp: {customProfile.spectralWarp.toFixed(1)}
                      </label>
                      <input
                        type="range"
                        min="-10"
                        max="10"
                        step="0.1"
                        value={customProfile.spectralWarp}
                        onChange={(e) => setCustomProfile({ ...customProfile, spectralWarp: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>-10 (formant down)</span>
                        <span>+10 (formant up)</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Tempo: {customProfile.tempo.toFixed(2)}x
                      </label>
                      <input
                        type="range"
                        min="0.5"
                        max="2.0"
                        step="0.01"
                        value={customProfile.tempo}
                        onChange={(e) => setCustomProfile({ ...customProfile, tempo: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0.5x (slow)</span>
                        <span>2.0x (fast)</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Frequency Controls */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-md font-semibold text-gray-900 mb-4">Frequency Controls</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        EQ Tilt: {customProfile.eqTilt > 0 ? '+' : ''}{customProfile.eqTilt.toFixed(1)}dB
                      </label>
                      <input
                        type="range"
                        min="-6"
                        max="6"
                        step="0.1"
                        value={customProfile.eqTilt}
                        onChange={(e) => setCustomProfile({ ...customProfile, eqTilt: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>-6dB (bass)</span>
                        <span>+6dB (treble)</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Bass Gain: {customProfile.bassGain > 0 ? '+' : ''}{customProfile.bassGain.toFixed(1)}dB
                      </label>
                      <input
                        type="range"
                        min="-20"
                        max="20"
                        step="0.5"
                        value={customProfile.bassGain}
                        onChange={(e) => setCustomProfile({ ...customProfile, bassGain: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>-20dB</span>
                        <span>+20dB</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Treble Gain: {customProfile.trebleGain > 0 ? '+' : ''}{customProfile.trebleGain.toFixed(1)}dB
                      </label>
                      <input
                        type="range"
                        min="-20"
                        max="20"
                        step="0.5"
                        value={customProfile.trebleGain}
                        onChange={(e) => setCustomProfile({ ...customProfile, trebleGain: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>-20dB</span>
                        <span>+20dB</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Effects and Advanced Controls */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Spatial Effects */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-md font-semibold text-gray-900 mb-4">Spatial Effects</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Reverb Amount: {customProfile.reverbAmount.toFixed(1)}%
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="50"
                        step="0.1"
                        value={customProfile.reverbAmount}
                        onChange={(e) => setCustomProfile({ ...customProfile, reverbAmount: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0% (dry)</span>
                        <span>50% (wet)</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Temporal Jitter: {customProfile.temporalJitter.toFixed(1)}%
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="10"
                        step="0.1"
                        value={customProfile.temporalJitter}
                        onChange={(e) => setCustomProfile({ ...customProfile, temporalJitter: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0% (none)</span>
                        <span>10% (high)</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Spectral Noise: {customProfile.spectralNoise.toFixed(1)}%
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="10"
                        step="0.1"
                        value={customProfile.spectralNoise}
                        onChange={(e) => setCustomProfile({ ...customProfile, spectralNoise: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0% (none)</span>
                        <span>10% (max)</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Advanced Processing */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-md font-semibold text-gray-900 mb-4">Advanced Processing</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Compression Ratio: {customProfile.compandRatio.toFixed(1)}:1
                      </label>
                      <input
                        type="range"
                        min="1.0"
                        max="10.0"
                        step="0.1"
                        value={customProfile.compandRatio}
                        onChange={(e) => setCustomProfile({ ...customProfile, compandRatio: parseFloat(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>1.0 (none)</span>
                        <span>10.0 (heavy)</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Harmonic Distortion: {customProfile.harmonicDistortion}%
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="50"
                        step="1"
                        value={customProfile.harmonicDistortion}
                        onChange={(e) => setCustomProfile({ ...customProfile, harmonicDistortion: parseInt(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0% (clean)</span>
                        <span>50% (heavy)</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Vocoder Strength: {customProfile.vocoderStrength}%
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="100"
                        step="1"
                        value={customProfile.vocoderStrength}
                        onChange={(e) => setCustomProfile({ ...customProfile, vocoderStrength: parseInt(e.target.value) })}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0% (off)</span>
                        <span>100% (robotic)</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Processing Options */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-md font-semibold text-gray-900 mb-4">Processing Options</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={customProfile.preserveClarity}
                      onChange={(e) => setCustomProfile({ ...customProfile, preserveClarity: e.target.checked })}
                      className="mr-2"
                    />
                    <div>
                      <span className="text-sm font-medium text-gray-700">Preserve Clarity</span>
                      <p className="text-xs text-gray-500">Maintain speech intelligibility</p>
                    </div>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={customProfile.antiForensic}
                      onChange={(e) => setCustomProfile({ ...customProfile, antiForensic: e.target.checked })}
                      className="mr-2"
                    />
                    <div>
                      <span className="text-sm font-medium text-gray-700">Anti-Forensic</span>
                      <p className="text-xs text-gray-500">Enable anti-forensic features</p>
                    </div>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={customProfile.normalize}
                      onChange={(e) => setCustomProfile({ ...customProfile, normalize: e.target.checked })}
                      className="mr-2"
                    />
                    <div>
                      <span className="text-sm font-medium text-gray-700">Normalize Audio</span>
                      <p className="text-xs text-gray-500">Auto-level adjustment</p>
                    </div>
                  </label>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between pt-4 border-t border-gray-200">
                <button
                  onClick={() => testProfile(customProfile)}
                  disabled={testingProfile || !customProfile.name}
                  className="px-6 py-2 bg-purple-50 text-purple-700 rounded-md hover:bg-purple-100 disabled:opacity-50 transition-colors"
                >
                  {testingProfile ? 'Testing...' : 'Test Profile'}
                </button>
                <button
                  onClick={createCustomProfile}
                  disabled={isLoading || !customProfile.name}
                  className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 transition-colors"
                >
                  {isLoading ? 'Creating...' : 'Create Profile'}
                </button>
              </div>

            </div>
          )}

          {/* Test Samples Tab */}
          {activeTab === 'samples' && (
            <div className="space-y-6">
              <div className="text-sm text-gray-600">
                Upload audio samples to test voice profiles with your own audio content.
              </div>

              {/* Audio Upload */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-md font-semibold text-gray-900 mb-4">Upload Audio Sample</h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Audio File
                    </label>
                    <input
                      type="file"
                      accept="audio/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          setAudioFile(file);
                          setMessage(`Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
                        }
                      }}
                      className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Supported formats: MP3, WAV, M4A, OGG (Max: 10MB)
                    </p>
                  </div>

                  {audioFile && (
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12a7.971 7.971 0 00-1.343-4.243 1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                        <span className="text-sm text-blue-800">
                          {audioFile.name} ({(audioFile.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Profile Selection for Testing */}
              {audioFile && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-md font-semibold text-gray-900 mb-4">Test with Profiles</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {profiles.map((profile, index) => (
                      <button
                        key={index}
                        onClick={() => testProfileWithUploadedAudio(profile)}
                        disabled={testingProfile}
                        className={`p-3 text-left border rounded-lg transition-colors ${
                          testingProfile
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-white hover:bg-purple-50 hover:border-purple-300'
                        }`}
                      >
                        <div className="font-medium text-sm">{profile.name}</div>
                        <div className="text-xs text-gray-500 mt-1">{profile.description}</div>
                        <div className="flex items-center mt-2">
                          {currentlyPlayingProfile === profile.name ? (
                            <div className="flex items-center text-purple-600">
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-purple-600 mr-2"></div>
                              <span className="text-xs">Processing...</span>
                            </div>
                          ) : (
                            <span className="text-xs text-purple-600">Click to test</span>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Results */}
              {audioUrl && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-md font-semibold text-gray-900 mb-4">Test Results</h4>
                  <div className="space-y-3">
                    <div className="p-3 bg-white border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Processed Audio</span>
                        <span className="text-xs text-gray-500">Profile: {currentlyPlayingProfile}</span>
                      </div>
                      <audio
                        ref={audioRef}
                        controls
                        className="w-full"
                        onPlay={() => setIsPlaying(true)}
                        onPause={() => setIsPlaying(false)}
                        onEnded={() => setIsPlaying(false)}
                      >
                        <source src={audioUrl} type="audio/wav" />
                        Your browser does not support the audio element.
                      </audio>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedVoiceModulationPanel;
