import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import apiClient from '../utils/apiClient';
import {
  Mic as MicrophoneIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Upload as CloudArrowUpIcon,
  Download as CloudArrowDownIcon,
  Settings as CogIcon,
  Warning as ExclamationTriangleIcon,
  CheckCircle as CheckCircleIcon,
  VolumeUp as SpeakerWaveIcon,
  <PERSON>ne as SlidersIcon
} from '@mui/icons-material';

interface NeutralizationProfile {
  name: string;
  level: 'LIGHT' | 'MEDIUM' | 'HEAVY';
  preserveClarity: boolean;
  realTimeMode: boolean;
  latencyTarget: number;
  processing: {
    f0Neutralization: boolean;
    formantNormalization: boolean;
    spectralSmoothing: number;
    temporalJitter: number;
    noiseLevel: number;
    additionalPasses?: number;
  };
  description: string;
  recommended: boolean;
}

interface VoiceNeutralizationProps {
  onProfileChange?: (profile: string) => void;
  selectedProfile?: string;
  className?: string;
}

const VoiceNeutralization: React.FC<VoiceNeutralizationProps> = ({
  onProfileChange,
  selectedProfile = 'REAL_TIME_MEDIUM',
  className = ''
}) => {
  const [profiles, setProfiles] = useState<NeutralizationProfile[]>([]);
  const [currentProfile, setCurrentProfile] = useState<string>(selectedProfile);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [processedAudioUrl, setProcessedAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [feasibilityData, setFeasibilityData] = useState<any>(null);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Neutralization profiles with clarity-focused configurations (updated to match backend)
  const neutralizationProfiles: NeutralizationProfile[] = [
    {
      name: 'REAL_TIME_LIGHT',
      level: 'LIGHT',
      preserveClarity: true,
      realTimeMode: true,
      latencyTarget: 20,
      processing: {
        f0Neutralization: true,
        formantNormalization: false, // Disabled for maximum clarity
        spectralSmoothing: 0.05, // Very minimal smoothing to preserve clarity
        temporalJitter: 0.5, // ±0.5ms minimal jitter
        noiseLevel: 0.0005 // Extremely low noise level
      },
      description: 'Ultra-light processing for real-time calls. Maximum clarity and intelligibility with basic anonymization.',
      recommended: true
    },
    {
      name: 'REAL_TIME_MEDIUM',
      level: 'MEDIUM',
      preserveClarity: true,
      realTimeMode: true,
      latencyTarget: 40,
      processing: {
        f0Neutralization: true,
        formantNormalization: false, // Disabled to preserve speech clarity
        spectralSmoothing: 0.08, // Very light smoothing
        temporalJitter: 1, // ±1ms
        noiseLevel: 0.001 // Very light noise
      },
      description: 'Balanced anonymization with excellent speech clarity. Optimized for real-time voice calls.',
      recommended: true
    },
    {
      name: 'OFFLINE_HEAVY',
      level: 'HEAVY',
      preserveClarity: true,
      realTimeMode: false,
      latencyTarget: 200,
      processing: {
        f0Neutralization: true,
        formantNormalization: false, // Keep disabled for clarity
        spectralSmoothing: 0.12, // Light smoothing
        temporalJitter: 1.5, // ±1.5ms
        noiseLevel: 0.002, // Light noise
        additionalPasses: 0 // No additional passes for now
      },
      description: 'Enhanced anonymization for recorded messages while maintaining speech intelligibility and clarity.',
      recommended: false
    }
  ];

  useEffect(() => {
    setProfiles(neutralizationProfiles);
    setLoading(false);
    checkFeasibility(currentProfile);
  }, [currentProfile]);

  const checkFeasibility = async (profileName: string) => {
    try {
      const response = await apiClient.get('/api/voice/feasibility', {
        params: {
          audioLengthMs: 30000, // 30 seconds test
          profile: profileName
        }
      });
      setFeasibilityData(response.data.data);
    } catch (error) {
      console.error('Failed to check feasibility:', error);
    }
  };

  const handleProfileChange = (profileName: string) => {
    setCurrentProfile(profileName);
    onProfileChange?.(profileName);
    checkFeasibility(profileName);
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;

      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      const chunks: BlobPart[] = [];
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        setAudioBlob(blob);
      };

      mediaRecorder.start();
      setIsRecording(true);
      setError(null);
    } catch (error) {
      setError('Failed to access microphone');
      console.error('Recording error:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
  };

  const processAudio = async () => {
    if (!audioBlob) return;

    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.wav');
      formData.append('profile', currentProfile);

      const response = await apiClient.post('/api/voice/neutralize', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseType: 'blob'
      });

      console.log('Response received:', {
        status: response.status,
        headers: response.headers,
        dataType: typeof response.data,
        dataSize: response.data.size,
        contentType: response.headers['content-type']
      });

      // Create blob with explicit audio/wav type
      const processedBlob = new Blob([response.data], { type: 'audio/wav' });
      console.log('Created blob:', {
        size: processedBlob.size,
        type: processedBlob.type
      });

      // Clean up previous URL if exists
      if (processedAudioUrl) {
        URL.revokeObjectURL(processedAudioUrl);
      }

      const url = URL.createObjectURL(processedBlob);
      setProcessedAudioUrl(url);

      console.log('Created object URL:', url);

      // Force audio element to load the new source
      setTimeout(() => {
        if (audioRef.current) {
          console.log('Forcing audio reload');
          audioRef.current.load();
        }
      }, 100);
    } catch (error) {
      setError('Failed to process audio');
      console.error('Processing error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const getLatencyBadge = (latency: number) => {
    if (latency <= 20) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Ultra Low</span>;
    } else if (latency <= 50) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Low</span>;
    } else {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Medium</span>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
        <div className="flex items-center">
          <SpeakerWaveIcon className="w-8 h-8 mr-3" />
          <div>
            <h2 className="text-2xl font-bold">Clarity-Focused Voice Neutralization</h2>
            <p className="text-blue-100 mt-1">
              Real-time voice anonymization with preserved clarity and intelligibility
            </p>
          </div>
        </div>
      </div>

      {/* Key Benefits */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg shadow border-l-4 border-green-500">
          <div className="flex items-center">
            <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2" />
            <h3 className="font-medium text-gray-900">Clear & Audible</h3>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Speech remains clear, understandable, and intelligible
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow border-l-4 border-blue-500">
          <div className="flex items-center">
            <SpeakerWaveIcon className="w-5 h-5 text-blue-500 mr-2" />
            <h3 className="font-medium text-gray-900">Real-time Ready</h3>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Low latency processing optimized for live voice calls
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow border-l-4 border-purple-500">
          <div className="flex items-center">
            <SlidersIcon className="w-5 h-5 text-purple-500 mr-2" />
            <h3 className="font-medium text-gray-900">Voice Anonymized</h3>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Removes voice characteristics while maintaining clarity
          </p>
        </div>
      </div>

      {/* Profile Selection */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Neutralization Profiles</h3>
        <div className="space-y-4">
          {profiles.map((profile) => (
            <motion.div
              key={profile.name}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                currentProfile === profile.name
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleProfileChange(profile.name)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h4 className="font-medium text-gray-900">{profile.name}</h4>
                    {profile.recommended && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Recommended
                      </span>
                    )}
                    {getLatencyBadge(profile.latencyTarget)}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{profile.description}</p>
                  
                  <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Level:</span>
                      <span className="ml-1 font-medium">{profile.level}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Latency:</span>
                      <span className="ml-1 font-medium">{profile.latencyTarget}ms</span>
                    </div>
                    <div>
                      <span className="text-gray-500">F0 Neutralization:</span>
                      <span className={`ml-1 font-medium ${profile.processing.f0Neutralization ? 'text-green-600' : 'text-red-600'}`}>
                        {profile.processing.f0Neutralization ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">Formant Norm:</span>
                      <span className={`ml-1 font-medium ${profile.processing.formantNormalization ? 'text-green-600' : 'text-red-600'}`}>
                        {profile.processing.formantNormalization ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Feasibility Check */}
      {feasibilityData && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Real-time Feasibility</h3>
          <div className="flex items-center space-x-4">
            <div className={`flex items-center ${feasibilityData.feasible ? 'text-green-600' : 'text-red-600'}`}>
              {feasibilityData.feasible ? (
                <CheckCircleIcon className="w-5 h-5 mr-2" />
              ) : (
                <ExclamationTriangleIcon className="w-5 h-5 mr-2" />
              )}
              <span className="font-medium">
                {feasibilityData.feasible ? 'Real-time processing feasible' : 'Real-time processing not feasible'}
              </span>
            </div>
          </div>
          {feasibilityData.recommendations?.length > 0 && (
            <div className="mt-2">
              <ul className="text-sm text-gray-600 space-y-1">
                {feasibilityData.recommendations.map((rec: string, index: number) => (
                  <li key={index} className="flex items-center">
                    <div className="w-1 h-1 bg-gray-400 rounded-full mr-2"></div>
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Testing Interface */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Test Voice Neutralization</h3>
        
        {/* Recording Controls */}
        <div className="flex items-center space-x-4 mb-6">
          <button
            onClick={isRecording ? stopRecording : startRecording}
            disabled={isProcessing}
            className={`flex items-center px-4 py-2 rounded-lg font-medium ${
              isRecording
                ? 'bg-red-600 text-white hover:bg-red-700'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {isRecording ? (
              <>
                <StopIcon className="w-5 h-5 mr-2" />
                Stop Recording
              </>
            ) : (
              <>
                <MicrophoneIcon className="w-5 h-5 mr-2" />
                Start Recording
              </>
            )}
          </button>

          {audioBlob && (
            <button
              onClick={processAudio}
              disabled={isProcessing}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <SpeakerWaveIcon className="w-5 h-5 mr-2" />
                  Neutralize Voice
                </>
              )}
            </button>
          )}
        </div>

        {/* Audio Players */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {audioBlob && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Original Recording</h4>
              <audio
                controls
                src={URL.createObjectURL(audioBlob)}
                className="w-full"
              />
            </div>
          )}

          {processedAudioUrl && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Neutralized Voice</h4>
              <audio
                key={processedAudioUrl}
                controls
                src={processedAudioUrl}
                className="w-full"
                ref={audioRef}
                preload="auto"
                onError={(e) => {
                  console.error('Audio playback error:', e);
                  console.error('Audio element error details:', {
                    error: audioRef.current?.error,
                    networkState: audioRef.current?.networkState,
                    readyState: audioRef.current?.readyState,
                    src: audioRef.current?.src
                  });
                }}
                onLoadStart={() => console.log('Audio load started')}
                onLoadedData={() => console.log('Audio data loaded')}
                onCanPlay={() => console.log('Audio can play')}
              />
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}
      </div>

      {/* Technical Details */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">How Voice Neutralization Works</h3>
        <div className="space-y-4 text-sm text-gray-600">
          <div>
            <h4 className="font-medium text-gray-900">F0 Neutralization</h4>
            <p>Removes fundamental frequency characteristics that make voices unique.</p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900">Formant Normalization</h4>
            <p>Adjusts vocal tract resonances to neutral characteristics.</p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900">Spectral Smoothing</h4>
            <p>Removes fine-grained spectral details while preserving intelligibility.</p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900">Temporal Jitter</h4>
            <p>Adds controlled timing variations to prevent voice matching.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoiceNeutralization;
