{"name": "ccalc-app", "version": "1.0.0", "main": "index.js", "private": true, "scripts": {"start": "expo start --port 8082", "android": "expo start --android --port 8082", "ios": "expo start --ios --port 8082", "web": "expo start --web --port 8082", "eject": "expo eject", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:ios": "eas build --platform ios --profile production", "build:ios:preview": "eas build --platform ios --profile preview", "build:android": "eas build --platform android --profile production", "build:preview": "eas build --platform all --profile preview", "build:development": "eas build --platform all --profile development", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean", "verify-setup": "node verify-setup.js", "verify-ios": "node scripts/verify-ios-build.js"}, "dependencies": {"@expo/vector-icons": "~14.1.0", "@react-native-async-storage/async-storage": "~2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "~6.1.18", "@react-navigation/stack": "~6.4.1", "@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "expo": "53.0.15", "expo-av": "~15.1.7", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.2", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.4", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-modules-core": "~2.4.0", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "expo-video-thumbnails": "~9.1.3", "metro": "~0.82.4", "metro-config": "~0.82.4", "metro-resolver": "~0.82.4", "react": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "~5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "~15.11.2", "react-native-webrtc": "^124.0.5"}, "devDependencies": {"@babel/core": "^7.25.0", "@babel/node": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/plugin-transform-typescript": "^7.27.1", "@babel/preset-env": "^7.25.0", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.28.2", "@expo/cli": "^0.21.0", "@react-native-community/cli": "14.1.0", "@react-native-community/cli-platform-ios": "14.1.0", "@react-native/eslint-config": "^0.76.0", "@react-native/metro-config": "^0.76.0", "@react-native/typescript-config": "^0.76.0", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.7.0", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-remove-console": "^6.9.4", "babel-preset-expo": "~13.0.0", "eas-cli": "^13.2.0", "eslint": "^8.57.0", "jest": "^29.7.0", "prettier": "^3.0.0", "react-test-renderer": "19.0.0", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "engines": {"node": ">=16"}}