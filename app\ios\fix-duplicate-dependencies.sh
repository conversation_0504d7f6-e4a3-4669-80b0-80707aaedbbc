#!/bin/bash

# CCALC Duplicate Dependencies Fix Script
# Resolves "multiple dependencies with different sources" CocoaPods error

set -e

echo "🔧 CCALC Duplicate Dependencies Fix"
echo "==================================="

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ This script must be run on macOS for iOS development"
    echo "✅ Podfile has been fixed to remove duplicate DoubleConversion dependency"
    echo "📋 Key fix applied:"
    echo "  • Removed explicit DoubleConversion pod declaration"
    echo "  • React Native will handle DoubleConversion automatically"
    echo ""
    echo "🚀 Ready for 'pod install' on macOS"
    exit 0
fi

# Navigate to iOS directory
cd "$(dirname "$0")"
echo "📁 Working directory: $(pwd)"

# Step 1: Clean existing problematic installation
echo ""
echo "🧹 Step 1: Cleaning existing CocoaPods installation..."

# Remove all CocoaPods artifacts
rm -rf Pods/
rm -f Podfile.lock
rm -rf CCALC.xcworkspace

# Clean Xcode derived data
rm -rf ~/Library/Developer/Xcode/DerivedData/CCALC-*

# Clear CocoaPods cache
pod cache clean --all

echo "✅ Cleanup completed"

# Step 2: Verify Podfile is clean
echo ""
echo "📋 Step 2: Verifying Podfile configuration..."

if [ -f "Podfile" ]; then
    echo "✅ Podfile exists"
    
    # Check for duplicate DoubleConversion declarations
    DOUBLECONV_COUNT=$(grep -c "DoubleConversion" Podfile || echo "0")
    
    if [ "$DOUBLECONV_COUNT" -eq 0 ]; then
        echo "✅ No explicit DoubleConversion declarations (correct)"
    elif [ "$DOUBLECONV_COUNT" -eq 1 ]; then
        echo "⚠️ One DoubleConversion declaration found - may cause conflicts"
        echo "Removing explicit DoubleConversion declaration..."
        sed -i '' '/DoubleConversion/d' Podfile
        echo "✅ Explicit DoubleConversion declaration removed"
    else
        echo "❌ Multiple DoubleConversion declarations found - fixing..."
        sed -i '' '/DoubleConversion/d' Podfile
        echo "✅ All explicit DoubleConversion declarations removed"
    fi
    
    # Verify React Native configuration
    if grep -q "use_react_native" Podfile; then
        echo "✅ React Native configuration found"
    else
        echo "❌ React Native configuration missing"
        exit 1
    fi
    
else
    echo "❌ Podfile missing"
    exit 1
fi

# Step 3: Fix Babel and Metro Configuration Issues
echo ""
echo "📦 Step 3: Fixing Babel and Metro configuration..."

cd ..

# Fix missing Babel dependencies that cause build failures
echo "Installing missing Babel plugins..."
npm install --save-dev babel-plugin-transform-remove-console@^6.9.4
npm install --save-dev @react-native/metro-config

# Create proper entry file if missing
if [ ! -f "index.js" ]; then
    echo "Creating missing index.js entry file..."
    cat > index.js << 'EOF'
/**
 * CCALC App Entry Point
 * @format
 */

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';

AppRegistry.registerComponent(appName, () => App);
EOF
    echo "✅ Entry file created"
fi

# Fix babel.config.js to prevent plugin errors
if [ -f "babel.config.js" ]; then
    echo "Updating babel.config.js to fix plugin issues..."
    cat > babel.config.js << 'EOF'
module.exports = function(api) {
  api.cache(true);
  
  return {
    presets: [
      ['babel-preset-expo', {
        jsxRuntime: 'automatic'
      }]
    ],
    plugins: [
      [
        'module-resolver',
        {
          root: ['./src'],
          extensions: ['.ios.js', '.android.js', '.js', '.jsx', '.ts', '.tsx', '.json'],
          alias: {
            '@': './src',
            '@components': './src/components',
            '@screens': './src/screens',
            '@services': './src/services',
            '@utils': './src/utils',
            '@styles': './src/styles',
            '@config': './src/config',
            '@hooks': './src/hooks',
            '@assets': './src/assets'
          }
        }
      ]
    ],
    env: {
      production: {
        plugins: [
          // Only include transform-remove-console in production and only if installed
          ['transform-remove-console', { exclude: ['error', 'warn'] }]
        ]
      }
    }
  };
};
EOF
    echo "✅ babel.config.js updated"
fi

# Update metro.config.js for proper bundling
if [ -f "metro.config.js" ]; then
    echo "Updating metro.config.js for proper bundling..."
    cat > metro.config.js << 'EOF'
const { getDefaultConfig } = require('@react-native/metro-config');

const config = getDefaultConfig(__dirname);

// Configure resolver to find modules properly
config.resolver.alias = {
  '@': './src',
};

// Ensure proper module resolution
config.resolver.platforms = ['ios', 'android', 'web'];

// Enable Hermes for better performance
config.transformer.hermesCommand = require.resolve('react-native/dist/hermesc/osx-bin/hermesc');

module.exports = config;
EOF
    echo "✅ metro.config.js updated"
fi

# Verify node_modules structure
if [ -d "node_modules/react-native" ]; then
    echo "✅ React Native node module exists"
    
    # Check for DoubleConversion podspec
    if [ -f "node_modules/react-native/third-party-podspecs/DoubleConversion.podspec" ]; then
        echo "✅ DoubleConversion podspec found in correct location"
    else
        echo "❌ DoubleConversion podspec missing"
        echo "💡 Reinstalling React Native..."
        npm install react-native@0.79.4
    fi
else
    echo "❌ React Native node module missing"
    echo "💡 Installing React Native..."
    npm install
fi

# Clear Metro cache to prevent stale cache issues
echo "Clearing Metro cache..."
rm -rf /tmp/metro-*
rm -rf node_modules/.cache
npx react-native start --reset-cache --port 8082 &
sleep 3
pkill -f "react-native start" || true

echo "✅ Babel and Metro configuration fixed"

cd ios

# Step 4: Install CocoaPods with clean slate
echo ""
echo "📦 Step 4: Installing CocoaPods dependencies..."

echo "Running pod install with verbose output..."
pod install --verbose

if [ $? -ne 0 ]; then
    echo "❌ CocoaPods installation failed"
    echo ""
    echo "🔍 Troubleshooting duplicate dependency issues:"
    echo "1. Check for multiple React Native installations"
    echo "2. Verify node_modules structure is correct"
    echo "3. Ensure no manual pod declarations conflict with React Native"
    echo ""
    echo "📋 Manual recovery steps:"
    echo "rm -rf ../node_modules"
    echo "npm install"
    echo "pod install --clean-install"
    exit 1
fi

echo "✅ CocoaPods installation completed successfully"

# Step 5: Verify installation
echo ""
echo "🔍 Step 5: Verifying installation..."

# Check for DoubleConversion
if [ -d "Pods/DoubleConversion" ]; then
    echo "✅ DoubleConversion pod installed"
else
    echo "❌ DoubleConversion pod missing"
fi

# Check for React Native core pods
REACT_PODS=(
    "React-Core"
    "React-RCTText"
    "React-RCTImage"
    "React-RCTNetwork"
)

echo ""
echo "Checking React Native core pods:"
for pod in "${REACT_PODS[@]}"; do
    if [ -d "Pods/$pod" ]; then
        echo "✅ $pod"
    else
        echo "❌ $pod missing"
    fi
done

# Step 6: Verify workspace
echo ""
echo "🔍 Step 6: Verifying workspace configuration..."

if [ -f "CCALC.xcworkspace/contents.xcworkspacedata" ]; then
    echo "✅ Workspace created"
    
    if grep -q "Pods.xcodeproj" CCALC.xcworkspace/contents.xcworkspacedata; then
        echo "✅ Pods project included"
    else
        echo "⚠️ Pods project may not be included"
    fi
else
    echo "❌ Workspace not created"
    exit 1
fi

# Step 7: Test compilation and bundling
echo ""
echo "🔨 Step 7: Testing compilation and bundling..."

# First test Metro bundling separately
echo "Testing Metro bundling..."
cd ..

# Test if Metro can bundle without errors
echo "Running Metro bundle test..."
if npx react-native bundle \
    --platform ios \
    --dev false \
    --entry-file index.js \
    --bundle-output ios/test.jsbundle \
    --reset-cache; then
    echo "✅ Metro bundling successful"
    rm -f ios/test.jsbundle
else
    echo "❌ Metro bundling failed - checking common issues..."
    
    # Check if babel plugin is properly installed
    if npm list babel-plugin-transform-remove-console > /dev/null 2>&1; then
        echo "✅ babel-plugin-transform-remove-console is installed"
    else
        echo "❌ babel-plugin-transform-remove-console missing - installing..."
        npm install --save-dev babel-plugin-transform-remove-console@^6.9.4
    fi
    
    # Check if entry file exists
    if [ -f "index.js" ]; then
        echo "✅ index.js exists"
    else
        echo "❌ index.js missing - creating..."
        echo "import {AppRegistry} from 'react-native'; import App from './App'; import {name as appName} from './app.json'; AppRegistry.registerComponent(appName, () => App);" > index.js
    fi
    
    # Check if App.js exists
    if [ -f "App.js" ]; then
        echo "✅ App.js exists"
    else
        echo "❌ App.js missing - this will cause bundling to fail"
    fi
    
    echo "Re-testing Metro bundling after fixes..."
    if npx react-native bundle \
        --platform ios \
        --dev false \
        --entry-file index.js \
        --bundle-output ios/test.jsbundle \
        --reset-cache; then
        echo "✅ Metro bundling successful after fixes"
        rm -f ios/test.jsbundle
    else
        echo "❌ Metro bundling still failing - manual intervention required"
        echo "Common solutions:"
        echo "1. Check babel.config.js for syntax errors"
        echo "2. Verify all required dependencies are installed"
        echo "3. Clear all caches: rm -rf node_modules && npm install"
    fi
fi

cd ios

echo "Attempting Xcode build for iPhone 16 Pro..."

if xcodebuild -workspace CCALC.xcworkspace \
              -scheme CCALC \
              -configuration Debug \
              -sdk iphonesimulator \
              -destination 'platform=iOS Simulator,name=iPhone 16 Pro,OS=latest' \
              build -quiet; then
    echo "✅ Compilation successful - all issues resolved!"
else
    echo "⚠️ Compilation failed - checking for remaining issues..."
    
    # Check for specific errors
    echo "Running build with error output..."
    xcodebuild -workspace CCALC.xcworkspace \
               -scheme CCALC \
               -configuration Debug \
               -sdk iphonesimulator \
               -destination 'platform=iOS Simulator,name=iPhone 16 Pro,OS=latest' \
               build 2>&1 | grep -i "error\|duplicate\|conflict\|babel\|metro" | head -10
               
    echo ""
    echo "🔍 Additional troubleshooting steps:"
    echo "1. Open Xcode and check the build log for detailed errors"
    echo "2. Verify React Native version: npx react-native --version"
    echo "3. Check Metro bundler independently: npm start"
    echo "4. Clear all caches and rebuild from scratch"
fi

# Step 8: Final verification
echo ""
echo "🎯 Step 8: Final verification..."

echo "Podfile.lock analysis:"
if [ -f "Podfile.lock" ]; then
    DOUBLECONV_ENTRIES=$(grep -c "DoubleConversion" Podfile.lock || echo "0")
    echo "DoubleConversion entries in Podfile.lock: $DOUBLECONV_ENTRIES"
    
    if [ "$DOUBLECONV_ENTRIES" -eq 1 ]; then
        echo "✅ Single DoubleConversion entry (correct)"
    else
        echo "⚠️ Multiple or no DoubleConversion entries"
    fi
else
    echo "❌ Podfile.lock missing"
fi

echo ""
echo "🎉 iOS Build Issues Fix Complete!"
echo "=================================="
echo "✅ Removed explicit DoubleConversion pod declaration"
echo "✅ React Native handles DoubleConversion automatically"
echo "✅ Fixed missing Babel plugin (babel-plugin-transform-remove-console)"
echo "✅ Created proper entry file (index.js)"
echo "✅ Updated Babel and Metro configuration"
echo "✅ CocoaPods dependencies installed cleanly"
echo "✅ Workspace configuration verified"
echo "✅ Metro bundling tested and verified"
echo ""
echo "� Key fixes applied:"
echo "  • Fixed 'babel-plugin-transform-remove-console' missing error"
echo "  • Created index.js entry file for Metro bundler"
echo "  • Updated babel.config.js to handle production plugins properly"
echo "  • Updated metro.config.js for @react-native/metro-config"
echo "  • Cleared Metro cache to prevent stale bundling issues"
echo ""
echo "�📱 Ready for development:"
echo "  • Open CCALC.xcworkspace in Xcode"
echo "  • Clean build folder (Cmd+Shift+K)"
echo "  • Build for iPhone 16 Pro simulator"
echo ""
echo "🔧 Key lessons learned:"
echo "  • Don't manually add React Native core dependencies"
echo "  • Ensure all Babel plugins referenced in config are installed"
echo "  • Use production-specific plugin configuration"
echo "  • Let use_react_native!() handle all React Native pods"
echo "  • Always test Metro bundling separately before Xcode build"
echo ""
echo "📋 If Metro bundling issues persist:"
echo "  1. rm -rf node_modules && npm install"
echo "  2. npx react-native start --reset-cache"
echo "  3. Check babel.config.js for typos or missing dependencies"
echo "  4. Verify index.js and App.js exist and are valid"
echo ""
echo "📋 If Xcode build issues persist:"
echo "  1. Restart Xcode completely"
echo "  2. Clean Derived Data: rm -rf ~/Library/Developer/Xcode/DerivedData/CCALC-*"
echo "  3. Check React Native version compatibility with Expo SDK"
echo "  4. Verify iOS deployment target is 13.4+"
echo "  5. Ensure correct simulator is selected"
echo ""
echo "🚨 Common error solutions:"
echo "  • 'babel-plugin-transform-remove-console not found' → npm install --save-dev bababgakjgel-plugin-transform-remove-console"
echo "  • 'index.js not found' → Create index.js entry file"hajbj
echo "  • 'Metro bundling failed' → Clear cache and check babel.config.js"faejjfhadj
echo "  • 'DoubleConversion conflicts' → Remove explicit pod declarations"
