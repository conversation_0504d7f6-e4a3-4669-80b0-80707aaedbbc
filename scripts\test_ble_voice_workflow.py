#!/usr/bin/env python3
"""
End-to-End BLE Device Registration and Voice Call Workflow Test Suite
Tests the complete workflow from BLE device registration to voice call authorization
"""

import asyncio
import requests
import json
import time
import sys
from typing import Dict, Any, Optional
from bleak import BleakScanner, BleakClient

# Configuration
BACKEND_BASE_URL = "http://localhost:3000"
ADMIN_USERNAME = "admin"  # Update with actual admin credentials
ADMIN_PASSWORD = "admin123"  # Update with actual admin credentials
TEST_USER_USERNAME = "testuser"  # User to test with
CUSTOM_SERVICE_UUID = "0000c0de-0000-1000-8000-00805f9b34fb"
CUSTOM_CHAR_UUID = "0000beef-0000-1000-8000-00805f9b34fb"

class BleVoiceWorkflowTester:
    def __init__(self):
        self.admin_token = None
        self.user_token = None
        self.test_user_id = None
        self.registered_device_id = None
        self.test_results = []

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": time.time()
        })

    async def setup_admin_auth(self) -> bool:
        """Authenticate as admin"""
        try:
            response = requests.post(f"{BACKEND_BASE_URL}/api/admin/auth/login", json={
                "username": ADMIN_USERNAME,
                "password": ADMIN_PASSWORD
            })
            
            if response.status_code == 200:
                data = response.json()
                self.admin_token = data.get("token")
                self.log_test("Admin Authentication", True, f"Token: {self.admin_token[:20]}...")
                return True
            else:
                self.log_test("Admin Authentication", False, f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Admin Authentication", False, f"Error: {str(e)}")
            return False

    async def setup_test_user(self) -> bool:
        """Create or verify test user exists"""
        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            
            # Check if user exists
            response = requests.get(f"{BACKEND_BASE_URL}/api/admin/users?search={TEST_USER_USERNAME}", headers=headers)
            
            if response.status_code == 200:
                users = response.json().get("users", [])
                test_user = next((u for u in users if u["username"] == TEST_USER_USERNAME), None)
                
                if test_user:
                    self.test_user_id = test_user["_id"]
                    self.log_test("Test User Verification", True, f"User ID: {self.test_user_id}")
                    return True
                else:
                    # Create test user
                    create_response = requests.post(f"{BACKEND_BASE_URL}/api/admin/users", 
                        headers=headers,
                        json={
                            "username": TEST_USER_USERNAME,
                            "displayName": "Test User",
                            "expression": "2+2",
                            "expressionType": "calculator"
                        }
                    )
                    
                    if create_response.status_code == 201:
                        user_data = create_response.json()
                        self.test_user_id = user_data["user"]["_id"]
                        self.log_test("Test User Creation", True, f"User ID: {self.test_user_id}")
                        return True
                    else:
                        self.log_test("Test User Creation", False, f"Status: {create_response.status_code}")
                        return False
            else:
                self.log_test("Test User Verification", False, f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Test User Setup", False, f"Error: {str(e)}")
            return False

    async def test_ble_device_scan(self) -> Optional[Dict[str, Any]]:
        """Test BLE device scanning"""
        try:
            print("🔍 Scanning for BLE devices...")
            devices = await BleakScanner.discover(timeout=5.0)
            
            if devices:
                # Use the first available device for testing
                test_device = devices[0]
                device_info = {
                    "address": test_device.address,
                    "name": test_device.name or f"TestDevice-{test_device.address[-8:]}",
                    "rssi": getattr(test_device, 'rssi', -50)
                }
                
                self.log_test("BLE Device Scan", True, f"Found {len(devices)} devices, using {device_info['name']}")
                return device_info
            else:
                self.log_test("BLE Device Scan", False, "No BLE devices found")
                return None
                
        except Exception as e:
            self.log_test("BLE Device Scan", False, f"Error: {str(e)}")
            return None

    async def test_ble_device_registration(self, device_info: Dict[str, Any]) -> bool:
        """Test BLE device registration via API"""
        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            
            registration_data = {
                "deviceId": device_info["address"],
                "deviceName": device_info["name"],
                "deviceType": "earbud",
                "rssi": device_info["rssi"],
                "registeredBy": "script",
                "scriptVersion": "test-2.0",
                "notes": "Registered via end-to-end test suite"
            }
            
            response = requests.post(
                f"{BACKEND_BASE_URL}/api/admin/users/{self.test_user_id}/ble-devices",
                headers=headers,
                json=registration_data
            )
            
            if response.status_code == 201:
                device_data = response.json()
                self.registered_device_id = device_data["device"]["_id"]
                self.log_test("BLE Device Registration", True, f"Device ID: {self.registered_device_id}")
                return True
            else:
                self.log_test("BLE Device Registration", False, f"Status: {response.status_code}, Response: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("BLE Device Registration", False, f"Error: {str(e)}")
            return False

    async def test_admin_panel_ble_view(self) -> bool:
        """Test viewing BLE devices in admin panel"""
        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            
            response = requests.get(
                f"{BACKEND_BASE_URL}/api/admin/users/{self.test_user_id}/ble-devices",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                devices = data.get("devices", [])
                
                if devices and any(d["_id"] == self.registered_device_id for d in devices):
                    self.log_test("Admin Panel BLE View", True, f"Found {len(devices)} devices")
                    return True
                else:
                    self.log_test("Admin Panel BLE View", False, "Registered device not found in admin panel")
                    return False
            else:
                self.log_test("Admin Panel BLE View", False, f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Admin Panel BLE View", False, f"Error: {str(e)}")
            return False

    async def test_ble_challenge_generation(self) -> Optional[str]:
        """Test BLE authentication challenge generation"""
        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            
            response = requests.post(
                f"{BACKEND_BASE_URL}/api/admin/ble-devices/{self.registered_device_id}/challenge",
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                challenge = data.get("challenge")
                self.log_test("BLE Challenge Generation", True, f"Challenge: {challenge[:20]}...")
                return challenge
            else:
                self.log_test("BLE Challenge Generation", False, f"Status: {response.status_code}")
                return None
                
        except Exception as e:
            self.log_test("BLE Challenge Generation", False, f"Error: {str(e)}")
            return None

    async def test_user_authentication(self) -> bool:
        """Test user authentication for voice calls"""
        try:
            # Simulate user login (would normally be done through mobile app)
            response = requests.post(f"{BACKEND_BASE_URL}/api/auth/login", json={
                "username": TEST_USER_USERNAME,
                "expression": "4",  # Answer to 2+2
                "deviceFingerprint": "test-device-fingerprint"
            })
            
            if response.status_code == 200:
                data = response.json()
                self.user_token = data.get("token")
                self.log_test("User Authentication", True, f"Token: {self.user_token[:20]}...")
                return True
            else:
                self.log_test("User Authentication", False, f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("User Authentication", False, f"Error: {str(e)}")
            return False

    async def test_voice_call_ble_requirement(self) -> bool:
        """Test voice call BLE requirement check"""
        try:
            headers = {"Authorization": f"Bearer {self.user_token}"}
            
            response = requests.get(f"{BACKEND_BASE_URL}/api/voice/ble-requirement", headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                requires_ble = data.get("requiresBLE", False)
                user_role = data.get("userRole", "unknown")
                
                self.log_test("Voice Call BLE Requirement", True, f"Requires BLE: {requires_ble}, Role: {user_role}")
                return True
            else:
                self.log_test("Voice Call BLE Requirement", False, f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Voice Call BLE Requirement", False, f"Error: {str(e)}")
            return False

    async def test_voice_call_initiation(self, challenge: str) -> bool:
        """Test voice call initiation with BLE verification"""
        try:
            headers = {"Authorization": f"Bearer {self.user_token}"}
            
            # Simulate BLE challenge response (in real scenario, this would come from BLE device)
            import hashlib
            # The real implementation would read this from the BLE device's characteristic
            challenge_response = hashlib.sha256(f"{challenge}-{device_info['address']}".encode()).hexdigest()
            
            response = requests.post(f"{BACKEND_BASE_URL}/api/voice/start-call", 
                headers=headers,
                json={
                    "callType": "mobile_to_superuser",
                    "bleDeviceId": self.registered_device_id,
                    "bleChallenge": challenge_response
                }
            )
            
            # Note: This might fail due to BLE verification, which is expected in test environment
            if response.status_code in [200, 403]:  # 403 is expected for invalid challenge
                data = response.json()
                if response.status_code == 403 and data.get("requiresBLE"):
                    self.log_test("Voice Call BLE Verification", True, "BLE verification correctly required")
                    return True
                elif response.status_code == 200:
                    self.log_test("Voice Call Initiation", True, "Call initiated successfully")
                    return True
                else:
                    self.log_test("Voice Call Initiation", False, f"Unexpected response: {data}")
                    return False
            else:
                self.log_test("Voice Call Initiation", False, f"Status: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Voice Call Initiation", False, f"Error: {str(e)}")
            return False

    async def cleanup_test_data(self) -> bool:
        """Clean up test data"""
        try:
            headers = {"Authorization": f"Bearer {self.admin_token}"}
            
            # Delete test BLE device
            if self.registered_device_id:
                requests.delete(
                    f"{BACKEND_BASE_URL}/api/admin/ble-devices/{self.registered_device_id}?permanent=true",
                    headers=headers
                )
            
            # Optionally delete test user (commented out to preserve for multiple test runs)
            # if self.test_user_id:
            #     requests.delete(f"{BACKEND_BASE_URL}/api/admin/users/{self.test_user_id}", headers=headers)
            
            self.log_test("Test Data Cleanup", True, "Cleanup completed")
            return True
            
        except Exception as e:
            self.log_test("Test Data Cleanup", False, f"Error: {str(e)}")
            return False

    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "="*60)
        print("🧪 BLE VOICE WORKFLOW TEST SUMMARY")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['details']}")
        
        print("\n✅ Test completed!")

    async def run_full_test_suite(self):
        """Run the complete end-to-end test suite"""
        print("🚀 Starting BLE Voice Workflow End-to-End Test Suite")
        print("="*60)
        
        # Phase 1: Setup
        if not await self.setup_admin_auth():
            return
        
        if not await self.setup_test_user():
            return
        
        # Phase 2: BLE Device Registration
        device_info = await self.test_ble_device_scan()
        if not device_info:
            print("⚠️  Skipping BLE tests - no devices found")
            device_info = {
                "address": "00:11:22:33:44:55",
                "name": "MockTestDevice",
                "rssi": -50
            }
        
        if not await self.test_ble_device_registration(device_info):
            return
        
        # Phase 3: Admin Panel Integration
        await self.test_admin_panel_ble_view()
        
        # Phase 4: Voice Call Integration
        challenge = await self.test_ble_challenge_generation()
        
        if await self.test_user_authentication():
            await self.test_voice_call_ble_requirement()
            
            if challenge:
                await self.test_voice_call_initiation(challenge)
        
        # Phase 5: Cleanup
        await self.cleanup_test_data()
        
        # Print summary
        self.print_summary()

async def main():
    """Main test runner"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("BLE Voice Workflow Test Suite")
        print("Usage: python test_ble_voice_workflow.py")
        print("\nThis script tests the complete BLE device registration and voice call workflow.")
        print("Make sure the backend server is running on http://localhost:3000")
        return
    
    tester = BleVoiceWorkflowTester()
    await tester.run_full_test_suite()

if __name__ == "__main__":
    asyncio.run(main())
